<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.hqins.agent</groupId>
    <artifactId>hqins-agent-org</artifactId>
    <version>1.1.9-RELEASE</version>
    <packaging>pom</packaging>

    <modules>
        <module>hqins-agent-org-api</module>
        <module>hqins-agent-org-provider</module>
    </modules>

    <properties>
        <hqins-common.version>1.0.75-RELEASE</hqins-common.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <maven-source-plugin.verion>3.2.1</maven-source-plugin.verion>
        <maven-surefire-plugin.verion>3.0.0-M5</maven-surefire-plugin.verion>
        <flatten-maven-plugin.version>1.1.0</flatten-maven-plugin.version>
        <spring-boot.version>2.2.13.RELEASE</spring-boot.version>
        <spring-boot-maven-plugin.version>2.5.6</spring-boot-maven-plugin.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.hqins.common</groupId>
                <artifactId>hqins-common-dependencies</artifactId>
                <version>${hqins-common.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.hqins.common</groupId>
                <artifactId>hqins-common-bom</artifactId>
                <version>${hqins-common.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>1.5.20</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-api</artifactId>
                <version>2.0.3</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>nexus snapshots</name>
            <url>https://repository.e-hqins.com/repository/maven-snapshots/</url>
        </snapshotRepository>
        <repository>
            <id>nexus-releases</id>
            <name>nexus releases</name>
            <url>https://repository.e-hqins.com/repository/maven-releases/</url>
        </repository>
    </distributionManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>${maven-source-plugin.verion}</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <goals>
                                <goal>jar-no-fork</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.verion}</version>
                    <configuration>
                        <skip>true</skip>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

</project>
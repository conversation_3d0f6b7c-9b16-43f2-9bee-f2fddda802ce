<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hqins.agent.org.dao.mapper.exms.TbsaleteamMapper">

    <resultMap id="Base_Result_Map" type="com.hqins.agent.org.dao.entity.exms.Tbsaleteam">
        <result property="saleteamincode" column="saleteamincode"/>
        <result property="teamlevel" column="teamlevel"/>
        <result property="instCode" column="INST_CODE"/>
        <result property="instName" column="INST_NAME"/>
        <result property="companycode" column="companycode"/>
        <result property="empincode" column="empincode"/>
        <result property="empinname" column="empinname"/>
        <result property="empindate" column="empindate"/>
        <result property="eucempincode" column="eucempincode"/>
        <result property="saleteamcode" column="saleteamcode"/>
        <result property="saleteamname" column="saleteamname"/>
        <result property="saleteamshortname" column="saleteamshortname"/>
        <result property="supersaleteamcode" column="supersaleteamcode"/>
        <result property="saleteamlogoimagejson" column="saleteamlogoimagejson"/>
        <result property="founddate" column="founddate"/>
        <result property="wrokprov" column="wrokprov"/>
        <result property="wrokprovname" column="wrokprovname"/>
        <result property="wrokcity" column="wrokcity"/>
        <result property="wrokcityname" column="wrokcityname"/>
        <result property="wrokregion" column="wrokregion"/>
        <result property="wrokregionname" column="wrokregionname"/>
        <result property="workaddress" column="workaddress"/>
        <result property="workplacezip" column="workplacezip"/>
        <result property="workplacetelejson" column="workplacetelejson"/>
        <result property="workplacefaxjson" column="workplacefaxjson"/>
        <result property="saleteamtype" column="saleteamtype"/>
        <result property="effreason" column="effreason"/>
        <result property="expreason" column="expreason"/>
        <result property="canceldate" column="canceldate"/>
        <result property="cancelreason" column="cancelreason"/>
        <result property="effedate" column="effedate"/>
        <result property="expdate" column="expdate"/>
        <result property="saleteamstatus" column="saleteamstatus"/>
        <result property="type" column="type"/>
        <result property="isworflow" column="isworflow"/>
        <result property="systemstatus" column="systemstatus"/>
        <result property="createuser" column="createuser"/>
        <result property="createname" column="createname"/>
        <result property="modifyname" column="modifyname"/>
        <result property="createtime" column="createtime"/>
        <result property="modifyuser" column="modifyuser"/>
        <result property="modifytime" column="modifytime"/>
        <result property="cptype" column="CPTYPE"/>
        <result property="branchattr" column="BRANCHATTR"/>
        <result property="branchtype" column="BRANCHTYPE"/>
        <result property="managerorgcode" column="MANAGERORGCODE"/>
        <result property="eucsaleteamincode" column="eucsaleteamincode"/>
    </resultMap>

    <sql id="Base_Column_List">
        `saleteamincode`,`teamlevel`,`INST_CODE`,`INST_NAME`,`companycode`,`empincode`,`empinname`,`empindate`,`eucempincode`,`saleteamcode`,`saleteamname`,`saleteamshortname`,`supersaleteamcode`,`saleteamlogoimagejson`,`founddate`,`wrokprov`,`wrokprovname`,`wrokcity`,`wrokcityname`,`wrokregion`,`wrokregionname`,`workaddress`,`workplacezip`,`workplacetelejson`,`workplacefaxjson`,`saleteamtype`,`effreason`,`expreason`,`canceldate`,`cancelreason`,`effedate`,`expdate`,`saleteamstatus`,`type`,`isworflow`,`systemstatus`,`createuser`,`createname`,`modifyname`,`createtime`,`modifyuser`,`modifytime`,`CPTYPE`,`BRANCHATTR`,`BRANCHTYPE`,`MANAGERORGCODE`,`eucsaleteamincode`
    </sql>

    <select id="selectByTeamStatus" parameterType="java.lang.String" resultType="com.hqins.agent.org.dao.entity.exms.Tbsaleteam">
        select *  from tbsaleteam where saleteamstatus = #{saleteamstatus}
    </select>

    <!-- saleteamstatus = '00' 代表该团队为有效状态  -->
    <select id="selectBySaleTeamCode" parameterType="java.lang.String" resultType="com.hqins.agent.org.dao.entity.exms.Tbsaleteam">
        select *  from tbsaleteam where saleteamcode = #{saleTeamCode} and saleteamstatus = '00'
    </select>

</mapper>

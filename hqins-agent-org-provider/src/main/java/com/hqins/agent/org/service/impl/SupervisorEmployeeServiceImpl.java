package com.hqins.agent.org.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.constants.AppConsts;
import com.hqins.agent.org.dao.converter.PartnerConverter;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.dao.entity.iips.Tbepartner;
import com.hqins.agent.org.dao.entity.org.ChannelEmployee;
import com.hqins.agent.org.dao.entity.org.SupervisorEmployee;
import com.hqins.agent.org.dao.mapper.exms.TbempMapper;
import com.hqins.agent.org.dao.mapper.org.ChannelEmployeeMapper;
import com.hqins.agent.org.dao.mapper.org.SupervisorEmployeeMapper;
import com.hqins.agent.org.model.enums.DataType;
import com.hqins.agent.org.model.enums.EmployeeStatus;
import com.hqins.agent.org.model.enums.SupervisorType;
import com.hqins.agent.org.model.request.*;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.rpc.client.UmClient;
import com.hqins.agent.org.service.*;
import com.hqins.agent.org.utils.PasswordUtil;
import com.hqins.agent.sales.api.DigitalPersionApi;
import com.hqins.agent.sales.model.request.DigitalPlanSyncEmployeeRequest;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.enums.Gender;
import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.errors.BadRequestException;
import com.hqins.common.base.errors.ErrorCode;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.base.utils.AssertUtil;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.JsonUtil;
import com.hqins.common.utils.PageUtil;
import com.hqins.common.web.RequestContextHolder;
import com.hqins.um.model.dto.AccountInfoDTO;
import com.hqins.um.model.dto.AgentStateDTO;
import com.hqins.um.model.request.AgentCreateByAdminRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @Date 2023/4/3 17:25
 */
@Service
@Slf4j
public class SupervisorEmployeeServiceImpl extends ServiceImpl<SupervisorEmployeeMapper, SupervisorEmployee> implements SupervisorEmployeeService {
    @Autowired
    SupervisorEmployeeMapper supervisorEmployeeMapper;
    @Resource
    private UmClient umClient;
    @Resource
    private CacheService cacheService;
    @Resource
    private PartnerOrgService partnerOrgService;
    @Resource
    private PartnerService partnerService;
    @Resource
    @Lazy
    private PartnerEmployeeService partnerEmployeeService;

    final private TbempMapper tbempMapper;
    final private ChannelEmployeeMapper channelEmployeeMapper;
    final private SensorsService sensorsService;

    private final DigitalPersionApi digitalPersionApi;

    public SupervisorEmployeeServiceImpl(TbempMapper tbempMapper, ChannelEmployeeMapper channelEmployeeMapper, SensorsService sensorsService, DigitalPersionApi digitalPersionApi) {
        this.tbempMapper = tbempMapper;
        this.channelEmployeeMapper = channelEmployeeMapper;
        this.sensorsService = sensorsService;
        this.digitalPersionApi = digitalPersionApi;
    }

    @Override
    public PageInfo<SupervisorEmployeeVO> listMy(SupervisorEmployeeRequest request) {
        Page<SupervisorEmployee> page = this.page(new Page<>(request.getCurrent(), request.getSize()), Wrappers.lambdaQuery(SupervisorEmployee.class)
                .eq(StringUtils.isNotBlank(request.getTopCode()), SupervisorEmployee::getTopCode, request.getTopCode())
                .eq(StringUtils.isNotBlank(request.getOrgCode()), SupervisorEmployee::getOrgCode, request.getOrgCode())
                .eq(StringUtils.isNotBlank(request.getEmployeeCode()), SupervisorEmployee::getEmployeeCode, request.getEmployeeCode())
                .like(StringUtils.isNotBlank(request.getName()), SupervisorEmployee::getName, request.getName())
                .eq( null != request.getRoleType() , SupervisorEmployee::getRoleType, request.getRoleType())
                .eq(StringUtils.isNotBlank(request.getMobile()), SupervisorEmployee::getMobile, request.getMobile())
                .orderByDesc(SupervisorEmployee::getUpdateTime)
        );
        return  PageUtil.convert(page, supervisorEmployee ->{
            SupervisorEmployeeVO t = BeanCopier.copyObject(supervisorEmployee, SupervisorEmployeeVO.class);
            t.setOrgList(JsonUtil.toList(supervisorEmployee.getOrgCodeList(), List.class, String.class));
            t.setTopList(JsonUtil.toList(supervisorEmployee.getTopCodeList(), List.class, String.class));
            return t;
        });
    }

    @Override
    public List<SupervisorEmployeeVO> selectSupervisorEmployeeList(SupervisorEmployeeRequest request) {
        String orgCode = request.getOrgCode();
        String topCode = "";
        SupervisorType roleType = request.getRoleType();
        if (SupervisorType.ZongBu.name().equals(roleType.name())){
            topCode = orgCode;
            orgCode = "";
        }
        List<SupervisorEmployee> supervisorEmployeeList = supervisorEmployeeMapper.selectList(new LambdaQueryWrapper<SupervisorEmployee>()
                .like(StringUtils.isNotBlank(orgCode), SupervisorEmployee::getOrgCodeList, orgCode)
                .like(StringUtils.isNotBlank(topCode), SupervisorEmployee::getTopCodeList, topCode)
                .eq(StringUtils.isNotBlank(request.getEmployeeCode()), SupervisorEmployee::getEmployeeCode, request.getEmployeeCode())
                .eq( null != request.getRoleType() , SupervisorEmployee::getRoleType, request.getRoleType())
                .eq(StringUtils.isNotBlank(request.getMobile()), SupervisorEmployee::getMobile, request.getMobile())
                .eq(SupervisorEmployee::getStatus,"SERVING")
        );
        if (CollectionUtils.isEmpty(supervisorEmployeeList)){
            return new ArrayList<>(0);
        }
        return BeanCopier.copyList(supervisorEmployeeList, SupervisorEmployeeVO.class);
    }

    @Override
    public List<SupervisorEmployeeVO> selectSupervisorEmployeeListPost(SelectSupervisorEmployeeListPostRequest request) {
        String orgCode = request.getOrgCode();
        String topCode = request.getTopCode();
        SupervisorType roleType = request.getRoleType();
        List<SupervisorEmployee> supervisorEmployeeList = supervisorEmployeeMapper.selectList(new LambdaQueryWrapper<SupervisorEmployee>()
                .like(StringUtils.isNotBlank(orgCode), SupervisorEmployee::getOrgCodeList, orgCode)
                .like(StringUtils.isNotBlank(topCode), SupervisorEmployee::getTopCodeList, topCode)
                .eq(StringUtils.isNotBlank(request.getEmployeeCode()), SupervisorEmployee::getEmployeeCode, request.getEmployeeCode())
                .eq(StringUtils.isNotBlank(request.getTeamCode()), SupervisorEmployee::getTeamCode, request.getTeamCode())
                .eq( null != request.getRoleType() , SupervisorEmployee::getRoleType, request.getRoleType())
                .eq(StringUtils.isNotBlank(request.getMobile()), SupervisorEmployee::getMobile, request.getMobile())
                .eq(SupervisorEmployee::getStatus,"SERVING"));

        if (CollectionUtils.isEmpty(supervisorEmployeeList)){
            return new ArrayList<>(0);
        }
        return BeanCopier.copyList(supervisorEmployeeList, SupervisorEmployeeVO.class);
    }

    @Override
    public EmployeeVO getSupervisorEmployee(String employeeCode) {
        SupervisorEmployee employee = supervisorEmployeeMapper.selectOne(new LambdaQueryWrapper<SupervisorEmployee>()
                .eq(SupervisorEmployee::getEmployeeCode, employeeCode)
                .last("limit 1") );
        AssertUtil.isTrue(employee != null, new ApiException(ErrorCode.BAD_REQUEST, "销售员不存在"));
        EmployeeVO vo = new EmployeeVO();
        vo.setCode(employee.getEmployeeCode());
        vo.setName(employee.getName());
        vo.setIdCode(employee.getIdCode());
        vo.setIdType(employee.getIdType());
        vo.setSupervisorRoleType(employee.getRoleType());
        vo.setGender(Gender.get(employee.getGender()));
        vo.setOrgType(AgentOrgType.PARTNER);
        vo.setBirthday(employee.getBirthday());
        vo.setMobile(employee.getMobile());
        vo.setTopCode(employee.getTopCode());
        vo.setTopName(employee.getTopCodeName());
        vo.setOrgCode(employee.getOrgCode());
        vo.setOrgName(employee.getOrgName());
        vo.setTeamName(employee.getTeamName());
        vo.setTeamCode(employee.getTeamCode());
        vo.setTeamLevel(employee.getTeamLevel());
        vo.setStatus(EmployeeStatus.get(employee.getStatus()));
        vo.setRankName(employee.getPosition());
        return vo;
    }

    @Override
    public EmployeeVO getSupervisorEmployeeByMobile(String mobile) {
        SupervisorEmployee employee = supervisorEmployeeMapper.selectOne(new LambdaQueryWrapper<SupervisorEmployee>()
                .eq(SupervisorEmployee::getMobile, mobile)
                .eq(SupervisorEmployee::getStatus,EmployeeStatus.SERVING)
                .last("limit 1") );
        AssertUtil.isTrue(employee != null, new ApiException(ErrorCode.BAD_REQUEST, "销售员不存在"));
        EmployeeVO vo = new EmployeeVO();
        vo.setCode(employee.getEmployeeCode());
        vo.setName(employee.getName());
        vo.setIdCode(employee.getIdCode());
        vo.setIdType(employee.getIdType());
        vo.setSupervisorRoleType(employee.getRoleType());
        vo.setGender(Gender.get(employee.getGender()));
        vo.setOrgType(AgentOrgType.PARTNER);
        vo.setBirthday(employee.getBirthday());
        vo.setMobile(employee.getMobile());
        vo.setTopCode(employee.getTopCode());
        vo.setTopName(employee.getTopCodeName());
        vo.setOrgCode(employee.getOrgCode());
        vo.setOrgName(employee.getOrgName());
        vo.setStatus(EmployeeStatus.get(employee.getStatus()));
        return vo;
    }

    @Override
    public void checkAndSave(SupervisorEmployeeAddRequest request) {
        log.info("checkAndSave ::: request{},StaffId{},StaffUsername{},",JsonUtil.toJSON(request),RequestContextHolder.getStaffId(),RequestContextHolder.getStaffUsername());
        Map<String, Object> checkRequest = checkRequest(request.getMobile(),request.getIdCode());
        AssertUtil.isTrue(checkRequest.get("success").equals(Boolean.TRUE), new ApiException(checkRequest.get("message").toString()));

        SupervisorEmployee supervisorEmployee = generateEmployeeCode(request);
        try {
            //更新之前先跨服务先创建账号。
            AgentCreateByAdminRequest req = new AgentCreateByAdminRequest();
            req.setOrgType(AgentOrgType.PARTNER.name());
            req.setEmployeeCode(supervisorEmployee.getEmployeeCode());
            req.setState(AppConsts.VALID);
            req.setPhone(supervisorEmployee.getMobile());
            req.setPassword(PasswordUtil.generatePwd(supervisorEmployee.getIdCode()));
            req.setStaffId(RequestContextHolder.getStaffId());
            req.setName(supervisorEmployee.getName());

            Long umId = umClient.createAgentUser(req);
            supervisorEmployee.setStatus(EmployeeStatus.SERVING.name());

            this.updateById(supervisorEmployee);

            //神策埋点
            /*if (null != umId) {
                sensorsService.addSupervisorEmployee(supervisorEmployee, umId.toString());
            }*/
            //扩展功能
            extend(supervisorEmployee);
        }catch (Exception e){
            this.removeById(supervisorEmployee);
            AssertUtil.isTrue(Boolean.FALSE, new ApiException(e.getMessage()));
        }
    }

    private void extend(SupervisorEmployee supervisorEmployee){
        DigitalPlanSyncEmployeeRequest digitalPlanSyncEmployeeRequest = new DigitalPlanSyncEmployeeRequest();
        digitalPlanSyncEmployeeRequest.setEmployeeCode(supervisorEmployee.getEmployeeCode());
        if (EmployeeStatus.SERVING.name().equals(supervisorEmployee.getStatus())){
            digitalPlanSyncEmployeeRequest.setStatus("01");
        } else if (EmployeeStatus.LEAVING.name().equals(supervisorEmployee.getStatus())){
            digitalPlanSyncEmployeeRequest.setStatus("04");
        }else{
            return;
        }
        try{
            digitalPersionApi.syncEmployee(digitalPlanSyncEmployeeRequest);
        }catch (Exception e){
            log.error("数字人同步代理人异常,digitalPlanSyncEmployeeRequest:{}",digitalPlanSyncEmployeeRequest,e);
        }
    }

    @Override
    public void leave(Long id) {
        SupervisorEmployee supervisorEmployee = this.getById(id);

        if (EmployeeStatus.SERVING.name().equals(supervisorEmployee.getStatus())) {
            supervisorEmployee.setStatus(EmployeeStatus.LEAVING.name());
            supervisorEmployee.setUpdateTime(LocalDateTime.now());
            supervisorEmployee.setModifier(RequestContextHolder.getStaffUsername());
            supervisorEmployee.setModifierId(RequestContextHolder.getStaffId());

            List<AccountInfoDTO> accountInfoDTOList = umClient.getAgentsBatch(supervisorEmployee.getEmployeeCode());
            if (CollectionUtils.isNotEmpty(accountInfoDTOList)) {
                //跨服务把账号变成失效。成功才能保存
                AgentStateDTO agentStateDTO = new AgentStateDTO();
                agentStateDTO.setState(AppConsts.QUIT);
                agentStateDTO.setStaffId(RequestContextHolder.getStaffId());
                agentStateDTO.setUserId(accountInfoDTOList.get(0).getUserId());
                umClient.updateEmployeeStateByAdmin(agentStateDTO);
            }
            this.updateById(supervisorEmployee);
            extend(supervisorEmployee);
          }
    }

    @Override
    public void reentry(Long id) {

        SupervisorEmployee supervisorEmployee = this.getById(id);

        if (EmployeeStatus.LEAVING.name().equals(supervisorEmployee.getStatus())) {
            //1.查询销售员的团队状态停用状态不允许重入职
            supervisorEmployee.setStatus(EmployeeStatus.SERVING.name());
            supervisorEmployee.setUpdateTime(LocalDateTime.now());
            supervisorEmployee.setModifier(RequestContextHolder.getStaffUsername());
            supervisorEmployee.setModifierId(RequestContextHolder.getStaffId());

            //人员复用重新创建账号
            AgentCreateByAdminRequest request = new AgentCreateByAdminRequest();
            request.setOrgType(AgentOrgType.PARTNER.name());
            request.setEmployeeCode(supervisorEmployee.getEmployeeCode());
            request.setState(AppConsts.VALID);
            request.setPhone(supervisorEmployee.getMobile());
            request.setPassword(PasswordUtil.generatePwd(supervisorEmployee.getIdCode()));
            request.setStaffId(RequestContextHolder.getStaffId());
            request.setName(supervisorEmployee.getName());
            Long umId = umClient.createAgentUser(request);
            //神策埋点
            if (null != umId) {
                sensorsService.addSupervisorEmployee(supervisorEmployee, umId.toString());
            }
            this.updateById(supervisorEmployee);
            extend(supervisorEmployee);
        }
    }

    @Override
    public void updateInformation(SupervisorEmployeeUpdateRequest request) {
        //根据id查询信息
        SupervisorEmployee supervisorEmployee = this.getOne(Wrappers.lambdaQuery(SupervisorEmployee.class).eq(SupervisorEmployee::getId, request.getId()));
        //判断手机号和证件号是否一致  不一致需要校验
        if (!supervisorEmployee.getIdCode().equals(request.getIdCode())){
            Map<String, Object> checkRequest = checkRequest(request.getMobile(), request.getIdCode(),"2");
            AssertUtil.isTrue(checkRequest.get("success").equals(Boolean.TRUE), new BadRequestException(checkRequest.get("message").toString()));

        }
        if (!supervisorEmployee.getMobile().equals(request.getMobile())){
            Map<String, Object> checkRequest = checkRequest(request.getMobile(), request.getIdCode(),"1");
            AssertUtil.isTrue(checkRequest.get("success").equals(Boolean.TRUE), new BadRequestException(checkRequest.get("message").toString()));
            //是否修改手机号，修改手机号需要跨服务更新手机号，成功才能继续
            List<AccountInfoDTO> accountInfoDTOList = umClient.getAgentsBatch(supervisorEmployee.getEmployeeCode());
            if (!CollectionUtils.isEmpty(accountInfoDTOList)) {
                //跨服务把账号变成失效。成功才能保存
                umClient.agentUserPhoneUpdate(accountInfoDTOList.get(0).getUserId(), supervisorEmployee.getMobile(), request.getMobile());
            }
        }

        supervisorEmployeeMapper.updateByPrimaryKeySelective(SupervisorEmployee.builder()
                .id(request.getId())
                .name(request.getName())
                .idType(request.getIdType())
                .idCode(request.getIdCode())
                .roleType(request.getRoleType().name())
                .gender(request.getGender().name())
                .birthday(request.getBirthday())
                .mobile(request.getMobile())
                .topCode(request.getTopCode())
                .topCodeName(request.getTopCodeName())
                .orgCode(request.getOrgCode())
                .orgName(request.getOrgName())
                .teamName(request.getTeamName())
                .teamLevel(request.getTeamLevel())
                .teamCode(request.getTeamCode())
                .position(request.getPosition())
                .orgCodeList(JsonUtil.toJSON(request.getOrgCodeList()))
                .topCodeList(JsonUtil.toJSON(request.getTopCodeList()))
                .modifier(RequestContextHolder.getStaffUsername())
                .modifierId(RequestContextHolder.getStaffId())
                .updateTime(LocalDateTime.now())
                .build());
    }

    @Override
    public void conversions(SupervisorEmployeeOrgUpdateRequest request) {
        Long id = this.getOne(Wrappers.lambdaQuery(SupervisorEmployee.class).eq(SupervisorEmployee::getEmployeeCode, request.getEmployeeCode())).getId();
        supervisorEmployeeMapper.updateByPrimaryKeySelective(SupervisorEmployee.builder()
                .id(id)
                .topCode(request.getTopCode())
                .topCodeName(request.getTopCodeName())
                .orgCode(request.getOrgCode())
                .orgName(request.getOrgName())
                .teamCode(request.getTeamCode())
                .teamName(request.getTeamName())
                .teamLevel(request.getTeamLevel())
                .modifier(RequestContextHolder.getStaffUsername())
                .modifierId(RequestContextHolder.getStaffId())
                .updateTime(LocalDateTime.now())
                .build());
    }

    @Override
    public PageInfo<AccountVO> listMyAccountVO(PartnerEmployeeRequest queryRequest) {
        Page<SupervisorEmployee> supervisorEmployeePage = this.page(new Page<>(queryRequest.getCurrent(), queryRequest.getSize()),
                Wrappers.lambdaQuery(SupervisorEmployee.class)
                        .like(StringUtils.isNotEmpty(queryRequest.getPartnerName()), SupervisorEmployee::getTopCodeName, queryRequest.getPartnerName())
                        .like(StringUtils.isNotEmpty(queryRequest.getOrgName()), SupervisorEmployee::getOrgName, queryRequest.getOrgName())
                        .like(StringUtils.isNotEmpty(queryRequest.getName()), SupervisorEmployee::getName, queryRequest.getName())
                        .eq(StringUtils.isNotEmpty(queryRequest.getCode()), SupervisorEmployee::getEmployeeCode, queryRequest.getCode())
                        .eq(StringUtils.isNotEmpty(queryRequest.getMobile()), SupervisorEmployee::getMobile, queryRequest.getMobile())
        );
        return PageUtil.convert(supervisorEmployeePage , SupervisorEmployeeServiceImpl::supervisorEmployeeToAccountVO );
    }

    @Override
    public Map<String, List<String>> getCodeList() {
        String employeeCode = RequestContextHolder.getEmployeeCode();
        if (StringUtils.isEmpty(employeeCode)){
            return null;
        }
        Map<String, List<String>> result = new HashMap<>();
        SupervisorEmployee supervisorEmployee = supervisorEmployeeMapper.selectOne(new LambdaQueryWrapper<SupervisorEmployee>()
                .eq(SupervisorEmployee::getEmployeeCode, employeeCode)
                .eq(SupervisorEmployee::getStatus, EmployeeStatus.SERVING)
                .last("limit 1"));
        if (null == supervisorEmployee){
            return null;
        }
        if (supervisorEmployee.getRoleType().equals(SupervisorType.ZongBu.name())){
            result.put("TopCodeList",JsonUtil.toList(supervisorEmployee.getTopCodeList(), List.class,String.class));
        }
        if (supervisorEmployee.getRoleType().equals(SupervisorType.JiGou.name())){
            result.put("TopCodeList",JsonUtil.toList(supervisorEmployee.getTopCodeList(), List.class,String.class));
            result.put("OrgCodeList",JsonUtil.toList(supervisorEmployee.getOrgCodeList(), List.class, String.class));
        }

        return result;
    }

    @Override
    public List<SimpleNodeVO> getTop() {
        TopQueryRequest queryRequest = TopQueryRequest.builder()
                .current(1).size(9999).build();

        List<SimpleNodeVO> result = new ArrayList<>();
        Map<String, List<String>> codeList = getCodeList();
        if (codeList != null ){
            List<String> list = codeList.get("TopCodeList");
            if (CollectionUtils.isNotEmpty(list)){
                Set<String> set = new HashSet<>(list);
                Page<Tbepartner> tbepartnerPage = cacheService.selectTbepartnerPage(AppConsts.CPTYPE_PARTNER, queryRequest, set);
                return PageUtil.convert(tbepartnerPage, PartnerConverter::tbepartnerToSimpleNodeVO).getRecords();
            }
        }
        return result;
    }

    @Override
    public List<TreeNodeVO> getOrg(OrgQueryRequest queryRequest) {

        Map<String, List<String>> codeList = getCodeList();
        List<TreeNodeVO> treeNodeVOS = new ArrayList<>();
        if (codeList != null ){
            List<String> list = codeList.get("OrgCodeList");
            if (CollectionUtils.isNotEmpty(list)){
                Set<String> set = new HashSet<>(list);
                Page<BaseInst> baseInstPage = cacheService.selectBaseInstPage(AppConsts.CPTYPE_PARTNER, queryRequest, set);
                treeNodeVOS = BeanCopier.copyList(baseInstPage.getRecords(), PartnerConverter::instToTreeNodeVo);
                for (TreeNodeVO vo : treeNodeVOS) {
                    vo.setDataType(DataType.PARTNER_ORG);
                }
            }
        }
        if (CollectionUtils.isEmpty(treeNodeVOS)){
            RequestContextHolder.setAdminAppIdLocal(1L);
            RequestContextHolder.setStaffIdLocal(1L);
            treeNodeVOS = partnerOrgService.listMySimple(queryRequest);
            RequestContextHolder.setAdminAppIdLocal(null);
            RequestContextHolder.setStaffIdLocal(null);

            if (!ObjectUtils.isEmpty(treeNodeVOS)){
                List<TreeNodeVO> children = treeNodeVOS.get(0).getChildren();
                if (!CollectionUtils.isEmpty(children)){
                    if (!ObjectUtils.isEmpty(codeList) && !CollectionUtils.isEmpty(codeList.get("TopCodeList"))){
                        treeNodeVOS = children;
                    } else {
                        List<String> partnerCodeList = treeNodeVOS.stream().map(TreeNodeVO::getCode).collect(Collectors.toList());
                        children = children.stream().filter(child -> !partnerCodeList.contains(child.getCode())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(children)) {
                            treeNodeVOS.addAll(children);
                        }
                    }
                }
            }
        }

        return treeNodeVOS;
    }

    public static AccountVO supervisorEmployeeToAccountVO(SupervisorEmployee t) {
        AccountVO vo = new AccountVO();
        vo.setCode(t.getEmployeeCode());
        vo.setName(t.getName());
        vo.setMobile(t.getMobile());
        vo.setTopCode(t.getTopCode());
        vo.setTopName(t.getTopCodeName());
        if (EmployeeStatus.SERVING.name().equals(t.getStatus())) {
            vo.setEmployeeStatus(EmployeeStatus.SERVING);
        } else {
            vo.setEmployeeStatus(EmployeeStatus.LEAVING);
        }
        return vo;
    }

    public SupervisorEmployee generateEmployeeCode(SupervisorEmployeeAddRequest request) {
        //人员：S000000001
        SupervisorEmployee supervisorEmployee = SupervisorEmployee.builder()
                .employeeCode(System.currentTimeMillis() + "")
                .name(request.getName())
                .idType(request.getIdType())
                .idCode(request.getIdCode())
                .roleType(request.getRoleType().name())
                .gender(request.getGender().name())
                .birthday(request.getBirthday())
                .mobile(request.getMobile())
                .topCode(request.getTopCode())
                .topCodeName(request.getTopCodeName())
                .orgName(request.getOrgName())
                .orgCode(request.getOrgCode())
                .teamCode(request.getTeamCode())
                .teamLevel(request.getTeamLevel())
                .teamName(request.getTeamName())
                .status(EmployeeStatus.LEAVING.name())
                .orgCodeList(JsonUtil.toJSON(request.getOrgCodeList()))
                .topCodeList(JsonUtil.toJSON(request.getTopCodeList()))
                .position(request.getPosition())
                .creator(RequestContextHolder.getStaffUsername())
                .creatorId(RequestContextHolder.getStaffId().toString())
                .createTime(LocalDateTime.now())
                .build();

        log.info("添加进入的数据:{}", JsonUtil.toJSON(supervisorEmployee));
        this.save(supervisorEmployee);
        String employeeCode =  "S" + String.format("%09d", supervisorEmployee.getId());
        supervisorEmployee.setEmployeeCode(employeeCode);
        return supervisorEmployee;
    }
    private Map<String, Object> checkRequest(String mobile,String idCode) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("success", true);
        resultMap.put("message", "200");
        ChannelEmployee channelEmployeeCheckMobile = channelEmployeeMapper.selectOne(Wrappers.lambdaQuery(ChannelEmployee.class)
                .eq(ChannelEmployee::getMobile, mobile)
                .eq(ChannelEmployee::getStatus,EmployeeStatus.SERVING.name()));
        if (null != channelEmployeeCheckMobile){
            resultMap.put("success", false);
            resultMap.put("message", "手机号已存在");
            return resultMap;
        }
        SupervisorEmployee supervisorEmployeeCheckMobile = this.getOne(Wrappers.lambdaQuery(SupervisorEmployee.class)
                .eq(SupervisorEmployee::getMobile, mobile)
                .eq(SupervisorEmployee::getStatus,EmployeeStatus.SERVING.name()));
        if (null != supervisorEmployeeCheckMobile){
            resultMap.put("success", false);
            resultMap.put("message", "手机号已存在");
            return resultMap;
        }
        String tbempCheckPhone = tbempMapper.queryPhone(mobile);
        if (StringUtils.isNotBlank(tbempCheckPhone)){
            resultMap.put("success", false);
            resultMap.put("message", "手机号已存在");
            return resultMap;
        }

        if (StringUtils.isNotEmpty(idCode)){
            SupervisorEmployee supervisorEmployeeCheckId = this.getOne(Wrappers.lambdaQuery(SupervisorEmployee.class)
                    .eq(SupervisorEmployee::getIdCode,idCode)
                    .eq(SupervisorEmployee::getStatus,EmployeeStatus.SERVING.name()));
            if (null != supervisorEmployeeCheckId){
                resultMap.put("success", false);
                resultMap.put("message", "证件号已存在");
                return resultMap;
            }

            ChannelEmployee channelEmployeeCheckId = channelEmployeeMapper.selectOne(Wrappers.lambdaQuery(ChannelEmployee.class)
                    .eq(ChannelEmployee::getIdCode, idCode)
                    .eq(ChannelEmployee::getStatus,EmployeeStatus.SERVING.name()));
            if (null != channelEmployeeCheckId){
                resultMap.put("success", false);
                resultMap.put("message", "证件号已存在");
                return resultMap;
            }
            String tbempCheckId = tbempMapper.queryIdCode(idCode);
            if (StringUtils.isNotEmpty(tbempCheckId)){
                resultMap.put("success", false);
                resultMap.put("message", "证件号已存在");
                return resultMap;
            }
        }
        return resultMap;
    }
    private Map<String, Object> checkRequest(String mobile,String idCode,String check) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("success", true);
        resultMap.put("message", "200");
        if ("1".equals(check)){
            ChannelEmployee channelEmployeeCheckMobile = channelEmployeeMapper.selectOne(Wrappers.lambdaQuery(ChannelEmployee.class)
                    .eq(ChannelEmployee::getMobile, mobile)
                    .eq(ChannelEmployee::getStatus,EmployeeStatus.SERVING.name()));
            if (null != channelEmployeeCheckMobile){
                resultMap.put("success", false);
                resultMap.put("message", "手机号已存在");
                return resultMap;
            }
            int supervisorEmployeeCheckMobile = this.count(Wrappers.lambdaQuery(SupervisorEmployee.class)
                    .eq(SupervisorEmployee::getMobile, mobile)
                    .eq(SupervisorEmployee::getStatus,EmployeeStatus.SERVING.name()));
            if (supervisorEmployeeCheckMobile >= 1){
                resultMap.put("success", false);
                resultMap.put("message", "手机号已存在");
                return resultMap;
            }
            String tbempCheckPhone = tbempMapper.queryPhone(mobile);
            if (StringUtils.isNotBlank(tbempCheckPhone)){
                resultMap.put("success", false);
                resultMap.put("message", "手机号已存在");
                return resultMap;
            }
        }
        if ("2".equals(check)){
            int supervisorEmployeeCheckId = this.count(Wrappers.lambdaQuery(SupervisorEmployee.class)
                    .eq(StringUtils.isNotBlank(idCode),SupervisorEmployee::getIdCode,idCode)
                    .eq(SupervisorEmployee::getStatus,EmployeeStatus.SERVING.name()));
            if (supervisorEmployeeCheckId >= 1){
                resultMap.put("success", false);
                resultMap.put("message", "证件号已存在");
                return resultMap;
            }
            ChannelEmployee channelEmployeeCheckId = channelEmployeeMapper.selectOne(Wrappers.lambdaQuery(ChannelEmployee.class)
                    .eq(StringUtils.isNotBlank(idCode),ChannelEmployee::getIdCode, idCode)
                    .eq(StringUtils.isNotBlank(idCode),ChannelEmployee::getStatus,EmployeeStatus.SERVING.name()));
            if (null != channelEmployeeCheckId){
                resultMap.put("success", false);
                resultMap.put("message", "证件号已存在");
                return resultMap;
            }
            if (StringUtils.isNotBlank(idCode)){
                String tbempCheckId = tbempMapper.queryIdCode(idCode);
                if (StringUtils.isNotBlank(tbempCheckId)){
                    resultMap.put("success", false);
                    resultMap.put("message", "证件号已存在");
                    return resultMap;
                }
            }
        }
        return resultMap;
    }


    /**
     * 更新督导账号历史 org  team top  code
     * @param type
     */

    @Override
    public void updateCode(String type) {
        log.info("初始化督导机构接口,开始");
        if (type.equals("update")){
            List<SupervisorEmployee> supervisorEmployeeList = supervisorEmployeeMapper.selectList(new LambdaQueryWrapper<SupervisorEmployee>()
                    .eq(SupervisorEmployee::getStatus, EmployeeStatus.SERVING));
            log.info("初始化督导机构接口,supervisorEmployeeList:{}",supervisorEmployeeList.size());
            //org查询teamCode
            for (SupervisorEmployee supervisorEmployee : supervisorEmployeeList) {
                String orgCode = supervisorEmployee.getOrgCode();
                String topCode = supervisorEmployee.getTopCode();
                log.info("初始化督导机构接口,000,orgCode:{},topCode:{}",orgCode,topCode);
                List<SaleTeamVO> saleTeamVOList = partnerEmployeeService.listSaleTeamByOrgCode(orgCode, null);
                if (CollectionUtils.isEmpty(saleTeamVOList)){
                    log.info("初始化督导机构接口,通过orgCode未查到记录,orgCode:{},topCode:{}",orgCode,topCode);
                    continue;
                }
                SaleTeamVO saleTeamVO = saleTeamVOList.get(0);
                log.info("初始化督导机构接口,1111,orgCode:{},topCode:{},saleTeamVO:{}",orgCode,topCode,JsonUtil.toJSON(saleTeamVO));
                if (supervisorEmployee.getRoleType().equals(SupervisorType.ZongBu.name())
                        && StringUtils.isEmpty(supervisorEmployee.getTopCodeList())){
                    supervisorEmployee.setTopCodeList(JsonUtil.toJSON(Collections.singletonList(topCode)));
                }
                if (supervisorEmployee.getRoleType().equals(SupervisorType.JiGou.name())
                        && StringUtils.isEmpty(supervisorEmployee.getOrgCodeList())){
                    supervisorEmployee.setTopCodeList(JsonUtil.toJSON(Collections.singletonList(topCode)));
                    supervisorEmployee.setOrgCodeList(JsonUtil.toJSON(Collections.singletonList(orgCode)));
                }
                supervisorEmployee.setTeamCode(saleTeamVO.getCode());
                supervisorEmployee.setTeamName(saleTeamVO.getName());
                supervisorEmployee.setTeamLevel(saleTeamVO.getLevel().name());
                log.info("初始化督导机构接口,2222222,supervisorEmployee:{}",JsonUtil.toJSON(supervisorEmployee));
                supervisorEmployeeMapper.updateByPrimaryKeySelective(supervisorEmployee);
            }
        }
        log.info("初始化督导机构接口,结束");
    }

}

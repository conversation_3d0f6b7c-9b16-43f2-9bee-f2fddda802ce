package com.hqins.agent.org.builder;

import cn.hutool.core.collection.CollUtil;
import com.hqins.agent.org.constants.EmployeeLogConstants;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.model.vo.EmployeeLogHierarchyLevelVO;
import com.hqins.agent.org.model.vo.EmployeeLogResultVO;
import com.hqins.agent.org.model.vo.EmployeeVO;

import java.util.ArrayList;
import java.util.List;

/**
 * 员工日志层级结构建造者
 *
 * <AUTHOR> MXH
 * @create 2025/1/15
 */
public class EmployeeLogHierarchyBuilder {

    private String employeeCode;
    private String employeeRole;
    private final List<EmployeeLogHierarchyLevelVO> hierarchyList = new ArrayList<>();

    /**
     * 设置员工基本信息
     *
     * @param code 员工编码
     * @param role 员工角色
     * @return 建造者实例
     */
    public EmployeeLogHierarchyBuilder setEmployee(String code, String role) {
        this.employeeCode = code;
        this.employeeRole = role;
        return this;
    }

    /**
     * 添加层级
     *
     * @param level 层级对象
     * @return 建造者实例
     */
    public EmployeeLogHierarchyBuilder addLevel(EmployeeLogHierarchyLevelVO level) {
        if (level != null) {
            this.hierarchyList.add(level);
        }
        return this;
    }

    /**
     * 添加机构层级
     *
     * @param institution 机构信息
     * @return 建造者实例
     */
    public EmployeeLogHierarchyBuilder addInstitutionLevel(BaseInst institution) {
        if (institution != null) {
            EmployeeLogHierarchyLevelVO level = EmployeeLogHierarchyLevelVO.builder()
                    .code(institution.getInstCode())
                    .name(institution.getInstName())
                    .level(EmployeeLogConstants.LevelNames.INSTITUTION)
                    .build();
            this.hierarchyList.add(level);
        }
        return this;
    }

    /**
     * 添加区级层级
     *
     * @param team 团队信息
     * @return 建造者实例
     */
    public EmployeeLogHierarchyBuilder addRegionLevel(Tbsaleteam team) {
        if (team != null) {
            EmployeeLogHierarchyLevelVO level = EmployeeLogHierarchyLevelVO.builder()
                    .code(team.getSaleteamcode())
                    .name(team.getSaleteamname())
                    .level(EmployeeLogConstants.LevelNames.REGION)
                    .build();
            this.hierarchyList.add(level);
        }
        return this;
    }

    /**
     * 添加部级层级
     *
     * @param team 团队信息
     * @return 建造者实例
     */
    public EmployeeLogHierarchyBuilder addDepartmentLevel(Tbsaleteam team) {
        if (team != null) {
            EmployeeLogHierarchyLevelVO level = EmployeeLogHierarchyLevelVO.builder()
                    .code(team.getSaleteamcode())
                    .name(team.getSaleteamname())
                    .level(EmployeeLogConstants.LevelNames.DEPARTMENT)
                    .build();
            this.hierarchyList.add(level);
        }
        return this;
    }

    /**
     * 添加团队层级
     *
     * @param team 团队信息
     * @return 建造者实例
     */
    public EmployeeLogHierarchyBuilder addTeamLevel(Tbsaleteam team) {
        if (team != null) {
            EmployeeLogHierarchyLevelVO level = EmployeeLogHierarchyLevelVO.builder()
                    .code(team.getSaleteamcode())
                    .name(team.getSaleteamname())
                    .level(EmployeeLogConstants.LevelNames.TEAM)
                    .build();
            this.hierarchyList.add(level);
        }
        return this;
    }

    /**
     * 为最后一个层级添加员工列表
     *
     * @param employees 员工列表
     * @return 建造者实例
     */
    public EmployeeLogHierarchyBuilder addEmployeesToLastLevel(List<EmployeeVO> employees) {
        if (CollUtil.isNotEmpty(employees) && CollUtil.isNotEmpty(hierarchyList)) {
            EmployeeLogHierarchyLevelVO lastLevel = hierarchyList.get(hierarchyList.size() - 1);
            lastLevel.setEmployeeList(employees);
        }
        return this;
    }

    /**
     * 为最后一个层级添加子层级
     *
     * @param subLevels 子层级列表
     * @return 建造者实例
     */
    public EmployeeLogHierarchyBuilder addSubLevelsToLastLevel(List<EmployeeLogHierarchyLevelVO> subLevels) {
        if (CollUtil.isNotEmpty(subLevels) && CollUtil.isNotEmpty(hierarchyList)) {
            EmployeeLogHierarchyLevelVO lastLevel = hierarchyList.get(hierarchyList.size() - 1);
            lastLevel.setSubLevels(subLevels);
        }
        return this;
    }

    /**
     * 创建新的建造者实例
     *
     * @return 新的建造者实例
     */
    public static EmployeeLogHierarchyBuilder newBuilder() {
        return new EmployeeLogHierarchyBuilder();
    }

    /**
     * 构建最终结果
     *
     * @return 员工日志结果对象
     */
    public EmployeeLogResultVO build() {
        if (CollUtil.isEmpty(hierarchyList)) {
            return null;
        }

        return EmployeeLogResultVO.builder()
                .employeeCode(employeeCode)
                .employeeRole(employeeRole)
                .hierarchyList(hierarchyList)
                .build();
    }

    /**
     * 清空构建器状态
     *
     * @return 建造者实例
     */
    public EmployeeLogHierarchyBuilder clear() {
        this.employeeCode = null;
        this.employeeRole = null;
        this.hierarchyList.clear();
        return this;
    }

    /**
     * 克隆建造者
     *
     * @return 新的建造者实例，包含相同的状态
     */
    public EmployeeLogHierarchyBuilder clone() {
        EmployeeLogHierarchyBuilder cloned = new EmployeeLogHierarchyBuilder();
        cloned.employeeCode = this.employeeCode;
        cloned.employeeRole = this.employeeRole;
        // 复制层级列表（深拷贝）
        for (EmployeeLogHierarchyLevelVO level : this.hierarchyList) {
            cloned.hierarchyList.add(level); // 注意：这里是浅拷贝，如果需要深拷贝需要进一步处理
        }
        return cloned;
    }
} 
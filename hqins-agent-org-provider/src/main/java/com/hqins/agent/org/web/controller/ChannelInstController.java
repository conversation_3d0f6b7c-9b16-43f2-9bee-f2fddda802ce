package com.hqins.agent.org.web.controller;

import com.hqins.agent.org.model.request.ChannelInstOutletsQueryRequest;
import com.hqins.agent.org.model.vo.ChannelInstOutletsVO;
import com.hqins.agent.org.model.vo.ChannelTeamTreeNodeVO;
import com.hqins.agent.org.service.ChannelInstService;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.constants.Strings;
import com.hqins.common.base.page.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

/**
 * 渠道商机构管理交互层.
 *
 * <AUTHOR> MXH
 * @create 2025/6/30 9:33
 */
@Api(tags = "公共接口")
@RestController
@RequestMapping("/channel/inst")
@Slf4j
@RequiredArgsConstructor
public class ChannelInstController {

    private final ChannelInstService channelInstService;

    @ApiOperation("分页查询渠道商机构网点")
    @GetMapping("/listInstitutionalOutlets")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<PageInfo<ChannelInstOutletsVO>> listInstitutionalOutlets(
            @ApiParam("机构网点编码") @RequestParam(value = "channelInstCode", required = false) String channelInstCode,
            @ApiParam("机构网点名称") @RequestParam(value = "channelInstName", required = false) String channelInstName,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size) {
        ChannelInstOutletsQueryRequest queryRequest = ChannelInstOutletsQueryRequest.builder()
                .channelInstCode(channelInstCode).channelInstName(channelInstName)
                .current(current).size(size).build();
        queryRequest.correctPageQueryParameters();

        return ApiResult.ok(channelInstService.listInstitutionalOutlets(queryRequest));
    }
}

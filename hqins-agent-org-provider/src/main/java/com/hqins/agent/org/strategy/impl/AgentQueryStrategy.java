package com.hqins.agent.org.strategy.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.hqins.agent.org.builder.EmployeeLogHierarchyBuilder;
import com.hqins.agent.org.constants.EmployeeLogConstants;
import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.model.context.EmployeeLogContext;
import com.hqins.agent.org.model.vo.EmployeeLogHierarchyLevelVO;
import com.hqins.agent.org.model.vo.EmployeeLogResultVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.service.impl.EmployeeLogDataService;
import com.hqins.agent.org.strategy.EmployeeHierarchyStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 代理人查询策略实现
 *
 * <AUTHOR> MXH
 * @create 2025/1/15
 */
@Component("AGENT_QUERY_STRATEGY")
@Slf4j
@RequiredArgsConstructor
public class AgentQueryStrategy implements EmployeeHierarchyStrategy {

    private final EmployeeLogDataService dataService;

    @Override
    public EmployeeLogResultVO buildHierarchy(EmployeeLogContext context, EmployeeLogHierarchyBuilder builder) {
        log.info("开始构建代理人查询层级结构, employeeCode: {}", context.getEmployeeCode());

        Tbemp currentEmployee = context.getCurrentEmployee();
        List<Tbsaleteam> managedTeams = context.getManagedTeams();

        // 如果代理人管理团队
        if (CollUtil.isNotEmpty(managedTeams)) {
            return buildManagerHierarchy(context, builder, managedTeams);
        } else {
            // 如果代理人不管理团队，只查询自己
            return buildSelfHierarchy(context, builder, currentEmployee);
        }
    }

    /**
     * 构建管理者层级结构
     *
     * @param context      执行上下文
     * @param builder      建造者
     * @param managedTeams 管理的团队列表
     * @return 构建结果
     */
    private EmployeeLogResultVO buildManagerHierarchy(EmployeeLogContext context, 
                                                     EmployeeLogHierarchyBuilder builder, 
                                                     List<Tbsaleteam> managedTeams) {
        // 获取最高等级的团队
        Tbsaleteam highestTeam = getHighestLevelTeam(managedTeams);
        if (highestTeam == null) {
            log.warn("未找到有效的管理团队, employeeCode: {}", context.getEmployeeCode());
            return null;
        }

        String teamLevel = highestTeam.getTeamlevel();
        log.info("代理人管理的最高团队等级: {}, employeeCode: {}", teamLevel, context.getEmployeeCode());

        switch (teamLevel) {
            case EmployeeLogConstants.TEAM_LEVEL_GROUP: // 01-营业组
                return buildGroupManagerHierarchy(context, builder, highestTeam);
            case EmployeeLogConstants.TEAM_LEVEL_DEPARTMENT: // 02-营业部
                return buildDepartmentManagerHierarchy(context, builder, highestTeam);
            case EmployeeLogConstants.TEAM_LEVEL_REGION: // 03-营业区
                return buildRegionManagerHierarchy(context, builder, highestTeam);
            default:
                log.warn("未知的团队等级: {}, employeeCode: {}", teamLevel, context.getEmployeeCode());
                return null;
        }
    }

    /**
     * 构建小组主管层级结构
     *
     * @param context     执行上下文
     * @param builder     建造者
     * @param groupTeam   小组团队
     * @return 构建结果
     */
    private EmployeeLogResultVO buildGroupManagerHierarchy(EmployeeLogContext context, 
                                                          EmployeeLogHierarchyBuilder builder, 
                                                          Tbsaleteam groupTeam) {
        // 创建团队层级
        EmployeeLogHierarchyLevelVO teamLevel = EmployeeLogHierarchyLevelVO.builder()
                .code(groupTeam.getSaleteamcode())
                .name(groupTeam.getSaleteamname())
                .level(EmployeeLogConstants.LevelNames.DEPARTMENT)
                .build();

        // 获取团队成员（查阅模式：包含当前员工）
        List<EmployeeVO> employees = dataService.getTeamMembers(
                groupTeam.getSaleteamincode(), context.getEmployeeCode(), true);

        if (CollUtil.isNotEmpty(employees)) {
            teamLevel.setEmployeeList(employees);
            builder.addLevel(teamLevel);
        }

        return builder.build();
    }

    /**
     * 构建部经理层级结构
     *
     * @param context        执行上下文
     * @param builder        建造者
     * @param departmentTeam 部团队
     * @return 构建结果
     */
    private EmployeeLogResultVO buildDepartmentManagerHierarchy(EmployeeLogContext context, 
                                                               EmployeeLogHierarchyBuilder builder, 
                                                               Tbsaleteam departmentTeam) {
        // 创建区级层级
        EmployeeLogHierarchyLevelVO regionLevel = EmployeeLogHierarchyLevelVO.builder()
                .code(departmentTeam.getSaleteamcode())
                .name(departmentTeam.getSaleteamname())
                .level(EmployeeLogConstants.LevelNames.REGION)
                .build();

        // 获取下级团队
        List<Tbsaleteam> subTeams = dataService.getSubTeams(departmentTeam.getSaleteamcode());
        List<EmployeeLogHierarchyLevelVO> subLevels = new ArrayList<>();

        for (Tbsaleteam subTeam : subTeams) {
            EmployeeLogHierarchyLevelVO departmentLevel = EmployeeLogHierarchyLevelVO.builder()
                    .code(subTeam.getSaleteamcode())
                    .name(subTeam.getSaleteamname())
                    .level(EmployeeLogConstants.LevelNames.DEPARTMENT)
                    .build();

            // 获取团队成员（查阅模式：包含当前员工）
            List<EmployeeVO> employees = dataService.getTeamMembers(
                    subTeam.getSaleteamincode(), context.getEmployeeCode(), true);

            if (CollUtil.isNotEmpty(employees)) {
                departmentLevel.setEmployeeList(employees);
                subLevels.add(departmentLevel);
            }
        }

        if (CollUtil.isNotEmpty(subLevels)) {
            regionLevel.setSubLevels(subLevels);
            builder.addLevel(regionLevel);
        }

        return builder.build();
    }

    /**
     * 构建区总监层级结构
     *
     * @param context    执行上下文
     * @param builder    建造者
     * @param regionTeam 区团队
     * @return 构建结果
     */
    private EmployeeLogResultVO buildRegionManagerHierarchy(EmployeeLogContext context, 
                                                           EmployeeLogHierarchyBuilder builder, 
                                                           Tbsaleteam regionTeam) {
        // 获取下级区团队
        List<Tbsaleteam> subRegionTeams = dataService.getSubTeams(regionTeam.getSaleteamcode());
        
        for (Tbsaleteam subRegionTeam : subRegionTeams) {
            // 创建区级层级
            EmployeeLogHierarchyLevelVO regionLevel = EmployeeLogHierarchyLevelVO.builder()
                    .code(subRegionTeam.getSaleteamcode())
                    .name(subRegionTeam.getSaleteamname())
                    .level(EmployeeLogConstants.LevelNames.REGION)
                    .build();

            // 获取区下的部级团队
            List<Tbsaleteam> departmentTeams = dataService.getSubTeams(subRegionTeam.getSaleteamcode());
            List<EmployeeLogHierarchyLevelVO> departmentLevels = new ArrayList<>();

            for (Tbsaleteam departmentTeam : departmentTeams) {
                EmployeeLogHierarchyLevelVO departmentLevel = EmployeeLogHierarchyLevelVO.builder()
                        .code(departmentTeam.getSaleteamcode())
                        .name(departmentTeam.getSaleteamname())
                        .level(EmployeeLogConstants.LevelNames.DEPARTMENT)
                        .build();

                // 获取团队成员（查阅模式：包含当前员工）
                List<EmployeeVO> employees = dataService.getTeamMembers(
                        departmentTeam.getSaleteamincode(), context.getEmployeeCode(), true);

                if (CollUtil.isNotEmpty(employees)) {
                    departmentLevel.setEmployeeList(employees);
                    departmentLevels.add(departmentLevel);
                }
            }

            if (CollUtil.isNotEmpty(departmentLevels)) {
                regionLevel.setSubLevels(departmentLevels);
                builder.addLevel(regionLevel);
            }
        }

        return builder.build();
    }

    /**
     * 构建个人层级结构（非管理者）
     *
     * @param context         执行上下文
     * @param builder         建造者
     * @param currentEmployee 当前员工
     * @return 构建结果
     */
    private EmployeeLogResultVO buildSelfHierarchy(EmployeeLogContext context, 
                                                  EmployeeLogHierarchyBuilder builder, 
                                                  Tbemp currentEmployee) {
        log.info("查询自己的工作日志, 代理人为: {}", context.getEmployeeCode());

        EmployeeLogHierarchyLevelVO selfLevel = EmployeeLogHierarchyLevelVO.builder()
                .code(context.getEmployeeCode())
                .name(currentEmployee.getEmpname())
                .level(EmployeeLogConstants.LevelNames.DEPARTMENT)
                .build();

        builder.addLevel(selfLevel);
        return builder.build();
    }

    /**
     * 获取最高等级的团队
     *
     * @param teamList 团队列表
     * @return 最高等级的团队
     */
    private Tbsaleteam getHighestLevelTeam(List<Tbsaleteam> teamList) {
        if (CollUtil.isEmpty(teamList)) {
            return null;
        }

        return teamList.stream()
                .filter(team -> StrUtil.isNotBlank(team.getTeamlevel()))
                .max((team1, team2) -> team1.getTeamlevel().compareTo(team2.getTeamlevel()))
                .orElse(null);
    }
} 
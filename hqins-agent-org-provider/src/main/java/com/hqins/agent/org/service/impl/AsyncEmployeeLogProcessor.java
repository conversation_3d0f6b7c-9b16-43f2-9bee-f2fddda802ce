package com.hqins.agent.org.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.hqins.agent.org.config.EmployeeLogOptimizationConfig;
import com.hqins.agent.org.constants.EmployeeLogConstants;
import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.model.context.EmployeeLogContext;
import com.hqins.agent.org.model.vo.EmployeeLogHierarchyLevelVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 异步员工日志处理器
 * 提供高性能的并行处理能力
 *
 * <AUTHOR> MXH
 * @create 2025/1/15
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AsyncEmployeeLogProcessor {

    private final BatchEmployeeLogDataService batchDataService;

    /**
     * 异步构建单个机构的查询层级结构
     *
     * @param institutionCode 机构编码
     * @param institution     机构信息
     * @param teams           团队列表
     * @param context         执行上下文
     * @return 异步结果
     */
    @Async("employeeLogExecutor")
    public CompletableFuture<EmployeeLogHierarchyLevelVO> buildInstitutionQueryHierarchyAsync(
            String institutionCode,
            BaseInst institution,
            List<Tbsaleteam> teams,
            EmployeeLogContext context) {

        try {
            log.debug("异步构建机构查询层级: {}", institutionCode);

            // 构建区级子层级
            List<EmployeeLogHierarchyLevelVO> regionLevels = buildRegionLevelsOptimized(teams, context, true);

            // 修复：只有当存在有效的区级层级时才创建并返回机构层级
            if (CollUtil.isNotEmpty(regionLevels)) {
                // 创建机构层级
                EmployeeLogHierarchyLevelVO institutionLevel = EmployeeLogHierarchyLevelVO.builder()
                        .code(institution.getInstCode())
                        .name(institution.getInstName())
                        .level(EmployeeLogConstants.LevelNames.INSTITUTION)
                        .subLevels(regionLevels)
                        .build();

                log.debug("完成机构查询层级构建: {}, 区级数量: {}", institutionCode, regionLevels.size());
                return CompletableFuture.completedFuture(institutionLevel);
            } else {
                log.debug("机构无有效数据，跳过: {}", institutionCode);
                return CompletableFuture.<EmployeeLogHierarchyLevelVO>completedFuture(null);
            }

        } catch (Exception e) {
            log.error("异步构建机构查询层级失败: {}", institutionCode, e);
            return CompletableFuture.<EmployeeLogHierarchyLevelVO>completedFuture(null);
        }
    }

    /**
     * 异步构建单个机构的审核层级结构
     *
     * @param institutionCode 机构编码
     * @param institution     机构信息
     * @param teams           团队列表
     * @param context         执行上下文
     * @return 异步结果
     */
    @Async("employeeLogExecutor")
    public CompletableFuture<EmployeeLogHierarchyLevelVO> buildInstitutionAuditHierarchyAsync(
            String institutionCode,
            BaseInst institution,
            List<Tbsaleteam> teams,
            EmployeeLogContext context) {

        try {
            log.debug("异步构建机构审核层级: {}", institutionCode);

            // 构建审核员工列表
            List<EmployeeVO> auditEmployees = buildInstitutionAuditEmployeesOptimized(teams, context);

            // 修复：只有当存在有效的审核员工时才创建并返回机构层级
            if (CollUtil.isNotEmpty(auditEmployees)) {
                // 创建机构层级
                EmployeeLogHierarchyLevelVO institutionLevel = EmployeeLogHierarchyLevelVO.builder()
                        .code(institution.getInstCode())
                        .name(institution.getInstName())
                        .level(EmployeeLogConstants.LevelNames.INSTITUTION)
                        .employeeList(auditEmployees)
                        .build();

                log.debug("完成机构审核层级构建: {}, 员工数量: {}", institutionCode, auditEmployees.size());
                return CompletableFuture.completedFuture(institutionLevel);
            } else {
                log.debug("机构无有效审核员工，跳过: {}", institutionCode);
                return CompletableFuture.<EmployeeLogHierarchyLevelVO>completedFuture(null);
            }

        } catch (Exception e) {
            log.error("异步构建机构审核层级失败: {}", institutionCode, e);
            return CompletableFuture.<EmployeeLogHierarchyLevelVO>completedFuture(null);
        }
    }

    /**
     * 优化版构建区级层级
     *
     * @param teams           团队列表
     * @param context         执行上下文
     * @param includeCurrentEmployee 是否包含当前员工
     * @return 区级层级列表
     */
    private List<EmployeeLogHierarchyLevelVO> buildRegionLevelsOptimized(List<Tbsaleteam> teams,
                                                                        EmployeeLogContext context,
                                                                        boolean includeCurrentEmployee) {
        if (CollUtil.isEmpty(teams)) {
            return new ArrayList<>();
        }

        List<EmployeeLogHierarchyLevelVO> regionLevels = new ArrayList<>();

        // 修复：筛选部级团队（02级别），与原始版本保持一致
        List<Tbsaleteam> departmentTeams = teams.stream()
                .filter(team -> EmployeeLogConstants.TEAM_LEVEL_DEPARTMENT.equals(team.getTeamlevel()))
                .collect(Collectors.toList());

        // 构建团队层级关系
        Map<String, List<Tbsaleteam>> teamHierarchy = batchDataService.buildTeamHierarchyBatch(teams);

        // 批量查询所有团队的员工信息
        List<String> allTeamCodes = teams.stream()
                .map(Tbsaleteam::getSaleteamincode)
                .collect(Collectors.toList());

        Map<String, List<EmployeeVO>> employeesByTeam = batchDataService.getEmployeeVOsByTeamsBatch(
                allTeamCodes, context.getEmployeeCode(), includeCurrentEmployee);

        for (Tbsaleteam departmentTeam : departmentTeams) {
            EmployeeLogHierarchyLevelVO regionLevel = EmployeeLogHierarchyLevelVO.builder()
                    .code(departmentTeam.getSaleteamcode())
                    .name(departmentTeam.getSaleteamname())
                    .level(EmployeeLogConstants.LevelNames.REGION)
                    .build();

            // 构建部级子层级（实际是组级）
            List<EmployeeLogHierarchyLevelVO> subDepartmentLevels = buildDepartmentLevelsOptimized(
                    departmentTeam, teamHierarchy, employeesByTeam);

            if (CollUtil.isNotEmpty(subDepartmentLevels)) {
                regionLevel.setSubLevels(subDepartmentLevels);
                regionLevels.add(regionLevel);
            }
        }

        return regionLevels;
    }

    /**
     * 优化版构建部级层级
     *
     * @param regionTeam       区级团队
     * @param teamHierarchy    团队层级关系
     * @param employeesByTeam  员工信息映射
     * @return 部级层级列表
     */
    private List<EmployeeLogHierarchyLevelVO> buildDepartmentLevelsOptimized(Tbsaleteam regionTeam,
                                                                           Map<String, List<Tbsaleteam>> teamHierarchy,
                                                                           Map<String, List<EmployeeVO>> employeesByTeam) {
        List<EmployeeLogHierarchyLevelVO> departmentLevels = new ArrayList<>();

        // 获取区下的部级团队
        List<Tbsaleteam> departmentTeams = teamHierarchy.get(regionTeam.getSaleteamcode());
        if (CollUtil.isEmpty(departmentTeams)) {
            return departmentLevels;
        }

        for (Tbsaleteam departmentTeam : departmentTeams) {
            EmployeeLogHierarchyLevelVO departmentLevel = EmployeeLogHierarchyLevelVO.builder()
                    .code(departmentTeam.getSaleteamcode())
                    .name(departmentTeam.getSaleteamname())
                    .level(EmployeeLogConstants.LevelNames.DEPARTMENT)
                    .build();

            // 获取部级团队的员工
            List<EmployeeVO> employees = employeesByTeam.get(departmentTeam.getSaleteamincode());
            if (CollUtil.isNotEmpty(employees)) {
                departmentLevel.setEmployeeList(employees);
                departmentLevels.add(departmentLevel);
            }
        }

        return departmentLevels;
    }

    /**
     * 优化版构建机构审核员工列表
     *
     * @param teams   团队列表
     * @param context 执行上下文
     * @return 审核员工列表
     */
    private List<EmployeeVO> buildInstitutionAuditEmployeesOptimized(List<Tbsaleteam> teams,
                                                                    EmployeeLogContext context) {
        List<EmployeeVO> employees = new ArrayList<>();
        Map<String, Tbemp> allEmployees = context.getAllEmployees();
        String employeeCode = context.getEmployeeCode();

        if (CollUtil.isEmpty(teams)) {
            return employees;
        }

        // 修复：获取顶级团队（与原始版本逻辑一致）
        List<Tbsaleteam> topLevelTeams = teams.stream()
                .filter(team -> StrUtil.isBlank(team.getSupersaleteamcode()) || 
                               team.getSupersaleteamcode().equals(team.getSaleteamcode()) ||
                               EmployeeLogConstants.TEAM_LEVEL_REGION.equals(team.getTeamlevel()))
                .collect(Collectors.toList());

        // 构建团队层级关系
        Map<String, List<Tbsaleteam>> teamHierarchy = batchDataService.buildTeamHierarchyBatch(teams);

        // 批量查询员工信息（使用督导审核专用方法，与原始版本extracted方法保持一致）
        List<String> allTeamCodes = teams.stream()
                .map(Tbsaleteam::getSaleteamincode)
                .collect(Collectors.toList());

        Map<String, List<EmployeeVO>> employeesByTeam = batchDataService.getEmployeeVOsByTeamsBatchForAudit(
                allTeamCodes, employeeCode, false); // 审核模式：排除当前员工

        for (Tbsaleteam topTeam : topLevelTeams) {
            employees.addAll(processHighestLevelTeamOptimized(topTeam, teamHierarchy, employeesByTeam, allEmployees, employeeCode));
        }

        return employees;
    }

    /**
     * 优化版处理最高级别团队
     *
     * @param highestLevelTeam 最高级别团队
     * @param teamHierarchy    团队层级关系
     * @param employeesByTeam  员工信息映射
     * @param allEmployees     所有员工信息
     * @param employeeCode     当前员工编码
     * @return 员工列表
     */
    private List<EmployeeVO> processHighestLevelTeamOptimized(Tbsaleteam highestLevelTeam,
                                                             Map<String, List<Tbsaleteam>> teamHierarchy,
                                                             Map<String, List<EmployeeVO>> employeesByTeam,
                                                             Map<String, Tbemp> allEmployees,
                                                             String employeeCode) {
        List<EmployeeVO> employees = new ArrayList<>();
        String teamLevel = highestLevelTeam.getTeamlevel();

        switch (teamLevel) {
            case EmployeeLogConstants.TEAM_LEVEL_REGION: // 03-营业区
                employees.addAll(processRegionTeamOptimized(highestLevelTeam, teamHierarchy, employeesByTeam, allEmployees, employeeCode));
                break;
            case EmployeeLogConstants.TEAM_LEVEL_DEPARTMENT: // 02-营业部
                employees.addAll(processDepartmentTeamOptimized(highestLevelTeam, teamHierarchy, employeesByTeam, allEmployees, employeeCode));
                break;
            case EmployeeLogConstants.TEAM_LEVEL_GROUP: // 01-营业组
                employees.addAll(processGroupTeamOptimized(highestLevelTeam, employeesByTeam, allEmployees, employeeCode));
                break;
            default:
                log.warn("未知的团队等级: {}, 团队: {}", teamLevel, JSONObject.toJSONString(highestLevelTeam));
                break;
        }

        return employees;
    }

    /**
     * 优化版处理区级团队
     */
    private List<EmployeeVO> processRegionTeamOptimized(Tbsaleteam regionTeam,
                                                       Map<String, List<Tbsaleteam>> teamHierarchy,
                                                       Map<String, List<EmployeeVO>> employeesByTeam,
                                                       Map<String, Tbemp> allEmployees,
                                                       String employeeCode) {
        List<EmployeeVO> employees = new ArrayList<>();

        if (StrUtil.isNotEmpty(regionTeam.getEmpincode())) {
            // 区总监存在
            String empCode = regionTeam.getEmpincode().substring(4);
            Tbemp emp = allEmployees.get(empCode);
            if (emp != null) {
                EmployeeVO employeeVO = EmployeeVO.builder()
                        .code(empCode)
                        .name(emp.getEmpname())
                        .teamLevel(regionTeam.getTeamlevel())
                        .build();
                employees.add(employeeVO);
            }
        } else {
            log.info("顶层团队为区，机构内勤批阅区总监为空，区团队信息为: {}", JSONObject.toJSONString(regionTeam));
            // 查询下级部团队
            employees.addAll(processSubDepartmentTeamsOptimized(regionTeam, teamHierarchy, employeesByTeam, allEmployees, employeeCode));
        }

        return employees;
    }

    /**
     * 优化版处理部级团队
     */
    private List<EmployeeVO> processDepartmentTeamOptimized(Tbsaleteam departmentTeam,
                                                           Map<String, List<Tbsaleteam>> teamHierarchy,
                                                           Map<String, List<EmployeeVO>> employeesByTeam,
                                                           Map<String, Tbemp> allEmployees,
                                                           String employeeCode) {
        List<EmployeeVO> employees = new ArrayList<>();

        if (StrUtil.isNotEmpty(departmentTeam.getEmpincode())) {
            // 部经理存在
            String empCode = departmentTeam.getEmpincode().substring(4);
            Tbemp emp = allEmployees.get(empCode);
            if (emp != null) {
                EmployeeVO employeeVO = EmployeeVO.builder()
                        .code(empCode)
                        .name(emp.getEmpname())
                        .teamLevel(departmentTeam.getTeamlevel())
                        .build();
                employees.add(employeeVO);
            }
        } else {
            log.info("顶层团队为部，机构内勤批阅部经理为空，部团队信息为: {}", JSONObject.toJSONString(departmentTeam));
            // 查询下级团队
            employees.addAll(processSubGroupTeamsOptimized(departmentTeam, teamHierarchy, employeesByTeam, allEmployees, employeeCode));
        }

        return employees;
    }

    /**
     * 优化版处理组级团队
     */
    private List<EmployeeVO> processGroupTeamOptimized(Tbsaleteam groupTeam,
                                                      Map<String, List<EmployeeVO>> employeesByTeam,
                                                      Map<String, Tbemp> allEmployees,
                                                      String employeeCode) {
        List<EmployeeVO> employees = new ArrayList<>();

        if (StrUtil.isNotEmpty(groupTeam.getEmpincode())) {
            String empCode = groupTeam.getEmpincode().substring(4);
            if (!empCode.equals(employeeCode)) {
                // 小组主管不是当前督导
                Tbemp emp = allEmployees.get(empCode);
                if (emp != null) {
                    EmployeeVO employeeVO = EmployeeVO.builder()
                            .code(empCode)
                            .name(emp.getEmpname())
                            .teamLevel(groupTeam.getTeamlevel())
                            .build();
                    employees.add(employeeVO);
                }
            } else {
                // 查询小组人员
                List<EmployeeVO> teamMembers = employeesByTeam.get(groupTeam.getSaleteamincode());
                if (CollUtil.isNotEmpty(teamMembers)) {
                    employees.addAll(teamMembers);
                }
            }
        } else {
            log.info("顶层团队为组，机构内勤批阅小组主管为空，小组团队信息为: {}", JSONObject.toJSONString(groupTeam));
            // 查询小组人员
            List<EmployeeVO> teamMembers = employeesByTeam.get(groupTeam.getSaleteamincode());
            if (CollUtil.isNotEmpty(teamMembers)) {
                employees.addAll(teamMembers);
            }
        }

        return employees;
    }

    /**
     * 优化版处理下级部团队
     */
    private List<EmployeeVO> processSubDepartmentTeamsOptimized(Tbsaleteam regionTeam,
                                                               Map<String, List<Tbsaleteam>> teamHierarchy,
                                                               Map<String, List<EmployeeVO>> employeesByTeam,
                                                               Map<String, Tbemp> allEmployees,
                                                               String employeeCode) {
        List<EmployeeVO> employees = new ArrayList<>();

        List<Tbsaleteam> departmentTeams = teamHierarchy.get(regionTeam.getSaleteamcode());
        if (CollUtil.isEmpty(departmentTeams)) {
            return employees;
        }

        for (Tbsaleteam departmentTeam : departmentTeams) {
            employees.addAll(processDepartmentTeamOptimized(departmentTeam, teamHierarchy, employeesByTeam, allEmployees, employeeCode));
        }

        return employees;
    }

    /**
     * 优化版处理下级组团队
     */
    private List<EmployeeVO> processSubGroupTeamsOptimized(Tbsaleteam departmentTeam,
                                                          Map<String, List<Tbsaleteam>> teamHierarchy,
                                                          Map<String, List<EmployeeVO>> employeesByTeam,
                                                          Map<String, Tbemp> allEmployees,
                                                          String employeeCode) {
        List<EmployeeVO> employees = new ArrayList<>();

        List<Tbsaleteam> groupTeams = teamHierarchy.get(departmentTeam.getSaleteamcode());
        if (CollUtil.isEmpty(groupTeams)) {
            return employees;
        }

        for (Tbsaleteam groupTeam : groupTeams) {
            if (StrUtil.isNotBlank(groupTeam.getEmpincode())) {
                String empCode = groupTeam.getEmpincode().substring(4);
                if (!empCode.equals(employeeCode)) {
                    // 小组主管不是当前督导
                    Tbemp emp = allEmployees.get(empCode);
                    if (emp != null) {
                        EmployeeVO employeeVO = EmployeeVO.builder()
                                .code(empCode)
                                .name(emp.getEmpname())
                                .teamLevel(groupTeam.getTeamlevel())
                                .build();
                        employees.add(employeeVO);
                    }
                } else {
                    // 修复：使用正确的团队编码查询小组成员
                    List<EmployeeVO> teamMembers = employeesByTeam.get(groupTeam.getSaleteamincode());
                    if (CollUtil.isNotEmpty(teamMembers)) {
                        employees.addAll(teamMembers);
                    }
                }
            } else {
                log.info("部下级组团队主管为空，组团队信息为: {}", JSONObject.toJSONString(groupTeam));
                // 修复：使用正确的团队编码查询小组成员
                List<EmployeeVO> teamMembers = employeesByTeam.get(groupTeam.getSaleteamincode());
                if (CollUtil.isNotEmpty(teamMembers)) {
                    employees.addAll(teamMembers);
                }
            }
        }

        return employees;
    }
} 
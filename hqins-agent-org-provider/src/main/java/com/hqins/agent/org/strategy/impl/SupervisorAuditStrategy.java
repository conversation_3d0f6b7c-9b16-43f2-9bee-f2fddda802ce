package com.hqins.agent.org.strategy.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.hqins.agent.org.builder.EmployeeLogHierarchyBuilder;
import com.hqins.agent.org.constants.EmployeeLogConstants;
import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.model.context.EmployeeLogContext;
import com.hqins.agent.org.model.vo.EmployeeLogHierarchyLevelVO;
import com.hqins.agent.org.model.vo.EmployeeLogResultVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.service.impl.EmployeeLogDataService;
import com.hqins.agent.org.strategy.EmployeeHierarchyStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 督导审核策略实现
 *
 * <AUTHOR> MXH
 * @create 2025/1/15
 */
@Component("SUPERVISOR_AUDIT_STRATEGY")
@Slf4j
@RequiredArgsConstructor
public class SupervisorAuditStrategy implements EmployeeHierarchyStrategy {

    private final EmployeeLogDataService dataService;

    @Override
    public EmployeeLogResultVO buildHierarchy(EmployeeLogContext context, EmployeeLogHierarchyBuilder builder) {
        log.info("开始构建督导审核层级结构, employeeCode: {}", context.getEmployeeCode());

        List<String> institutionCodes = context.getInstitutionCodes();
        Map<String, BaseInst> allInstitutions = context.getAllInstitutions();
        Map<String, List<Tbsaleteam>> teamsByInstitution = context.getTeamsByInstitution();

        // 为每个机构构建审核层级结构
        for (String institutionCode : institutionCodes) {
            log.info("机构内勤的机构编码为: {}", institutionCode);

            BaseInst institution = allInstitutions.get(institutionCode);
            if (institution == null) {
                log.warn("机构信息不存在: {}", institutionCode);
                continue;
            }

            // 创建机构层级
            EmployeeLogHierarchyLevelVO institutionLevel = EmployeeLogHierarchyLevelVO.builder()
                    .code(institution.getInstCode())
                    .name(institution.getInstName())
                    .level(EmployeeLogConstants.LevelNames.INSTITUTION)
                    .build();

            // 构建机构下的审核员工列表
            List<EmployeeVO> auditEmployees = buildInstitutionAuditEmployees(
                    teamsByInstitution.get(institutionCode), context);

            if (CollUtil.isNotEmpty(auditEmployees)) {
                institutionLevel.setEmployeeList(auditEmployees);
                builder.addLevel(institutionLevel);
            }
        }

        return builder.build();
    }

    /**
     * 构建机构审核员工列表
     *
     * @param teams   团队列表
     * @param context 执行上下文
     * @return 审核员工列表
     */
    private List<EmployeeVO> buildInstitutionAuditEmployees(List<Tbsaleteam> teams, EmployeeLogContext context) {
        List<EmployeeVO> employees = new ArrayList<>();
        Map<String, Tbemp> allEmployees = context.getAllEmployees();

        if (CollUtil.isEmpty(teams)) {
            return employees;
        }

        // 查询上级团队为空的或者上级团队编码和本身的团队编码相同或者团队等级为03的团队
        List<Tbsaleteam> highestLevelTeams = teams.stream()
                .filter(team -> StrUtil.isBlank(team.getSupersaleteamcode()) || 
                               team.getSupersaleteamcode().equals(team.getSaleteamcode()) || 
                               EmployeeLogConstants.TEAM_LEVEL_REGION.equals(team.getTeamlevel()))
                .collect(java.util.stream.Collectors.toList());

        for (Tbsaleteam highestLevelTeam : highestLevelTeams) {
            employees.addAll(processHighestLevelTeam(highestLevelTeam, context));
        }

        return employees;
    }

    /**
     * 处理最高级别团队
     *
     * @param highestLevelTeam 最高级别团队
     * @param context          执行上下文
     * @return 员工列表
     */
    private List<EmployeeVO> processHighestLevelTeam(Tbsaleteam highestLevelTeam, EmployeeLogContext context) {
        List<EmployeeVO> employees = new ArrayList<>();
        Map<String, Tbemp> allEmployees = context.getAllEmployees();
        String employeeCode = context.getEmployeeCode();

        String teamLevel = highestLevelTeam.getTeamlevel();
        
        switch (teamLevel) {
            case EmployeeLogConstants.TEAM_LEVEL_REGION: // 03-营业区
                employees.addAll(processRegionTeam(highestLevelTeam, context));
                break;
            case EmployeeLogConstants.TEAM_LEVEL_DEPARTMENT: // 02-营业部
                employees.addAll(processDepartmentTeam(highestLevelTeam, context));
                break;
            case EmployeeLogConstants.TEAM_LEVEL_GROUP: // 01-营业组
                employees.addAll(processGroupTeam(highestLevelTeam, context));
                break;
            default:
                log.warn("未知的团队等级: {}, 团队: {}", teamLevel, JSONObject.toJSONString(highestLevelTeam));
                break;
        }

        return employees;
    }

    /**
     * 处理区级团队
     *
     * @param regionTeam 区级团队
     * @param context    执行上下文
     * @return 员工列表
     */
    private List<EmployeeVO> processRegionTeam(Tbsaleteam regionTeam, EmployeeLogContext context) {
        List<EmployeeVO> employees = new ArrayList<>();
        Map<String, Tbemp> allEmployees = context.getAllEmployees();

        if (StrUtil.isNotEmpty(regionTeam.getEmpincode())) {
            // 区总监存在
            EmployeeVO employeeVO = EmployeeVO.builder()
                    .code(regionTeam.getEmpincode().substring(4))
                    .name(allEmployees.get(regionTeam.getEmpincode().substring(4)).getEmpname())
                    .teamLevel(regionTeam.getTeamlevel())
                    .build();
            employees.add(employeeVO);
        } else {
            log.info("顶层团队为区，机构内勤批阅区总监为空，区团队信息为: {}", JSONObject.toJSONString(regionTeam));
            // 查询下级部团队
            employees.addAll(processSubDepartmentTeams(regionTeam, context));
        }

        return employees;
    }

    /**
     * 处理部级团队
     *
     * @param departmentTeam 部级团队
     * @param context        执行上下文
     * @return 员工列表
     */
    private List<EmployeeVO> processDepartmentTeam(Tbsaleteam departmentTeam, EmployeeLogContext context) {
        List<EmployeeVO> employees = new ArrayList<>();
        Map<String, Tbemp> allEmployees = context.getAllEmployees();

        if (StrUtil.isNotEmpty(departmentTeam.getEmpincode())) {
            // 部经理存在
            EmployeeVO employeeVO = EmployeeVO.builder()
                    .code(departmentTeam.getEmpincode().substring(4))
                    .name(allEmployees.get(departmentTeam.getEmpincode().substring(4)).getEmpname())
                    .teamLevel(departmentTeam.getTeamlevel())
                    .build();
            employees.add(employeeVO);
        } else {
            log.info("顶层团队为部，机构内勤批阅部经理为空，部团队信息为: {}", JSONObject.toJSONString(departmentTeam));
            // 查询下级团队
            employees.addAll(processSubGroupTeams(departmentTeam, context));
        }

        return employees;
    }

    /**
     * 处理组级团队
     *
     * @param groupTeam 组级团队
     * @param context   执行上下文
     * @return 员工列表
     */
    private List<EmployeeVO> processGroupTeam(Tbsaleteam groupTeam, EmployeeLogContext context) {
        List<EmployeeVO> employees = new ArrayList<>();
        Map<String, Tbemp> allEmployees = context.getAllEmployees();
        String employeeCode = context.getEmployeeCode();

        if (StrUtil.isNotEmpty(groupTeam.getEmpincode())) {
            if (!groupTeam.getEmpincode().substring(4).equals(employeeCode)) {
                // 小组主管不是当前督导
                EmployeeVO employeeVO = EmployeeVO.builder()
                        .code(groupTeam.getEmpincode().substring(4))
                        .name(allEmployees.get(groupTeam.getEmpincode().substring(4)).getEmpname())
                        .teamLevel(groupTeam.getTeamlevel())
                        .build();
                employees.add(employeeVO);
            } else {
                // 查询小组人员
                employees.addAll(dataService.getTeamMembersForAudit(
                        groupTeam.getSaleteamincode(), employeeCode));
            }
        } else {
            log.info("顶层团队为组，机构内勤批阅小组主管为空，小组团队信息为: {}", JSONObject.toJSONString(groupTeam));
            // 查询小组人员
            employees.addAll(dataService.getTeamMembersForAudit(
                    groupTeam.getSaleteamincode(), employeeCode));
        }

        return employees;
    }

    /**
     * 处理下级部团队
     *
     * @param regionTeam 区团队
     * @param context    执行上下文
     * @return 员工列表
     */
    private List<EmployeeVO> processSubDepartmentTeams(Tbsaleteam regionTeam, EmployeeLogContext context) {
        List<EmployeeVO> employees = new ArrayList<>();
        Map<String, Tbemp> allEmployees = context.getAllEmployees();
        String employeeCode = context.getEmployeeCode();

        List<Tbsaleteam> departmentTeams = dataService.getSubTeams(regionTeam.getSaleteamcode());
        
        for (Tbsaleteam departmentTeam : departmentTeams) {
            if (StrUtil.isNotBlank(departmentTeam.getEmpincode())) {
                // 部经理存在
                EmployeeVO employeeVO = EmployeeVO.builder()
                        .code(departmentTeam.getEmpincode().substring(4))
                        .name(allEmployees.get(departmentTeam.getEmpincode().substring(4)).getEmpname())
                        .teamLevel(departmentTeam.getTeamlevel())
                        .build();
                employees.add(employeeVO);
            } else {
                log.info("顶层团队为区，机构内勤批阅部经理为空，部团队信息为: {}", JSONObject.toJSONString(departmentTeam));
                // 查询下级组团队
                employees.addAll(processSubSubTeams(departmentTeam, context));
            }
        }

        return employees;
    }

    /**
     * 处理下级组团队
     *
     * @param departmentTeam 部团队
     * @param context        执行上下文
     * @return 员工列表
     */
    private List<EmployeeVO> processSubGroupTeams(Tbsaleteam departmentTeam, EmployeeLogContext context) {
        List<EmployeeVO> employees = new ArrayList<>();
        Map<String, Tbemp> allEmployees = context.getAllEmployees();
        String employeeCode = context.getEmployeeCode();

        List<Tbsaleteam> groupTeams = dataService.getSubTeams(departmentTeam.getSaleteamcode());
        
        for (Tbsaleteam groupTeam : groupTeams) {
            if (StrUtil.isNotBlank(groupTeam.getEmpincode())) {
                if (!groupTeam.getEmpincode().substring(4).equals(employeeCode)) {
                    // 小组主管不是当前督导
                    EmployeeVO employeeVO = EmployeeVO.builder()
                            .code(groupTeam.getEmpincode().substring(4))
                            .name(allEmployees.get(groupTeam.getEmpincode().substring(4)).getEmpname())
                            .teamLevel(groupTeam.getTeamlevel())
                            .build();
                    employees.add(employeeVO);
                } else {
                    // 查询小组人员
                    employees.addAll(dataService.getTeamMembersForAudit(
                            groupTeam.getSaleteamincode(), employeeCode));
                }
            } else {
                // 无小组主管，查询小组人员
                employees.addAll(dataService.getTeamMembersForAudit(
                        groupTeam.getSaleteamincode(), employeeCode));
            }
        }

        return employees;
    }

    /**
     * 处理下下级团队（区->部->组的场景）
     *
     * @param departmentTeam 部团队
     * @param context        执行上下文
     * @return 员工列表
     */
    private List<EmployeeVO> processSubSubTeams(Tbsaleteam departmentTeam, EmployeeLogContext context) {
        List<EmployeeVO> employees = new ArrayList<>();
        Map<String, Tbemp> allEmployees = context.getAllEmployees();
        String employeeCode = context.getEmployeeCode();

        List<Tbsaleteam> groupTeams = dataService.getSubTeams(departmentTeam.getSaleteamcode());
        
        for (Tbsaleteam groupTeam : groupTeams) {
            if (StrUtil.isNotBlank(groupTeam.getEmpincode())) {
                if (!groupTeam.getEmpincode().substring(4).equals(employeeCode)) {
                    // 小组主管不是当前督导
                    EmployeeVO employeeVO = EmployeeVO.builder()
                            .code(groupTeam.getEmpincode().substring(4))
                            .name(allEmployees.get(groupTeam.getEmpincode().substring(4)).getEmpname())
                            .teamLevel(groupTeam.getTeamlevel())
                            .build();
                    employees.add(employeeVO);
                } else {
                    // 查询小组人员
                    employees.addAll(dataService.getTeamMembersForAudit(
                            groupTeam.getSaleteamincode(), employeeCode));
                }
            } else {
                // 无小组主管，查询小组人员
                employees.addAll(dataService.getTeamMembersForAudit(
                        groupTeam.getSaleteamincode(), employeeCode));
            }
        }

        return employees;
    }
} 
package com.hqins.agent.org.strategy.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.hqins.agent.org.builder.EmployeeLogHierarchyBuilder;
import com.hqins.agent.org.constants.EmployeeLogConstants;
import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.model.context.EmployeeLogContext;
import com.hqins.agent.org.model.vo.EmployeeLogHierarchyLevelVO;
import com.hqins.agent.org.model.vo.EmployeeLogResultVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.service.impl.EmployeeLogDataService;
import com.hqins.agent.org.strategy.EmployeeHierarchyStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 代理人审核策略实现
 *
 * <AUTHOR> MXH
 * @create 2025/1/15
 */
@Component("AGENT_AUDIT_STRATEGY")
@Slf4j
@RequiredArgsConstructor
public class AgentAuditStrategy implements EmployeeHierarchyStrategy {

    private final EmployeeLogDataService dataService;

    @Override
    public EmployeeLogResultVO buildHierarchy(EmployeeLogContext context, EmployeeLogHierarchyBuilder builder) {
        log.info("开始构建代理人审核层级结构, employeeCode: {}", context.getEmployeeCode());

        Tbemp currentEmployee = context.getCurrentEmployee();
        List<Tbsaleteam> managedTeams = context.getManagedTeams();

        // 如果代理人管理团队
        if (CollUtil.isNotEmpty(managedTeams)) {
            return buildManagerAuditHierarchy(context, builder, managedTeams);
        } else {
            log.info("代理人不管理团队，无审核权限, employeeCode: {}", context.getEmployeeCode());
            return null;
        }
    }

    /**
     * 构建管理者审核层级结构
     *
     * @param context      执行上下文
     * @param builder      建造者
     * @param managedTeams 管理的团队列表
     * @return 构建结果
     */
    private EmployeeLogResultVO buildManagerAuditHierarchy(EmployeeLogContext context, 
                                                          EmployeeLogHierarchyBuilder builder, 
                                                          List<Tbsaleteam> managedTeams) {
        // 获取最高等级的团队
        Tbsaleteam highestTeam = getHighestLevelTeam(managedTeams);
        if (highestTeam == null) {
            log.warn("未找到有效的管理团队, employeeCode: {}", context.getEmployeeCode());
            return null;
        }

        if (StrUtil.isBlank(highestTeam.getTeamlevel())) {
            log.info("团队等级为空, 团队信息为: {}", JSONObject.toJSONString(highestTeam));
            throw new RuntimeException(EmployeeLogConstants.ErrorMessages.TEAM_LEVEL_EMPTY);
        }

        String teamLevel = highestTeam.getTeamlevel();
        log.info("代理人管理的最高团队等级: {}, employeeCode: {}", teamLevel, context.getEmployeeCode());

        switch (teamLevel) {
            case EmployeeLogConstants.TEAM_LEVEL_GROUP: // 01-营业组
                return buildGroupManagerAuditHierarchy(context, builder, highestTeam);
            case EmployeeLogConstants.TEAM_LEVEL_DEPARTMENT: // 02-营业部
                return buildDepartmentManagerAuditHierarchy(context, builder, highestTeam);
            case EmployeeLogConstants.TEAM_LEVEL_REGION: // 03-营业区
                return buildRegionManagerAuditHierarchy(context, builder, highestTeam);
            default:
                log.warn("未知的团队等级: {}, employeeCode: {}", teamLevel, context.getEmployeeCode());
                return null;
        }
    }

    /**
     * 构建小组主管审核层级结构
     *
     * @param context     执行上下文
     * @param builder     建造者
     * @param groupTeam   小组团队
     * @return 构建结果
     */
    private EmployeeLogResultVO buildGroupManagerAuditHierarchy(EmployeeLogContext context, 
                                                               EmployeeLogHierarchyBuilder builder, 
                                                               Tbsaleteam groupTeam) {
        // 小组主管审核组内成员
        List<EmployeeVO> employees = dataService.getTeamMembersForAudit(
                groupTeam.getSaleteamincode(), context.getEmployeeCode());

        if (CollUtil.isNotEmpty(employees)) {
            EmployeeLogHierarchyLevelVO teamLevel = EmployeeLogHierarchyLevelVO.builder()
                    .code(groupTeam.getSaleteamcode())
                    .name(groupTeam.getSaleteamname())
                    .level(EmployeeLogConstants.LevelNames.TEAM)
                    .employeeList(employees)
                    .build();

            builder.addLevel(teamLevel);
        }

        return builder.build();
    }

    /**
     * 构建部经理审核层级结构
     *
     * @param context        执行上下文
     * @param builder        建造者
     * @param departmentTeam 部团队
     * @return 构建结果
     */
    private EmployeeLogResultVO buildDepartmentManagerAuditHierarchy(EmployeeLogContext context, 
                                                                    EmployeeLogHierarchyBuilder builder, 
                                                                    Tbsaleteam departmentTeam) {
        List<EmployeeLogHierarchyLevelVO> teamLevels = new ArrayList<>();

        // 获取下级团队
        List<Tbsaleteam> subTeams = dataService.getSubTeams(departmentTeam.getSaleteamcode());
        
        if (CollUtil.isNotEmpty(subTeams)) {
            for (Tbsaleteam subTeam : subTeams) {
                EmployeeLogHierarchyLevelVO teamLevel = EmployeeLogHierarchyLevelVO.builder()
                        .code(subTeam.getSaleteamcode())
                        .name(subTeam.getSaleteamname())
                        .level(EmployeeLogConstants.LevelNames.TEAM)
                        .build();

                log.info("代理人为部经理，下级团队信息为: {}", JSONObject.toJSONString(subTeam));

                // 处理团队负责人或团队成员
                List<EmployeeVO> employees = buildTeamEmployeesForAudit(context, subTeam, departmentTeam);
                
                if (CollUtil.isNotEmpty(employees)) {
                    teamLevel.setEmployeeList(employees);
                    teamLevels.add(teamLevel);
                }
            }
        }

        // 添加所有团队层级
        for (EmployeeLogHierarchyLevelVO teamLevel : teamLevels) {
            builder.addLevel(teamLevel);
        }

        return builder.build();
    }

    /**
     * 构建区总监审核层级结构
     *
     * @param context    执行上下文
     * @param builder    建造者
     * @param regionTeam 区团队
     * @return 构建结果
     */
    private EmployeeLogResultVO buildRegionManagerAuditHierarchy(EmployeeLogContext context, 
                                                                EmployeeLogHierarchyBuilder builder, 
                                                                Tbsaleteam regionTeam) {
        // 获取下级部团队
        List<Tbsaleteam> departmentTeams = dataService.getSubTeams(regionTeam.getSaleteamcode());
        
        if (CollUtil.isNotEmpty(departmentTeams)) {
            for (Tbsaleteam departmentTeam : departmentTeams) {
                // 创建部层级
                EmployeeLogHierarchyLevelVO departmentLevel = EmployeeLogHierarchyLevelVO.builder()
                        .code(regionTeam.getSaleteamcode())
                        .name(regionTeam.getSaleteamname())
                        .level(EmployeeLogConstants.LevelNames.TEAM)
                        .build();

                log.info("代理人为区总监，下级部团队信息为: {}", JSONObject.toJSONString(departmentTeam));

                // 处理部经理或其下级团队
                List<EmployeeVO> employees = new ArrayList<>();
                if (StrUtil.isNotBlank(departmentTeam.getEmpincode()) && 
                    !departmentTeam.getEmpincode().substring(4).equals(context.getEmployeeCode())) {
                    
                    // 部经理不是当前用户，返回部经理信息
                    Map<String, Tbemp> allEmployees = context.getAllEmployees();
                    Tbemp partManager = allEmployees.get(departmentTeam.getEmpincode().substring(4));
                    
                    EmployeeVO employeeVO = EmployeeVO.builder()
                            .code(partManager.getEmpcode())
                            .name(partManager.getEmpname())
                            .teamLevel(EmployeeLogConstants.TEAM_LEVEL_DEPARTMENT)
                            .build();
                    employees.add(employeeVO);
                    
                    departmentLevel.setEmployeeList(employees);
                } else {
                    // 部经理是当前用户或为空，需要构建下级团队结构
                    List<EmployeeLogHierarchyLevelVO> subLevels = buildSubTeamLevelsForAudit(context, departmentTeam);
                    if (CollUtil.isNotEmpty(subLevels)) {
                        departmentLevel.setSubLevels(subLevels);
                    }
                }

                builder.addLevel(departmentLevel);
            }
        }

        return builder.build();
    }

    /**
     * 构建团队员工列表（审核用）
     *
     * @param context        执行上下文
     * @param subTeam        子团队
     * @param departmentTeam 部门团队
     * @return 员工列表
     */
    private List<EmployeeVO> buildTeamEmployeesForAudit(EmployeeLogContext context, 
                                                       Tbsaleteam subTeam, 
                                                       Tbsaleteam departmentTeam) {
        List<EmployeeVO> employees = new ArrayList<>();
        Map<String, Tbemp> allEmployees = context.getAllEmployees();

        if (StrUtil.isNotBlank(subTeam.getEmpincode())) {
            Tbemp teamLeader = allEmployees.get(subTeam.getEmpincode().substring(4));
            if (!teamLeader.getEmpcode().equals(context.getEmployeeCode())) {
                // 返回小组主管信息
                EmployeeVO employeeVO = EmployeeVO.builder()
                        .code(teamLeader.getEmpcode())
                        .name(teamLeader.getEmpname())
                        .teamLevel(EmployeeLogConstants.TEAM_LEVEL_GROUP)
                        .build();
                employees.add(employeeVO);
            } else {
                // 该代理人为小组主管，获取组内人员信息
                employees.addAll(dataService.getTeamMembersForAudit(
                        subTeam.getSaleteamincode(), context.getEmployeeCode()));
            }
        } else {
            // 无小组主管，获取组内人员信息
            employees.addAll(dataService.getTeamMembersForAudit(
                    subTeam.getSaleteamincode(), context.getEmployeeCode()));
        }

        return employees;
    }

    /**
     * 构建子团队层级（审核用）
     *
     * @param context        执行上下文
     * @param departmentTeam 部门团队
     * @return 子层级列表
     */
    private List<EmployeeLogHierarchyLevelVO> buildSubTeamLevelsForAudit(EmployeeLogContext context, 
                                                                        Tbsaleteam departmentTeam) {
        List<EmployeeLogHierarchyLevelVO> subLevels = new ArrayList<>();
        
        // 这里的逻辑根据原始代码可能有问题，需要根据实际业务调整
        // 查询部下级团队（这里使用departmentTeam的上级团队编码可能不正确）
        List<Tbsaleteam> childrenTeams = dataService.getSubTeams(departmentTeam.getSupersaleteamcode());
        
        if (CollUtil.isNotEmpty(childrenTeams)) {
            for (Tbsaleteam childTeam : childrenTeams) {
                List<EmployeeVO> employees = buildChildTeamEmployeesForAudit(context, childTeam);
                
                if (CollUtil.isNotEmpty(employees)) {
                    EmployeeLogHierarchyLevelVO subLevel = EmployeeLogHierarchyLevelVO.builder()
                            .code(childTeam.getSaleteamcode())
                            .name(childTeam.getSaleteamname())
                            .level(EmployeeLogConstants.LevelNames.TEAM)
                            .employeeList(employees)
                            .build();
                    subLevels.add(subLevel);
                }
            }
        }

        return subLevels;
    }

    /**
     * 构建子团队员工列表（审核用）
     *
     * @param context   执行上下文
     * @param childTeam 子团队
     * @return 员工列表
     */
    private List<EmployeeVO> buildChildTeamEmployeesForAudit(EmployeeLogContext context, Tbsaleteam childTeam) {
        List<EmployeeVO> employees = new ArrayList<>();
        Map<String, Tbemp> allEmployees = context.getAllEmployees();

        if (StrUtil.isNotBlank(childTeam.getEmpincode())) {
            if (!childTeam.getEmpincode().substring(4).equals(context.getEmployeeCode())) {
                Tbemp emp = allEmployees.get(childTeam.getEmpincode().substring(4));
                EmployeeVO employeeVO = EmployeeVO.builder()
                        .code(emp.getEmpcode())
                        .name(emp.getEmpname())
                        .teamLevel(EmployeeLogConstants.TEAM_LEVEL_GROUP)
                        .build();
                employees.add(employeeVO);
            } else {
                // 该代理人为小组主管，获取组内人员信息
                employees.addAll(dataService.getTeamMembersForAudit(
                        childTeam.getSaleteamincode(), context.getEmployeeCode()));
            }
        } else {
            // 该代理人为小组主管，获取组内人员信息
            employees.addAll(dataService.getTeamMembersForAudit(
                    childTeam.getSaleteamincode(), context.getEmployeeCode()));
        }

        return employees;
    }

    /**
     * 获取最高等级的团队
     *
     * @param teamList 团队列表
     * @return 最高等级的团队
     */
    private Tbsaleteam getHighestLevelTeam(List<Tbsaleteam> teamList) {
        if (CollUtil.isEmpty(teamList)) {
            return null;
        }

        return teamList.stream()
                .filter(team -> StrUtil.isNotBlank(team.getTeamlevel()))
                .max((team1, team2) -> team1.getTeamlevel().compareTo(team2.getTeamlevel()))
                .orElse(null);
    }
} 
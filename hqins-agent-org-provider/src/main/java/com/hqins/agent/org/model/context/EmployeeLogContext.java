package com.hqins.agent.org.model.context;

import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.dao.entity.org.SupervisorEmployee;
import com.hqins.agent.org.enums.EmployeeType;
import com.hqins.agent.org.enums.OperationType;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 员工日志执行上下文
 *
 * <AUTHOR> MXH
 * @create 2025/1/15
 */
@Data
@Builder
public class EmployeeLogContext {

    /**
     * 员工编码
     */
    private String employeeCode;

    /**
     * 员工类型
     */
    private EmployeeType employeeType;

    /**
     * 操作类型
     */
    private OperationType operationType;

    /**
     * 所有员工信息映射
     */
    private Map<String, Tbemp> allEmployees;

    /**
     * 所有团队信息映射
     */
    private Map<String, Tbsaleteam> allTeams;

    /**
     * 所有机构信息映射
     */
    private Map<String, BaseInst> allInstitutions;

    /**
     * 督导员工信息（当员工类型为督导时）
     */
    private SupervisorEmployee supervisorEmployee;

    /**
     * 当前员工信息
     */
    private Tbemp currentEmployee;

    /**
     * 管理的团队列表（当员工为代理人时）
     */
    private List<Tbsaleteam> managedTeams;

    /**
     * 机构编码列表（当员工为督导时）
     */
    private List<String> institutionCodes;

    /**
     * 按机构分组的团队信息
     */
    private Map<String, List<Tbsaleteam>> teamsByInstitution;
} 
package com.hqins.agent.org.service.impl;

import com.hqins.agent.org.builder.EmployeeLogHierarchyBuilder;
import com.hqins.agent.org.config.EmployeeLogOptimizationConfig;
import com.hqins.agent.org.model.context.EmployeeLogContext;
import com.hqins.agent.org.model.vo.EmployeeLogResultVO;
import com.hqins.agent.org.strategy.EmployeeHierarchyStrategy;
import com.hqins.agent.org.strategy.impl.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 员工日志版本管理服务
 * 支持原版本和优化版本的动态切换
 *
 * <AUTHOR> MXH
 * @create 2025/1/15
 */
@Service
@Slf4j
public class EmployeeLogVersionManager {

    private final EmployeeLogOptimizationConfig optimizationConfig;
    
    // 原版本策略
    private final AgentQueryStrategy agentQueryStrategy;
    private final AgentAuditStrategy agentAuditStrategy;
    private final SupervisorQueryStrategy supervisorQueryStrategy;
    private final SupervisorAuditStrategy supervisorAuditStrategy;
    
    // 优化版本策略（可能为null，取决于配置）
    private OptimizedSupervisorQueryStrategy optimizedSupervisorQueryStrategy;
    private OptimizedSupervisorAuditStrategy optimizedSupervisorAuditStrategy;

    public EmployeeLogVersionManager(
            EmployeeLogOptimizationConfig optimizationConfig,
            AgentQueryStrategy agentQueryStrategy,
            AgentAuditStrategy agentAuditStrategy,
            SupervisorQueryStrategy supervisorQueryStrategy,
            SupervisorAuditStrategy supervisorAuditStrategy,
            @Autowired(required = false) OptimizedSupervisorQueryStrategy optimizedSupervisorQueryStrategy,
            @Autowired(required = false) OptimizedSupervisorAuditStrategy optimizedSupervisorAuditStrategy) {
        this.optimizationConfig = optimizationConfig;
        this.agentQueryStrategy = agentQueryStrategy;
        this.agentAuditStrategy = agentAuditStrategy;
        this.supervisorQueryStrategy = supervisorQueryStrategy;
        this.supervisorAuditStrategy = supervisorAuditStrategy;
        this.optimizedSupervisorQueryStrategy = optimizedSupervisorQueryStrategy;
        this.optimizedSupervisorAuditStrategy = optimizedSupervisorAuditStrategy;
    }

    private Map<String, EmployeeHierarchyStrategy> originalStrategies;
    private Map<String, EmployeeHierarchyStrategy> optimizedStrategies;

    @PostConstruct
    public void initializeStrategies() {
        // 初始化原版本策略映射
        originalStrategies = new HashMap<>();
        originalStrategies.put("AGENT_QUERY_STRATEGY", agentQueryStrategy);
        originalStrategies.put("AGENT_AUDIT_STRATEGY", agentAuditStrategy);
        originalStrategies.put("SUPERVISOR_QUERY_STRATEGY", supervisorQueryStrategy);
        originalStrategies.put("SUPERVISOR_AUDIT_STRATEGY", supervisorAuditStrategy);

        // 初始化优化版本策略映射
        optimizedStrategies = new HashMap<>();
        
        // 代理人策略暂时使用原版本（优化版本主要针对督导）
        optimizedStrategies.put("AGENT_QUERY_STRATEGY", agentQueryStrategy);
        optimizedStrategies.put("AGENT_AUDIT_STRATEGY", agentAuditStrategy);
        
        // 督导策略使用优化版本（如果可用）
        if (optimizedSupervisorQueryStrategy != null) {
            optimizedStrategies.put("SUPERVISOR_QUERY_STRATEGY", optimizedSupervisorQueryStrategy);
        } else {
            optimizedStrategies.put("SUPERVISOR_QUERY_STRATEGY", supervisorQueryStrategy);
        }
        
        if (optimizedSupervisorAuditStrategy != null) {
            optimizedStrategies.put("SUPERVISOR_AUDIT_STRATEGY", optimizedSupervisorAuditStrategy);
        } else {
            optimizedStrategies.put("SUPERVISOR_AUDIT_STRATEGY", supervisorAuditStrategy);
        }

        log.info("员工日志版本管理器初始化完成");
        log.info("优化版本启用状态: {}", optimizationConfig.isOptimizationEnabled());
        log.info("可用策略: 原版本={}, 优化版本={}", originalStrategies.keySet(), optimizedStrategies.keySet());
    }

    /**
     * 构建员工日志层级结构
     * 根据配置自动选择使用原版本或优化版本
     *
     * @param context 执行上下文
     * @param builder 层级构建器
     * @return 构建结果
     */
    public EmployeeLogResultVO buildHierarchy(EmployeeLogContext context, EmployeeLogHierarchyBuilder builder) {
        String strategyKey = buildStrategyKey(context);
        
        // 根据配置选择策略版本
        Map<String, EmployeeHierarchyStrategy> strategies = optimizationConfig.isOptimizationEnabled() 
                ? optimizedStrategies 
                : originalStrategies;
        
        EmployeeHierarchyStrategy strategy = strategies.get(strategyKey);
        if (strategy == null) {
            log.error("未找到对应的策略实现: {}", strategyKey);
            throw new RuntimeException("未找到对应的策略实现: " + strategyKey);
        }

        String version = optimizationConfig.isOptimizationEnabled() ? "优化版本" : "原版本";
        log.info("使用{}策略构建层级结构: {}", version, strategyKey);

        return strategy.buildHierarchy(context, builder);
    }

    /**
     * 强制使用原版本构建层级结构
     *
     * @param context 执行上下文
     * @param builder 层级构建器
     * @return 构建结果
     */
    public EmployeeLogResultVO buildHierarchyWithOriginal(EmployeeLogContext context, EmployeeLogHierarchyBuilder builder) {
        String strategyKey = buildStrategyKey(context);
        EmployeeHierarchyStrategy strategy = originalStrategies.get(strategyKey);
        
        if (strategy == null) {
            log.error("未找到原版本策略实现: {}", strategyKey);
            throw new RuntimeException("未找到原版本策略实现: " + strategyKey);
        }

        log.info("强制使用原版本策略构建层级结构: {}", strategyKey);
        return strategy.buildHierarchy(context, builder);
    }

    /**
     * 强制使用优化版本构建层级结构
     *
     * @param context 执行上下文
     * @param builder 层级构建器
     * @return 构建结果
     */
    public EmployeeLogResultVO buildHierarchyWithOptimized(EmployeeLogContext context, EmployeeLogHierarchyBuilder builder) {
        String strategyKey = buildStrategyKey(context);
        EmployeeHierarchyStrategy strategy = optimizedStrategies.get(strategyKey);
        
        if (strategy == null) {
            log.error("未找到优化版本策略实现: {}", strategyKey);
            throw new RuntimeException("未找到优化版本策略实现: " + strategyKey);
        }

        log.info("强制使用优化版本策略构建层级结构: {}", strategyKey);
        return strategy.buildHierarchy(context, builder);
    }

    /**
     * 性能对比测试
     *
     * @param context 执行上下文
     * @param builder 层级构建器
     * @return 性能对比结果
     */
    public Map<String, Object> performanceComparison(EmployeeLogContext context, EmployeeLogHierarchyBuilder builder) {
        Map<String, Object> comparison = new HashMap<>();

        try {
            // 测试原版本
            long originalStart = System.currentTimeMillis();
            EmployeeLogResultVO originalResult = buildHierarchyWithOriginal(context, builder.clone());
            long originalEnd = System.currentTimeMillis();
            long originalTime = originalEnd - originalStart;

            // 测试优化版本
            long optimizedStart = System.currentTimeMillis();
            EmployeeLogResultVO optimizedResult = buildHierarchyWithOptimized(context, builder.clone());
            long optimizedEnd = System.currentTimeMillis();
            long optimizedTime = optimizedEnd - optimizedStart;

            // 计算性能提升
            double improvement = originalTime > 0 ? (double)(originalTime - optimizedTime) / originalTime * 100 : 0;
            double speedup = optimizedTime > 0 ? (double)originalTime / optimizedTime : 1;

            comparison.put("原版本耗时(ms)", originalTime);
            comparison.put("优化版本耗时(ms)", optimizedTime);
            comparison.put("性能提升(%)", String.format("%.1f", improvement));
            comparison.put("加速比", String.format("%.1fx", speedup));
            comparison.put("结果一致性", compareResults(originalResult, optimizedResult));

            log.info("性能对比完成 - 原版本: {}ms, 优化版本: {}ms, 提升: {:.1f}%",
                    originalTime, optimizedTime, improvement);

        } catch (Exception e) {
            log.error("性能对比测试失败", e);
            comparison.put("错误", e.getMessage());
        }

        return comparison;
    }

    /**
     * 获取当前版本信息
     *
     * @return 版本信息
     */
    public Map<String, Object> getVersionInfo() {
        Map<String, Object> info = new HashMap<>();
        
        info.put("当前版本", optimizationConfig.isOptimizationEnabled() ? "优化版本" : "原版本");
        info.put("优化配置", optimizationConfig.getOptimizationSummary());
        info.put("线程池配置", optimizationConfig.getExecutorConfigInfo());
        info.put("原版本策略数", originalStrategies.size());
        info.put("优化版本策略数", optimizedStrategies.size());
        
        return info;
    }

    /**
     * 构建策略键
     *
     * @param context 执行上下文
     * @return 策略键
     */
    private String buildStrategyKey(EmployeeLogContext context) {
        String employeeType = context.getEmployeeType().name();
        String operationType = context.getOperationType().name();
        return String.format("%s_%s_STRATEGY", employeeType, operationType);
    }

    /**
     * 比较两个结果的一致性
     *
     * @param original  原版本结果
     * @param optimized 优化版本结果
     * @return 是否一致
     */
    private boolean compareResults(EmployeeLogResultVO original, EmployeeLogResultVO optimized) {
        if (original == null && optimized == null) {
            return true;
        }
        
        if (original == null || optimized == null) {
            return false;
        }
        
        // 简单比较，实际可以进行更详细的对比
        return original.getEmployeeCode().equals(optimized.getEmployeeCode()) &&
               original.getEmployeeRole().equals(optimized.getEmployeeRole()) &&
               original.getHierarchyList().size() == optimized.getHierarchyList().size();
    }
} 
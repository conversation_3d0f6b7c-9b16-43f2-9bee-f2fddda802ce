package com.hqins.agent.org.strategy;

import com.hqins.agent.org.builder.EmployeeLogHierarchyBuilder;
import com.hqins.agent.org.model.context.EmployeeLogContext;
import com.hqins.agent.org.model.vo.EmployeeLogResultVO;

/**
 * 员工层级策略接口
 *
 * <AUTHOR> MXH
 * @create 2025/1/15
 */
public interface EmployeeHierarchyStrategy {

    /**
     * 构建层级结构
     *
     * @param context 执行上下文
     * @param builder 层级建造者
     * @return 构建结果
     */
    EmployeeLogResultVO buildHierarchy(EmployeeLogContext context, EmployeeLogHierarchyBuilder builder);
} 
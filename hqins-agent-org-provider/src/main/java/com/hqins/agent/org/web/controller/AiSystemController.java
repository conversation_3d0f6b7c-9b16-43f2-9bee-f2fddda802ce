package com.hqins.agent.org.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.hqins.agent.org.model.request.AiQueryRequest;
import com.hqins.agent.org.model.request.AiRespondBean;
import com.hqins.agent.org.model.request.ChannelEmployeeAddRequest;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.AiSystemService;
import com.hqins.agent.org.service.HonorSystemService;
import com.hqins.common.base.ApiResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/29
 */
@Api(tags = "AI访问服务")
@RestController
@RequestMapping("/ai-isms")
@RefreshScope
@Slf4j
public class AiSystemController {

    @Autowired
    private AiSystemService aiSystemService;

    @ApiOperation("AI调用销管系统创费服务")
    @PostMapping("/call-policy-report")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<AiRespondBean> getPolicyReportInfo(@RequestBody AiQueryRequest requestBean) {
        try {
            return ApiResult.ok(aiSystemService.getPolicyReportInfo(requestBean.getSql(), requestBean.getApiKey()));
        } catch (Exception e) {
            log.error("AI调用出错", e);
            return ApiResult.fail("AI调用出错," + e.getMessage());
        }
    }

    @ApiOperation("AI调用销管系统创费服务A")
    @PostMapping("/call-policy-report-select")
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<AiRespondBean> getPolicyReportInfoBySelect(@RequestBody Map<String, Object> requestBean) {
        // 参数校验
        if (!requestBean.containsKey("X_API_KEY") || !requestBean.containsKey("SQL")) {
            return ApiResult.fail("参数不能为空！");
        }

        String apiKey = (String) requestBean.get("X_API_KEY");
        String sql = (String) requestBean.get("SQL");

        // 类型检查与空值处理
        if (apiKey == null || sql == null) {
            return ApiResult.fail("参数类型错误或为空！");
        }

        try {
            return ApiResult.ok(aiSystemService.getPolicyReportInfo(sql, apiKey));
        } catch (Exception e) {
            log.error("AI调用出错", e);
            return ApiResult.fail("AI调用出错," + e.getMessage());
        }
    }



}

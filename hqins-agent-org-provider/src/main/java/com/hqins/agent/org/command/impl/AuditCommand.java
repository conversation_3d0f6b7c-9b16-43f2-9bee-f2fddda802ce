package com.hqins.agent.org.command.impl;

import com.hqins.agent.org.builder.EmployeeLogHierarchyBuilder;
import com.hqins.agent.org.command.EmployeeLogCommand;
import com.hqins.agent.org.model.context.EmployeeLogContext;
import com.hqins.agent.org.model.vo.EmployeeLogResultVO;
import com.hqins.agent.org.service.impl.EmployeeLogVersionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 审核命令实现
 *
 * <AUTHOR> MXH
 * @create 2025/1/15
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class AuditCommand implements EmployeeLogCommand {

    private final EmployeeLogVersionManager versionManager;

    @Override
    public EmployeeLogResultVO execute(EmployeeLogContext context) {
        log.info("审核命令执行, employeeType: {}, employeeCode: {}", 
                context.getEmployeeType(), context.getEmployeeCode());
        
        // 创建建造者
        EmployeeLogHierarchyBuilder builder = EmployeeLogHierarchyBuilder.newBuilder()
                .setEmployee(context.getEmployeeCode(), context.getEmployeeType().getRoleName());

        // 通过版本管理器执行策略构建层级结构（自动选择优化版本或原版本）
        return versionManager.buildHierarchy(context, builder);
    }
} 
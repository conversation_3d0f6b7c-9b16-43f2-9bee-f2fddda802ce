package com.hqins.agent.org.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.hqins.agent.org.dao.HonorSystemDao;
import com.hqins.agent.org.dao.entity.settle.HonorAssessmentHistory;
import com.hqins.agent.org.dao.entity.settle.HonorCurrentInfo;
import com.hqins.agent.org.model.enums.CheckMetricsItemEnum;
import com.hqins.agent.org.model.enums.HonorItemEnum;
import com.hqins.agent.org.model.enums.HonorSystemConstants;
import com.hqins.agent.org.model.request.AiRespondBean;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.AiSystemService;
import com.hqins.agent.org.service.EmployeeService;
import com.hqins.agent.org.service.HonorSystemService;
import com.hqins.agent.org.service.PartnerEmployeeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.format.ResolverStyle;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/29
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AiSystemServiceImpl implements AiSystemService {

    final DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    final DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    @Override
    public AiRespondBean getPolicyReportInfo(String sql, String apiKey) {
        AiRespondBean aiRespondBean = new AiRespondBean();
        log.info("getTeamHonoraryTitleList param: sql={}, apiKey={} ", sql ,apiKey );

        if(!"K3y$tr!ng_2025@S3cur3".equals(apiKey)){
            aiRespondBean.FLAG = false;
            aiRespondBean.MESSAGE = "您没有权限访问服务！";
            return aiRespondBean;
        }
        // 定义API的URL
        String url = "https://new-msg-admin-uat.e-hqins.com/distributors/SERVER_V2/4001/SHUB-AIExecuteSql";

        // 创建HttpClient实例
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 使用URIBuilder来构建带有查询参数的URI
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.addParameter("apiKey", apiKey);
            uriBuilder.addParameter("SQL", sql);

            // 构建HttpGet请求
            URI uri = uriBuilder.build();
            HttpGet request = new HttpGet(uri);

            // 添加API密钥到请求头
            request.setHeader("X-API-KEY", apiKey);

            // 执行请求并获取响应
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                // 获取响应实体
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    // 将响应实体转换为字符串
                    String result = EntityUtils.toString(entity);
                    log.info("Response: " + result);
                    // 解析JSON字符串到AiRespondBean对象
                    aiRespondBean = JSON.parseObject(result, AiRespondBean.class);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return aiRespondBean;
    }

}

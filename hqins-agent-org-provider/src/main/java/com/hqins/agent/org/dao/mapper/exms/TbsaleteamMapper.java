package com.hqins.agent.org.dao.mapper.exms;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* <AUTHOR>
* @date 2021-05-07
* @Description 
*/
@Mapper
@DS("exms")
@Repository
public interface TbsaleteamMapper extends BaseMapper<Tbsaleteam> {

    /**
     * 根据团队状态查询团队信息
     * @param teamStatus
     * @return
     */
    List<Tbsaleteam> selectByTeamStatus(@Param("teamStatus") String teamStatus);

    Tbsaleteam selectBySaleTeamCode(@Param("saleTeamCode") String saleTeamCode);

}


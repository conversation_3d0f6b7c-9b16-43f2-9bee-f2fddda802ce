package com.hqins.agent.org.strategy.impl;

import cn.hutool.core.collection.CollUtil;
import com.hqins.agent.org.builder.EmployeeLogHierarchyBuilder;
import com.hqins.agent.org.config.EmployeeLogOptimizationConfig;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.model.context.EmployeeLogContext;
import com.hqins.agent.org.model.vo.EmployeeLogHierarchyLevelVO;
import com.hqins.agent.org.model.vo.EmployeeLogResultVO;
import com.hqins.agent.org.service.impl.AsyncEmployeeLogProcessor;
import com.hqins.agent.org.service.impl.BatchEmployeeLogDataService;
import com.hqins.agent.org.strategy.EmployeeHierarchyStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 优化版督导查询策略实现
 * 使用批量查询和异步并行处理提升性能
 *
 * <AUTHOR> MXH
 * @create 2025/1/15
 */
@Component("OPTIMIZED_SUPERVISOR_QUERY_STRATEGY")
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(name = "employee.log.optimization.enabled", havingValue = "true", matchIfMissing = false)
public class OptimizedSupervisorQueryStrategy implements EmployeeHierarchyStrategy {

    private final BatchEmployeeLogDataService batchDataService;
    private final AsyncEmployeeLogProcessor asyncProcessor;
    private final EmployeeLogOptimizationConfig optimizationConfig;

    @Override
    public EmployeeLogResultVO buildHierarchy(EmployeeLogContext context, EmployeeLogHierarchyBuilder builder) {
        long startTime = System.currentTimeMillis();
        log.info("开始优化版督导查询层级构建, employeeCode: {}", context.getEmployeeCode());

        try {
            List<String> institutionCodes = context.getInstitutionCodes();
            Map<String, BaseInst> allInstitutions = context.getAllInstitutions();

            // 检查并限制机构数量，防止内存溢出
            if (institutionCodes.size() > optimizationConfig.getMaxInstitutions()) {
                log.warn("机构数量超过限制 {} > {}，将截取前{}个机构进行处理", 
                        institutionCodes.size(), optimizationConfig.getMaxInstitutions(), optimizationConfig.getMaxInstitutions());
                institutionCodes = institutionCodes.subList(0, optimizationConfig.getMaxInstitutions());
            }

            // 性能监控
            if (optimizationConfig.isPerformanceMonitoringEnabled()) {
                Map<String, Object> metrics = batchDataService.getPerformanceMetrics(institutionCodes);
                log.info("数据规模统计: {}", metrics);
            }

            // 1. 批量查询所有机构的团队信息（关键优化：从N次查询减少到1次）
            Map<String, List<com.hqins.agent.org.dao.entity.exms.Tbsaleteam>> teamsByInstitution = 
                    batchDataService.getTeamsByInstitutionsBatch(institutionCodes);

            // 2. 并行处理每个机构（关键优化：从串行改为并行）
            List<CompletableFuture<EmployeeLogHierarchyLevelVO>> futures = institutionCodes.stream()
                    .map(institutionCode -> {
                        BaseInst institution = allInstitutions.get(institutionCode);
                        if (institution == null) {
                            log.warn("机构信息不存在: {}", institutionCode);
                            return CompletableFuture.<EmployeeLogHierarchyLevelVO>completedFuture(null);
                        }
                        
                        List<com.hqins.agent.org.dao.entity.exms.Tbsaleteam> teams = teamsByInstitution.get(institutionCode);
                        if (CollUtil.isEmpty(teams)) {
                            log.debug("机构无团队信息: {}", institutionCode);
                            return CompletableFuture.<EmployeeLogHierarchyLevelVO>completedFuture(null);
                        }

                        return asyncProcessor.buildInstitutionQueryHierarchyAsync(institutionCode, institution, teams, context);
                    })
                    .collect(Collectors.toList());

            // 3. 等待所有异步任务完成并收集结果
            List<EmployeeLogHierarchyLevelVO> institutionLevels = futures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 4. 构建最终结果
            institutionLevels.forEach(builder::addLevel);

            long endTime = System.currentTimeMillis();
            log.info("完成优化版督导查询层级构建, 耗时: {}ms, 机构数: {}, 层级数: {}", 
                    endTime - startTime, institutionCodes.size(), institutionLevels.size());

            return builder.build();

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("优化版督导查询层级构建失败, 耗时: {}ms, employeeCode: {}", 
                    endTime - startTime, context.getEmployeeCode(), e);
            throw new RuntimeException("构建督导查询层级失败", e);
        }
    }

    /**
     * 获取策略名称
     * 
     * @return 策略名称
     */
    public String getStrategyName() {
        return "优化版督导查询策略";
    }

    /**
     * 获取优化特性描述
     * 
     * @return 优化特性列表
     */
    public List<String> getOptimizationFeatures() {
        return Arrays.asList(
                "批量数据查询：从N次查询优化为1次查询",
                "异步并行处理：机构处理从串行改为并行",
                "内存优化：使用HashMap提供O(1)查找效率",
                "性能监控：实时监控执行性能和数据规模",
                "资源控制：限制处理规模防止内存溢出"
        );
    }

    /**
     * 估算性能提升
     * 
     * @param institutionCount 机构数量
     * @return 预期性能提升信息
     */
    public Map<String, Object> estimatePerformanceImprovement(int institutionCount) {
        Map<String, Object> estimation = new HashMap<>();
        
        // 数据库查询优化：假设原来每个机构平均需要5次查询，现在只需要2次
        int originalQueries = institutionCount * 5;
        int optimizedQueries = 2;
        double queryReduction = (double)(originalQueries - optimizedQueries) / originalQueries * 100;
        
        // 并行处理优化：理论上可以达到机构数量倍的提升（受限于CPU核数）
        int maxParallelism = Math.min(institutionCount, Runtime.getRuntime().availableProcessors());
        double parallelSpeedup = Math.min(institutionCount, maxParallelism);
        
        // 综合性能提升估算
        double overallImprovement = queryReduction + parallelSpeedup * 10; // 权重调整
        
        estimation.put("机构数量", institutionCount);
        estimation.put("查询减少率", String.format("%.1f%%", queryReduction));
        estimation.put("并行加速比", String.format("%.1fx", parallelSpeedup));
        estimation.put("预期性能提升", String.format("%.1f%%", Math.min(overallImprovement, 80))); // 最高80%
        estimation.put("最大并行度", maxParallelism);
        
        return estimation;
    }
} 
package com.hqins.agent.org.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.constants.EmployeeLogConstants;
import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.dao.mapper.exms.TbempMapper;
import com.hqins.agent.org.dao.mapper.exms.TbsaleteamMapper;
import com.hqins.agent.org.model.vo.EmployeeVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 员工日志数据服务
 *
 * <AUTHOR> MXH
 * @create 2025/1/15
 */
@Service
@RequiredArgsConstructor
public class EmployeeLogDataService {

    private final CacheService cacheService;
    private final TbsaleteamMapper tbsaleteamMapper;
    private final TbempMapper tbempMapper;

    /**
     * 获取所有员工信息映射
     *
     * @return 员工信息映射
     */
    public Map<String, Tbemp> getAllEmployeesMap() {
        return cacheService.getAllTbempMap();
    }

    /**
     * 获取所有团队信息映射
     *
     * @return 团队信息映射
     */
    public Map<String, Tbsaleteam> getAllTeamsMap() {
        return cacheService.getAllTbsaleteamsMap();
    }

    /**
     * 获取所有机构信息映射
     *
     * @return 机构信息映射
     */
    public Map<String, BaseInst> getAllInstitutionsMap() {
        return cacheService.getAllBaseInstsMap();
    }

    /**
     * 根据机构编码列表获取团队信息并按机构分组
     *
     * @param institutionCodes 机构编码列表
     * @return 按机构分组的团队信息
     */
    public Map<String, List<Tbsaleteam>> getTeamsByInstitutions(List<String> institutionCodes) {
        List<Tbsaleteam> teams = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                .eq(Tbsaleteam::getSaleteamstatus, EmployeeLogConstants.TEAM_STATUS_ACTIVE)
                .eq(Tbsaleteam::getCompanycode, EmployeeLogConstants.COMPANY_CODE)
                .in(Tbsaleteam::getInstCode, institutionCodes));

        return teams.stream().collect(Collectors.groupingBy(Tbsaleteam::getInstCode));
    }

    /**
     * 根据上级团队编码查询下级团队
     *
     * @param superTeamCode 上级团队编码
     * @return 下级团队列表
     */
    public List<Tbsaleteam> getSubTeams(String superTeamCode) {
        return tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                .eq(Tbsaleteam::getSaleteamstatus, EmployeeLogConstants.TEAM_STATUS_ACTIVE)
                .eq(Tbsaleteam::getSupersaleteamcode, superTeamCode)
                .orderByAsc(Tbsaleteam::getSaleteamincode));
    }

    /**
     * 根据团队编码查询团队成员
     *
     * @param teamCode 团队编码
     * @param excludeEmployeeCode 排除的员工编码
     * @return 团队成员列表
     * @deprecated 使用 getTeamMembers(String, String, boolean) 替代
     */
    @Deprecated
    public List<EmployeeVO> getTeamMembers(String teamCode, String excludeEmployeeCode) {
        return getTeamMembers(teamCode, excludeEmployeeCode, false);
    }

    /**
     * 根据团队编码查询团队成员
     *
     * @param teamCode 团队编码
     * @param currentEmployeeCode 当前员工编码
     * @param includeCurrentEmployee 是否包含当前员工（true=查阅模式，false=审核模式）
     * @return 团队成员列表
     */
    public List<EmployeeVO> getTeamMembers(String teamCode, String currentEmployeeCode, boolean includeCurrentEmployee) {
        LambdaQueryWrapper<Tbemp> wrapper = new LambdaQueryWrapper<Tbemp>()
                .eq(Tbemp::getSaleteamincode, teamCode)
                .eq(Tbemp::getCompanycode, EmployeeLogConstants.COMPANY_CODE)
                .eq(Tbemp::getEmpstatus, EmployeeLogConstants.EMP_STATUS_ACTIVE)
                .eq(Tbemp::getIsvirtualemp, EmployeeLogConstants.IS_VIRTUAL_EMP_NO)
                .eq(Tbemp::getIsinsideflag, EmployeeLogConstants.IS_INSIDE_FLAG_YES);

        // 根据业务模式决定是否排除当前员工
        if (StrUtil.isNotBlank(currentEmployeeCode) && !includeCurrentEmployee) {
            wrapper.ne(Tbemp::getEmpcode, currentEmployeeCode);
        }

        List<Tbemp> employees = tbempMapper.selectList(wrapper);
        
        return employees.stream()
                .map(emp -> EmployeeVO.builder()
                        .code(emp.getEmpcode())
                        .name(emp.getEmpname())
                        .teamLevel(EmployeeLogConstants.TEAM_LEVEL_GROUP) // 修复：使用常量而不是硬编码
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 根据员工内码查询管理的团队
     *
     * @param empInCode 员工内码
     * @return 管理的团队列表
     */
    public List<Tbsaleteam> getManagedTeams(String empInCode) {
        return tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                .eq(Tbsaleteam::getSaleteamstatus, EmployeeLogConstants.TEAM_STATUS_ACTIVE)
                .eq(Tbsaleteam::getEmpincode, empInCode)
                .eq(Tbsaleteam::getCompanycode, EmployeeLogConstants.COMPANY_CODE)
                .orderByDesc(Tbsaleteam::getTeamlevel));
    }

    /**
     * 根据团队等级筛选团队
     *
     * @param teams 团队列表
     * @param teamLevel 团队等级
     * @return 筛选后的团队列表
     */
    public List<Tbsaleteam> filterTeamsByLevel(List<Tbsaleteam> teams, String teamLevel) {
        return teams.stream()
                .filter(team -> teamLevel.equals(team.getTeamlevel()))
                .collect(Collectors.toList());
    }

    /**
     * 将字符串转换为字符串列表
     *
     * @param str 输入字符串
     * @return 字符串列表
     */
    public List<String> convertToList(String str) {
        if (StrUtil.isBlank(str) || EmployeeLogConstants.EMPTY_LIST_STRING.equals(str)) {
            return new ArrayList<>();
        }
        
        return Arrays.stream(str.replaceAll("[\\[\\]\"]", "").split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }

    /**
     * 获取最高等级的团队
     *
     * @param teamList 团队列表
     * @return 最高等级的团队
     */
    public Tbsaleteam getHighestLevelTeam(List<Tbsaleteam> teamList) {
        if (CollUtil.isEmpty(teamList)) {
            return null;
        }

        return teamList.stream()
                .filter(team -> team.getTeamlevel() != null && !team.getTeamlevel().trim().isEmpty())
                .max((team1, team2) -> team1.getTeamlevel().compareTo(team2.getTeamlevel()))
                .orElse(null);
    }

    /**
     * 根据团队编码查询团队成员（审核用，排除指定员工）
     * 注意：为与原始版本extracted方法保持一致，此方法不包含isinsideflag查询条件
     *
     * @param teamCode 团队编码
     * @param excludeEmployeeCode 排除的员工编码
     * @return 团队成员列表
     */
    public List<EmployeeVO> getTeamMembersForAudit(String teamCode, String excludeEmployeeCode) {
        LambdaQueryWrapper<Tbemp> wrapper = new LambdaQueryWrapper<Tbemp>()
                .eq(Tbemp::getSaleteamincode, teamCode)
                .eq(Tbemp::getCompanycode, EmployeeLogConstants.COMPANY_CODE)
                .eq(Tbemp::getEmpstatus, EmployeeLogConstants.EMP_STATUS_ACTIVE)
                .eq(Tbemp::getIsvirtualemp, EmployeeLogConstants.IS_VIRTUAL_EMP_NO);
                // 注意：此处不包含.eq(Tbemp::getIsinsideflag, "1")以与原始版本extracted方法保持一致

        // 审核时排除指定员工
        if (StrUtil.isNotBlank(excludeEmployeeCode)) {
            wrapper.ne(Tbemp::getEmpcode, excludeEmployeeCode);
        }

        List<Tbemp> employees = tbempMapper.selectList(wrapper);
        
        return employees.stream()
                .map(emp -> EmployeeVO.builder()
                        .code(emp.getEmpcode())
                        .name(emp.getEmpname())
                        .teamLevel(EmployeeLogConstants.TEAM_LEVEL_GROUP) // 审核时默认为组级别
                        .build())
                .collect(Collectors.toList());
    }
} 
package com.hqins.agent.org.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.hqins.agent.org.dao.PerformanceDao;
import com.hqins.agent.org.dao.TbSaleTeamDao;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.dao.entity.org.SupervisorEmployee;
import com.hqins.agent.org.dao.mapper.iips.TbepartnerMapper;
import com.hqins.agent.org.dao.mapper.org.SupervisorEmployeeMapper;
import com.hqins.agent.org.enums.CommissionItemEnum;
import com.hqins.agent.org.model.request.TeamPerformanceRequest;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.PerformanceService;
import com.hqins.agent.org.service.TeamService;
import com.hqins.common.utils.JsonUtil;
import com.hqins.common.web.RequestContextHolder;
import com.hqins.policy.api.PolicyEsApi;
import com.hqins.policy.api.PolicyQueryApi;
import com.hqins.policy.model.request.PolicyEsQueryRequestVo;
import com.hqins.policy.model.vo.SecurityResponse;
import com.hqins.policy.model.vo.es.*;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PerformanceServiceImpl implements PerformanceService {


    @Autowired
    private PerformanceDao performanceDao;

    @Autowired
    private TbSaleTeamDao saleTeamDao;

    @Autowired
    private SupervisorEmployeeMapper supervisorEmployeeMapper;

    @Autowired
    private TbepartnerMapper tbepartnerMapper;

    @Autowired
    private TeamService teamService;

    @Autowired
    private PolicyEsApi policyEsApi;

    @Autowired
    private PolicyQueryApi policyQueryApi;

    @Value("${newsale.lotusorglist}")
    private String lotusOrgList;


    final String filter = "agentCode,agentName,contNo,prem,amnt,appntEsVo,riskVoList.amnt," +
            "riskVoList.prem," +
            "riskVoList.payIntv," +
            "riskVoList.payYears," +
            "riskVoList.riskPeriod," +
            "riskVoList.payEndYear," +
            "riskVoList.payEndYearFlag," +
            "riskVoList.riskName," +
            "riskVoList.riskCode," +
            "riskVoList.contNo," +
            "riskVoList.appFlag," +
            "riskVoList.primaryOrSecondary," +
            "signDateTime";

    private static DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public PersonalVO getCurrentMonthIncomeInfo(String empCode) {
        PersonalVO personalVO = new PersonalVO();
        if (StringUtils.isEmpty(empCode)){
            empCode = getCurrentLoginEmpCode();
            if (StringUtils.isEmpty(empCode)){
                log.info("校验失败,未获取到当前登录人员信息");
                return personalVO;
            }
        }else if (!checkEmpAccess(empCode, null)){
            log.info("查询失败,权限不足:{}",empCode);
            return personalVO;
        }
        log.info("个人当月收入业绩查询,{}", empCode);
        BigDecimal amount = performanceDao.getCurrentMonthIncomeInfo(empCode);
        personalVO.setCurrentMonthIncome(amount==null?BigDecimal.ZERO:amount.setScale(2,RoundingMode.HALF_UP));
        return personalVO;
    }


    private String getCurrentLoginEmpCode() {
        //获取登录人code
        return RequestContextHolder.getEmployeeCode();
    }

    @Override
    public List<PersonalIncomeVO> getPreSettleIncomeInfo() {
        List<PersonalIncomeVO> incomeVOList = new ArrayList<>();
        String currentLoginEmpCode = getCurrentLoginEmpCode();
        log.info("个人收入预结算信息查询,{}", currentLoginEmpCode);
        if (StringUtils.isEmpty(currentLoginEmpCode)){
            return incomeVOList;
        }
        List<CommissionItemVO> commissionItemVOList = performanceDao.getPreSettleCommissionVOList(currentLoginEmpCode);
        if (!CollectionUtils.isEmpty(commissionItemVOList)){
            //按月分组
            Map<String, List<CommissionItemVO>> allMonthData    = commissionItemVOList.stream().filter(o -> !StringUtils.isEmpty(o.getSettleMonth())).collect(Collectors.groupingBy(CommissionItemVO::getSettleMonth));
            if (CollectionUtils.isEmpty(allMonthData)){
                return incomeVOList;
            }
            List<String> settleMonthList = new ArrayList<>(allMonthData.keySet());
            Collections.sort(settleMonthList);
            for (String settleMonth : settleMonthList) {
                List<CommissionItemVO> monthCommissionItemVOList = allMonthData.get(settleMonth);
                PersonalIncomeVO incomeVO = initPersonalIncomeVO(settleMonth);
                if (CollectionUtils.isEmpty(monthCommissionItemVOList)){
                    incomeVOList.add(incomeVO);
                    continue;
                }
                String versionType = monthCommissionItemVOList.get(0).getVersionType();
                String rankSeqCode = monthCommissionItemVOList.get(0).getRankSeqCode();
                String rankCode = monthCommissionItemVOList.get(0).getRankCode();
                //获取当月金额数据
                getAmount(incomeVO,monthCommissionItemVOList);
                //获取当月佣金项数据
                getCommissionItemVoList(incomeVO,monthCommissionItemVOList,versionType,rankSeqCode,rankCode,"PRE");
                //获取当月科目项数据
                getCommissionVoTypeList(incomeVO,monthCommissionItemVOList, versionType,rankSeqCode,"PRE");
                //非空处理
                zeroIfNull(incomeVO);
                incomeVOList.add(incomeVO);
            }
        }
        return incomeVOList;
    }

    private void getCommissionVoTypeList(PersonalIncomeVO incomeVO, List<CommissionItemVO> monthCommissionItemVOList, String versionType, String rankSeqCode, String preFlag) {
        if (CollectionUtils.isEmpty(monthCommissionItemVOList)){
            return;
        }
        List<String> commissionTypeNameList =  getSortCommissionTypeNameList(versionType,rankSeqCode,preFlag);
        List<CommissionItemVO> collect = monthCommissionItemVOList.stream().filter(o -> "类目".equals(o.getCommissionType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)){
            //排序
            List<CommissionItemVO> sortCommissionItemVOList = new ArrayList<>();
            for (String commissionTypeName : commissionTypeNameList) {
                sortCommissionItemVOList.addAll(collect.stream().filter(o->o.getCommissionItemName().equals(commissionTypeName)).collect(Collectors.toList()));
            }
            sortCommissionItemVOList.forEach(o->{
                if (!ObjectUtils.isEmpty(o.getRate())){
                    o.setRate(o.getRate().multiply(BigDecimal.valueOf(100)));
                }
            });
            //添加未排序数据(是否必要保留？)
//            sortCommissionItemVOList.addAll(collect.stream().filter(o->!commissionTypeNameList.contains(o.getCommissionItemName())).collect(Collectors.toList()));
            if ("RLP".equals(rankSeqCode)){
                sortCommissionItemVOList.stream().filter(o->"管理利益类".equals(o.getCommissionItemName())).forEach(o->o.setCommissionItemName("管理利益类(代持)"));
            }
            incomeVO.setCommissionTypeList(sortCommissionItemVOList);
        }
    }

    private List<String> getSortCommissionTypeNameList(String versionType, String rankSeqCode, String preFlag) {
        if ("SELF_2024".equals(versionType)){
            List<String> list = new ArrayList<>(Arrays.asList("个人销售利益类","管理利益类","组织发展类","续期利益类","方案类","其他类"));
            if (!StringUtils.isEmpty(preFlag)){
                list.remove("方案类");
                list.remove("其他类");
            }
            if ("FHWC".equals(rankSeqCode)){
                list.remove("管理利益类");
            }
            return list;
        }else if (versionType!=null && versionType.contains("DIY")){
            if ("FHWC".equals(rankSeqCode)){
                return Arrays.asList("个人销售利益类","组织发展类","续期利益类","方案类","其他类");
            }else {
                return Arrays.asList("个人销售利益类", "管理利益类", "组织发展类", "续期利益类", "方案类", "其他类");
            }
        }else if ("SELF".equals(versionType)){
            if ("FWMC1".equals(rankSeqCode)){
                return Arrays.asList("个人销售利益类","续期利益类","方案类","其他类");
            }else {
                return Arrays.asList("个人销售利益类","管理利益类","续期利益类","方案类","其他类");
            }
        }else if ("SELF_LOTUS".equals(versionType)){
            List<String> list = new ArrayList<>(Arrays.asList("个人销售利益类","管理利益类","组织发展类","续期利益类","方案类","其他类"));
            if (!StringUtils.isEmpty(preFlag)){
                list.remove("方案类");
                list.remove("其他类");
            }
            return list;
        }
        return new ArrayList<>();
    }

    private void getCommissionItemVoList(PersonalIncomeVO incomeVO, List<CommissionItemVO> monthCommissionItemVOList, String versionType, String rankSeqCode, String rankCode,String preFlag) {
        if (CollectionUtils.isEmpty(monthCommissionItemVOList)){
            return;
        }
        List<String> commissionItemNameList =  getSortCommissionItemNameList(versionType,rankSeqCode,preFlag,rankCode);
        List<CommissionItemVO> collect = monthCommissionItemVOList.stream().filter(o -> "跟人".equals(o.getCommissionType()) || "跟单".equals(o.getCommissionType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)){
            //排序
            List<CommissionItemVO> sortCommissionItemVOList = new ArrayList<>();
            for (String commissionItemName : commissionItemNameList) {
                sortCommissionItemVOList.addAll(collect.stream().filter(o->o.getCommissionItemName().equals(commissionItemName)).collect(Collectors.toList()));
            }
            //添加未排序数据(不需要展示)
//            sortCommissionItemVOList.addAll(collect.stream().filter(o->!commissionItemNameList.contains(o.getCommissionItemName())).collect(Collectors.toList()));
            //特殊处理
            if ("RLP".equals(rankSeqCode)){
                sortCommissionItemVOList.stream().filter(o->"运营基金".equals(o.getCommissionItemName())).forEach(o->o.setCommissionItemName("运营基金（代持）"));
            }
            incomeVO.setCommissionItemList(sortCommissionItemVOList);
        }
    }

    private List<String> getSortCommissionItemNameList(String versionType, String rankSeqCode, String preFlag, String rankCode) {
        if ("SELF_2024".equals(versionType)){
            if ("SWS".equals(rankSeqCode)){
                if (StringUtils.isEmpty(preFlag)){
                    return Arrays.asList("首年佣金","续年佣金","月度绩效津贴","月度绩效津贴季度通算补发","月度绩效津贴年度通算补发","增员津贴","继续率奖金","合伙人月度经营津贴","合伙人月度经营津贴季度通算补发","育成津贴","机构方案","总部方案","品质扣款","其它税前加扣款");
                }else{
                    return Arrays.asList("首年佣金","续年佣金","月度绩效津贴","增员津贴","继续率奖金","合伙人月度经营津贴","育成津贴","月度绩效津贴季度通算补发","合伙人月度经营津贴季度通算补发","月度绩效津贴年度通算补发");
                }
            }else if ("FHWC".equals(rankSeqCode)){
                if (StringUtils.isEmpty(preFlag)){
                    return Arrays.asList("首年佣金","续年佣金","月度绩效津贴","月度绩效津贴季度通算补发","月度绩效津贴年度通算补发","增员津贴","继续率奖金","机构方案","总部方案","品质扣款","其它税前加扣款");
                }else{
                    return Arrays.asList("首年佣金","续年佣金","月度绩效津贴","增员津贴","继续率奖金","月度绩效津贴季度通算补发","月度绩效津贴年度通算补发");
                }
            }
        }else if (versionType!=null && versionType.contains("DIY")){
            if ("SWS".equals(rankSeqCode)){
                return Arrays.asList("首年佣金","续年佣金","月度绩效津贴","增员津贴","继续率奖金","合伙人月度经营津贴","育成津贴","机构方案","总部方案","品质扣款","其它税前加扣款","其它跟单加扣款");
            }else if ("FHWC".equals(rankSeqCode)){
                return Arrays.asList("首年佣金","续年佣金","月度绩效津贴","增员津贴","继续率奖金","机构方案","总部方案","品质扣款","其它税前加扣款","其它跟单加扣款");
            }
        }else if ("SELF".equals(versionType)){
            if ("SWS2".equals(rankSeqCode)){
                return Arrays.asList("首年佣金","续年佣金","展业津贴","继续率奖金","赋能津贴","当月扣押品质保证金","上年品质保证金","机构方案","总部方案",
                        "首年佣金加扣款","续年佣金加扣款","展业津贴加扣款","继续率奖金加扣款","赋能津贴加扣款","当月扣押品质保证金加扣款","上年品质保证金加扣款","现金记录","其它税前加扣款");
            }else if ("FWMC1".equals(rankSeqCode)){
                return Arrays.asList("首年佣金","续年佣金","服务津贴","服务津贴补发","展业津贴","社保补贴","继续率奖金","当月扣押品质保证金","上年品质保证金","机构方案","总部方案",
                        "首年佣金加扣款","续年佣金加扣款","服务津贴加扣款","展业津贴加扣款","社保补贴加扣款","继续率奖金加扣款","当月扣押品质保证金加扣款","上年品质保证金加扣款","现金记录","其它税前加扣款");
            }
        }else if ("SELF_LOTUS".equals(versionType)){
            if ("RLP".equals(rankSeqCode)){
                if ("RLP01".equals(rankCode)){
                    if (StringUtils.isEmpty(preFlag)){
                        return Arrays.asList("新单销售奖金","续期服务奖金","团单销售佣金","个人年终奖","推荐奖金","机构方案","总部方案","其它税前加扣款","运营基金");
                    }else{
                        return Arrays.asList("新单销售奖金","续期服务奖金","团单销售佣金","个人年终奖","推荐奖金","运营基金");
                    }
                }else
                if (StringUtils.isEmpty(preFlag)){
                    return Arrays.asList("新单销售奖金","续期服务奖金","团单销售佣金","个人年终奖","推荐奖金","社保奖励","社保奖励补足","年终红利","机构方案","总部方案","其它税前加扣款","运营基金");
                }else{
                    return Arrays.asList("新单销售奖金","续期服务奖金","团单销售佣金","个人年终奖","推荐奖金","社保奖励","社保奖励补足","年终红利","运营基金");
                }
            }else if ("OM".equals(rankSeqCode)){
                if (StringUtils.isEmpty(preFlag)){
                    return Arrays.asList("新单销售奖金","续期服务奖金","团单销售佣金","个人年终奖","推荐奖金","社保奖励","社保奖励补足","年终红利","管理奖金","运营基金","育成奖金","机构方案","总部方案","其它税前加扣款");
                }else{
                    return Arrays.asList("新单销售奖金","续期服务奖金","团单销售佣金","个人年终奖","推荐奖金","社保奖励","社保奖励补足","年终红利","管理奖金","运营基金","育成奖金");
                }

            }
        }
        return new ArrayList<>();
    }


    private List<CommissionItemVO> initCommissionItemList(String versionType) {
        List<CommissionItemVO> commissionItemVOList = new ArrayList<>();
        if ("SELF_2024".equals(versionType)){
            CommissionItemVO commissionItemVO = new CommissionItemVO();
            commissionItemVO.setCommissionItem("FYC");
            commissionItemVO.setCommissionItemName("直接佣金");
        }
        return commissionItemVOList;
    }

    private void getAmount(PersonalIncomeVO incomeVO, List<CommissionItemVO> monthCommissionItemVOList) {
        if (CollectionUtils.isEmpty(monthCommissionItemVOList)){
            return;
        }
        monthCommissionItemVOList.stream().filter(o -> "收入".equals(o.getCommissionType())).findFirst()
                .ifPresent(commissionItemVO -> {
                    incomeVO.setAmountPreTax(commissionItemVO.getAmountPreTax());
                    incomeVO.setTotalPaidAmount(commissionItemVO.getTotalPaidAmount());
        });
    }

    private PersonalIncomeVO initPersonalIncomeVO(String settleMonth) {
        PersonalIncomeVO personalIncomeVO = new PersonalIncomeVO();
        personalIncomeVO.setMonth(settleMonth);
        personalIncomeVO.setAmountPreTax(BigDecimal.ZERO);
        personalIncomeVO.setTotalPaidAmount(BigDecimal.ZERO);
        personalIncomeVO.setCommissionItemList(new ArrayList<>());
        personalIncomeVO.setCommissionTypeList(new ArrayList<>());
        return personalIncomeVO;
    }

    @Override
    public List<CommissionItemVO> getCommissionItemList() {
        CommissionItemVO commissionItemVO = new CommissionItemVO();
        commissionItemVO.setCommissionItem("FYC");
        commissionItemVO.setCommissionItemName("首年佣金");
        commissionItemVO.setAmount(BigDecimal.valueOf(Math.random()*100).setScale(2, RoundingMode.HALF_UP));
        CommissionItemVO commissionItemVO2 = new CommissionItemVO();
        commissionItemVO2.setCommissionItem("RYC");
        commissionItemVO2.setCommissionItemName("续年佣金");
        commissionItemVO2.setAmount(BigDecimal.valueOf(Math.random()*100).setScale(2, RoundingMode.HALF_UP));
        CommissionItemVO commissionItemVO3 = new CommissionItemVO();
        commissionItemVO3.setCommissionItem("MPA");
        commissionItemVO3.setCommissionItemName("月度绩效津贴");
        commissionItemVO3.setAmount(BigDecimal.valueOf(Math.random()*100).setScale(2, RoundingMode.HALF_UP));
        CommissionItemVO commissionItemVO4 = new CommissionItemVO();
        commissionItemVO4.setCommissionItem("MPARA");
        commissionItemVO4.setCommissionItemName("月度绩效津贴季度补发");
        commissionItemVO4.setAmount(BigDecimal.valueOf(Math.random()*100).setScale(2, RoundingMode.HALF_UP));
        CommissionItemVO commissionItemVO5 = new CommissionItemVO();
        commissionItemVO5.setCommissionItem("EPA");
        commissionItemVO5.setCommissionItemName("增员津贴");
        commissionItemVO5.setAmount(BigDecimal.valueOf(Math.random()*100).setScale(2, RoundingMode.HALF_UP));
        return Arrays.asList(commissionItemVO,commissionItemVO2,commissionItemVO3,commissionItemVO4,commissionItemVO5);
    }

    @Override
    public List<CommissionItemVO> getCommissionTypeList() {
        CommissionItemVO commissionItemVO = new CommissionItemVO();
        commissionItemVO.setCommissionItemName("个人销售利益类");
        commissionItemVO.setAmount(BigDecimal.valueOf(Math.random()*100).setScale(2, RoundingMode.HALF_UP));
        commissionItemVO.setRate(new BigDecimal("0.50"));
        CommissionItemVO commissionItemVO2 = new CommissionItemVO();
        commissionItemVO2.setCommissionItemName("管理利益类");
        commissionItemVO2.setAmount(BigDecimal.valueOf(Math.random()*100).setScale(2, RoundingMode.HALF_UP));
        commissionItemVO2.setRate(new BigDecimal("0.10"));
        CommissionItemVO commissionItemVO3 = new CommissionItemVO();
        commissionItemVO3.setCommissionItemName("续期利益类");
        commissionItemVO3.setAmount(BigDecimal.valueOf(Math.random()*100).setScale(2, RoundingMode.HALF_UP));
        commissionItemVO3.setRate(new BigDecimal("0.20"));
        CommissionItemVO commissionItemVO4 = new CommissionItemVO();
        commissionItemVO4.setCommissionItemName("方案类");
        commissionItemVO4.setAmount(BigDecimal.valueOf(Math.random()*100).setScale(2, RoundingMode.HALF_UP));
        commissionItemVO4.setRate(new BigDecimal("0.20"));
        CommissionItemVO commissionItemVO5 = new CommissionItemVO();
        commissionItemVO5.setCommissionItemName("其它类");
        commissionItemVO5.setAmount(BigDecimal.valueOf(Math.random()*100).setScale(2, RoundingMode.HALF_UP));
        commissionItemVO5.setRate(new BigDecimal("0"));
        return Arrays.asList(commissionItemVO,commissionItemVO2,commissionItemVO3,commissionItemVO4,commissionItemVO5);
    }

    @Override
    public List<PersonalIncomeVO> getSettleIncomeTrendInfo() {
        String currentLoginEmpCode = getCurrentLoginEmpCode();
        log.info("获取个人收入月度趋势信息,{}", currentLoginEmpCode);
        if(StringUtils.isEmpty(currentLoginEmpCode)){
            return new ArrayList<>();
        }
        //获取月度列表与初始化
        List<PersonalIncomeVO> personalIncomeVOList = new ArrayList<>();
        List<String> monthList = new ArrayList<>();
        LocalDate now = LocalDate.now();
        for (int i = 5; i >= 0; i--) {
            LocalDate localDate = now.minusMonths(i);
            String settleMonth = localDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            PersonalIncomeVO personalIncomeVO = new PersonalIncomeVO();
            personalIncomeVO.setMonth(settleMonth);
            personalIncomeVOList.add(personalIncomeVO);
            monthList.add(settleMonth);
        }

        //获取当前数据
        List<PersonalIncomeVO>  currentIncomeVOList = performanceDao.getMonthsSettleIncomeInfo(currentLoginEmpCode,monthList);
        if (!CollectionUtils.isEmpty(currentIncomeVOList)){
            for (PersonalIncomeVO personalIncomeVO : personalIncomeVOList) {
                currentIncomeVOList.stream().filter(o -> personalIncomeVO.getMonth().equals(o.getMonth())).findFirst().ifPresent(o -> {
                    personalIncomeVO.setAmountPreTax(o.getAmountPreTax());
                    personalIncomeVO.setTotalPaidAmount(o.getTotalPaidAmount());
                });
            }
        }

        //获取对应的上年数据
        List<String> lastYearMonthList = new ArrayList<>();
        for (int i = 5; i >= 0; i--) {
            LocalDate localDate = now.minusYears(1).minusMonths(i);
            String settleMonth = localDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            lastYearMonthList.add(settleMonth);
        }
        List<PersonalIncomeVO>  lastYearIncomeVOList = performanceDao.getMonthsSettleIncomeInfo(currentLoginEmpCode, lastYearMonthList);
        if (!CollectionUtils.isEmpty(lastYearIncomeVOList)){
            for (PersonalIncomeVO personalIncomeVO : personalIncomeVOList) {
                lastYearIncomeVOList.stream().filter(o -> personalIncomeVO.getMonth().substring(5).equals(o.getMonth().substring(5))).findFirst().ifPresent(o -> {
                    personalIncomeVO.setLastYearTotalPaidAmount(o.getTotalPaidAmount());
                });
            }
        }


        return personalIncomeVOList;

//        List<PersonalIncomeVO>  personalIncomeVOList = performanceDao.getMonthsSettleIncomeInfo(monthList);
//        PersonalIncomeVO personalIncomeVO1 = new PersonalIncomeVO();
//        personalIncomeVO1.setMonth("2024-02");
//        personalIncomeVO1.setAmountPreTax(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        personalIncomeVO1.setTotalPaidAmount(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        personalIncomeVO1.setLastYearTotalPaidAmount(null);
//        PersonalIncomeVO personalIncomeVO2 = new PersonalIncomeVO();
//        personalIncomeVO2.setMonth("2024-03");
//        personalIncomeVO2.setAmountPreTax(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        personalIncomeVO2.setTotalPaidAmount(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        personalIncomeVO2.setLastYearTotalPaidAmount(null);
//        PersonalIncomeVO personalIncomeVO3 = new PersonalIncomeVO();
//        personalIncomeVO3.setMonth("2024-04");
//        personalIncomeVO3.setAmountPreTax(null);
//        personalIncomeVO3.setTotalPaidAmount(null);
//        personalIncomeVO3.setLastYearTotalPaidAmount(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        PersonalIncomeVO personalIncomeVO4 = new PersonalIncomeVO();
//        personalIncomeVO4.setMonth("2024-05");
//        personalIncomeVO4.setAmountPreTax(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        personalIncomeVO4.setTotalPaidAmount(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        personalIncomeVO4.setLastYearTotalPaidAmount(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        PersonalIncomeVO personalIncomeVO5 = new PersonalIncomeVO();
//        personalIncomeVO5.setMonth("2024-06");
//        personalIncomeVO5.setAmountPreTax(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        personalIncomeVO5.setTotalPaidAmount(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        personalIncomeVO5.setLastYearTotalPaidAmount(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        PersonalIncomeVO personalIncomeVO6 = new PersonalIncomeVO();
//        personalIncomeVO6.setMonth("2024-07");
//        personalIncomeVO6.setAmountPreTax(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        personalIncomeVO6.setTotalPaidAmount(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        personalIncomeVO6.setLastYearTotalPaidAmount(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        return Arrays.asList(personalIncomeVO1,personalIncomeVO2,personalIncomeVO3,personalIncomeVO4,personalIncomeVO5,personalIncomeVO6);
    }

    @Override
    public PersonalIncomeVO getSettleIncomeInfo(String settleMonth) {
        String currentLoginEmpCode = getCurrentLoginEmpCode();
        log.info("获取个人收入已结算信息,{}",currentLoginEmpCode);
        if (StringUtils.isEmpty(currentLoginEmpCode)){
            return null;
        }
        List<CommissionItemVO> preSettleCommissionVOList = performanceDao.getSettleCommissionVOList(currentLoginEmpCode,settleMonth);
        if (CollectionUtils.isEmpty(preSettleCommissionVOList)){
            return null;
        }

        PersonalIncomeVO incomeVO = initPersonalIncomeVO(settleMonth);
        String versionType = preSettleCommissionVOList.get(0).getVersionType();
        String rankSeqCode = preSettleCommissionVOList.get(0).getRankSeqCode();
        String rankCode = preSettleCommissionVOList.get(0).getRankCode();
        incomeVO.setMonth(preSettleCommissionVOList.get(0).getSettleMonth());
        //获取当月金额数据
        getAmount(incomeVO,preSettleCommissionVOList);
        //获取当月佣金项数据
        getCommissionItemVoList(incomeVO,preSettleCommissionVOList,versionType, rankSeqCode,rankCode, null);
        //获取当月科目项数据
        getCommissionVoTypeList(incomeVO,preSettleCommissionVOList,versionType, rankSeqCode, null);
        //非空处理
        zeroIfNull(incomeVO);
        return incomeVO;
//        PersonalIncomeVO incomeVO = new PersonalIncomeVO();
//        incomeVO.setMonth("2024-05");
//        incomeVO.setAmountPreTax(BigDecimal.valueOf(Math.random() * 100).setScale(2, RoundingMode.HALF_UP));
//        incomeVO.setTotalPaidAmount(BigDecimal.valueOf(Math.random() * 100).setScale(2, RoundingMode.HALF_UP));
//        incomeVO.setCommissionItemList(getCommissionItemList());
//        incomeVO.setCommissionTypeList(getCommissionTypeList());
//        incomeVO.setSettleBatchId(UUID.randomUUID().toString());
    }

    private void zeroIfNull(Object o) {
        for ( Field f : o.getClass().getDeclaredFields() ) {
            f.setAccessible(true);
            try {
                if ( f.getType().equals( BigDecimal.class ) ) {
                    BigDecimal value = (BigDecimal) f.get( o );
                    if ( value == null) {
                        f.set(o , BigDecimal.ZERO);
                    }else{
                        f.set(o , value.setScale(2, RoundingMode.HALF_UP));
                    }
                }
            } catch ( Exception e ) {
                throw new RuntimeException(e);
            }
        }
    }

    @Override
    public List<PerformancePolicyVO> getSettlePolicyInfo(String commissionItem, String settleMonth, String queryType) {
        String currentLoginEmpCode = getCurrentLoginEmpCode();
        log.info("个人收入保单明细,{}",currentLoginEmpCode);
        if (StringUtils.isEmpty(currentLoginEmpCode)){
            return null;
        }
        if (StringUtils.isEmpty(commissionItem)){
            log.info("个人收入保单查询缺少佣金项");
            return new ArrayList<>();
        }
        List<PerformancePolicyVO> performancePolicyVOList = new ArrayList<>();
        //获取结算数据
        PersonalIncomeVO incomeVO = getSettleIncomeAmountInfo(currentLoginEmpCode,settleMonth,null);
        if (ObjectUtils.isEmpty(incomeVO)){
            return performancePolicyVOList;
        }
        String settleBatchId = incomeVO.getSettleBatchId();
        String versionId = incomeVO.getVersionId();
        if ("PRE".equals(settleBatchId) || StringUtils.isEmpty(settleBatchId)){
            //预结算保单获取
            //判断是否为首月
            PersonalIncomeVO firstMonthIncomeVO = getSettleIncomeAmountInfo(currentLoginEmpCode,null,"PRE");
            performancePolicyVOList = performanceDao.getPreSettlePolicyInfo(currentLoginEmpCode,commissionItem,settleMonth,firstMonthIncomeVO.getMonth().equals(settleMonth),versionId,queryType);
            performancePolicyVOList.forEach(o->{
                o.setSettleFlag("未结算");
                o.setPremium(o.getPremium()==null?null:o.getPremium().setScale(2, RoundingMode.HALF_UP));
                o.setAmount(o.getAmount()==null?null:o.getAmount().setScale(2, RoundingMode.HALF_UP));
            });
        }else{
            //已结算保单获取
            String versionType = incomeVO.getVersionType();
            performancePolicyVOList = performanceDao.getSettlePolicyInfo(currentLoginEmpCode,commissionItem,settleMonth,settleBatchId,versionType,queryType);
            performancePolicyVOList.forEach(o->{
                o.setSettleFlag("已结算");
                o.setPremium(o.getPremium()==null?null:o.getPremium().setScale(2, RoundingMode.HALF_UP));
                o.setAmount(o.getAmount()==null?null:o.getAmount().setScale(2, RoundingMode.HALF_UP));
            });
        }
        //按保单号以及主附加险排重
        performancePolicyVOList = transByPolicyNo(performancePolicyVOList);
        return performancePolicyVOList;
//        for (int i = 0; i < 4; i++) {
//            PerformancePolicyVO performancePolicyVO = new PerformancePolicyVO();
//            performancePolicyVO.setCommissionItem(commissionItem);
//            performancePolicyVO.setPolicyNo(UUID.randomUUID().toString());
//            settleBatchId.setPaymentYears("30年");
//            performancePolicyVO.setInsureYears("30年");
//            performancePolicyVO.setPolicyState("有效");
//            performancePolicyVO.setSettleDate(new Date());
//            performancePolicyVO.setRiskPeriod(i<1?"M":"L");
//            performancePolicyVO.setRiskPeriodName(i<1?"短险":"长险");
//            performancePolicyVO.setSettleFlag("已结算");
//            performancePolicyVO.setQqdFlag("已续缴");
//            performancePolicyVO.setQqdDate(new Date());
//            performancePolicyVO.setRiskCode(UUID.randomUUID().toString());
//            performancePolicyVO.setRiskName("险种名称");
//            performancePolicyVO.setAppntName("投保人名称");
//            performancePolicyVO.setInsuredName("被保人名称");
//            performancePolicyVO.setAmount(BigDecimal.valueOf(Math.random() * 100).setScale(2, RoundingMode.HALF_UP));
//            performancePolicyVOList.add(performancePolicyVO);
//        }

    }

    private List<PerformancePolicyVO> transByPolicyNo(List<PerformancePolicyVO> performancePolicyVOList) {
        if (CollectionUtils.isEmpty(performancePolicyVOList)){
            return performancePolicyVOList;
        }
        List<PerformancePolicyVO> list = new ArrayList<>();
        Map<String, List<PerformancePolicyVO>> policyNoMap = performancePolicyVOList.stream().peek(o-> {
            if ("ZP".equals(o.getSource()) && StringUtils.isEmpty(o.getSubRiskFlag())) {
                o.setSubRiskFlag("M");
            }
        }).filter(o -> !StringUtils.isEmpty(o.getPolicyNo())).collect(Collectors.groupingBy(PerformancePolicyVO::getPolicyNo));
        for (String policyNo : policyNoMap.keySet()) {
            List<PerformancePolicyVO> policyVOList = policyNoMap.get(policyNo);
            List<PerformancePolicyVO> MVOList = policyVOList.stream().filter(o -> "M".equals(o.getSubRiskFlag())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(MVOList)){
                PerformancePolicyVO policyVO = new PerformancePolicyVO();
                if (MVOList.size()>1){
                    List<PerformancePolicyVO> MLVOList = MVOList.stream().filter(o -> "L".equals(o.getRiskPeriod())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(MLVOList)){
                        BeanUtils.copyProperties(MLVOList.get(0), policyVO);
                    }else{
                        BeanUtils.copyProperties(MVOList.get(0), policyVO);
                    }
                    policyVO.setPolicyNum(MVOList.size());
                }else{
                    BeanUtils.copyProperties(MVOList.get(0), policyVO);
                }
                policyVO.setAmount(policyVOList.stream().map(o->ObjectUtils.isEmpty(o.getAmount())?BigDecimal.ZERO:o.getAmount()).reduce(BigDecimal.ZERO,BigDecimal::add));
                policyVO.setPremium(policyVOList.stream().map(o->ObjectUtils.isEmpty(o.getPremium())?BigDecimal.ZERO:o.getPremium()).reduce(BigDecimal.ZERO,BigDecimal::add));
                policyVO.setInsuranceAmount(policyVOList.stream().map(o->ObjectUtils.isEmpty(o.getInsuranceAmount())?BigDecimal.ZERO:o.getInsuranceAmount()).reduce(BigDecimal.ZERO,BigDecimal::add));
                list.add(policyVO);
            }
        }
        return list;
    }

    private PersonalIncomeVO getSettleIncomeAmountInfo(String currentLoginEmpCode, String settleMonth, String settleBatchId) {
        return performanceDao.getSettleIncomeAmountInfo(currentLoginEmpCode,settleMonth,settleBatchId);
    }

    @Override
    public PersonalVO getCurrentMonthPerformanceInfo(String empCode) {
        PersonalVO personalVO = new PersonalVO();
        if (StringUtils.isEmpty(empCode)){
            empCode = getCurrentLoginEmpCode();
            if (StringUtils.isEmpty(empCode)){
                log.info("校验失败,未获取到当前登录人员信息");
                return personalVO;
            }
        }else if (!checkEmpAccess(empCode, null)){
            log.info("查询失败,权限不足:{}",empCode);
            return personalVO;
        }
        log.info("个人当月业绩查询,{}", empCode);
        String performanceMonth = new SimpleDateFormat("yyyy-MM").format(new Date());
        personalVO.setCurrentMonthPerformance(getEmpPerformance(empCode, performanceMonth));
        PersonalVO empCr = performanceDao.getEmpCr(empCode, performanceMonth);
        if (!ObjectUtils.isEmpty(empCr)){
            String cr13 = empCr.getCr13();
            if (StringUtils.isEmpty(cr13)){
                return null;
            }
            personalVO.setCurrentMonthCr(new BigDecimal(cr13).multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP));
        }


//        personalVO.setCurrentMonthPerformance(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        personalVO.setCurrentMonthCr(BigDecimal.valueOf(Math.random()*100).setScale(2, RoundingMode.HALF_UP));
        return personalVO;
    }

    @Override
    public PerformanceVO getPerformanceFycMonthInfo(String empCode, String performanceMonth) {
        PerformanceVO performanceVO = new PerformanceVO();
        if (StringUtils.isEmpty(empCode)){
            empCode = getCurrentLoginEmpCode();
            if (StringUtils.isEmpty(empCode)){
                log.info("校验失败,未获取到当前登录人员信息");
                return performanceVO;
            }
        }else if (!checkEmpAccess(empCode,performanceMonth)){
            log.info("查询失败,权限不足:{}",empCode);
            return performanceVO;
        }
        log.info("个人当月业绩查询,{}", empCode);
        String finalEmpCode = empCode;
        CompletableFuture<String> versionTypeFuture = CompletableFuture.supplyAsync(() -> {
            return getBasicLawVersionType(finalEmpCode, null,performanceMonth);
        });
        //判断是否当月
        boolean isCurrentMonth = performanceMonth.equals(new SimpleDateFormat("yyyy-MM").format(new Date()));
        List<String> wtPolNoList = new ArrayList<>();
        if (isCurrentMonth){
            //获取当天ES承保数据
            getEsCurrentDayInfo(empCode, performanceMonth, performanceVO);
            //获取当天犹退单投保单号
            wtPolNoList = getEsCurrentDayWtPolNoList(empCode, performanceMonth);
        }
        //获取销管业绩数据
        getPerformanceFycInfo(empCode, performanceMonth, wtPolNoList, performanceVO);
        performanceVO.setVersionType(versionTypeFuture.join());
        return performanceVO;
    }

    private void getEsCurrentDayInfo(String empCode, String performanceMonth, PerformanceVO performanceVO) {
        //获取ES当天数据
        List<PolicyEsVo>  policyEsVoList = getEsCurrentPolicyList(empCode, null, null, null);
        if (!CollectionUtils.isEmpty(policyEsVoList)){
            //添加今日承保数据
            performanceVO.setFycPolicyNum(policyEsVoList.size());
            performanceVO.setFycPremium(policyEsVoList.stream().map(PolicyEsVo::getPrem).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
    }

    private void getPerformanceFycInfo(String empCode, String performanceMonth, List<String> wtPolNoList, PerformanceVO performanceVO) {
        List<PerformancePolicyVO> policyVOList= performanceDao.getPerformanceMonthPolicyInfo(empCode, performanceMonth, wtPolNoList, "FYC", null);
        if (!CollectionUtils.isEmpty(policyVOList)){
//            performanceVO.setFycPolicyNum(addInteger(performanceVO.getFycPolicyNum(),getPolicyVoPolicyNum(policyVOList,"FYC")));
            performanceVO.setFycPolicyNum(addInteger(performanceVO.getFycPolicyNum(),(int)policyVOList.stream().map(PerformancePolicyVO::getPolicyNo).distinct().count()));
            performanceVO.setFycPremium(addBigDecimal(performanceVO.getFycPremium(),policyVOList.stream().map(PerformancePolicyVO::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add)));
            performanceVO.setFycAmount(policyVOList.stream().map(PerformancePolicyVO::getFyc).reduce(BigDecimal.ZERO, BigDecimal::add));
            performanceVO.setDcp(policyVOList.stream().map(PerformancePolicyVO::getDcp).reduce(BigDecimal.ZERO, BigDecimal::add));
            performanceVO.setFscAmount(policyVOList.stream().map(PerformancePolicyVO::getFsc).reduce(BigDecimal.ZERO, BigDecimal::add));
//            performanceVO.setRscAmount(policyVOList.stream().map(PerformancePolicyVO::getRsc).reduce(BigDecimal.ZERO, BigDecimal::add));
            performanceVO.setGscAmount(policyVOList.stream().map(PerformancePolicyVO::getGsc).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
    }


    private Integer getPolicyVoPolicyNum(List<PerformancePolicyVO> performancePolicyVOList, String type) {
        Integer Num = 0;
        if ("FYC".equals(type)){
            return getSourceInteger(performancePolicyVOList, Num);
        }else if ("QQD".equals(type)){
            return getInteger(performancePolicyVOList, Num);
        }else if ("YT".equals(type)){
            return ((Long)performancePolicyVOList.stream().map(PerformancePolicyVO::getPolicyNo).distinct().count()).intValue();
        }else if ("ZP".equals(type)){
            return ((Long)performancePolicyVOList.stream().map(PerformancePolicyVO::getEdoraccepto).distinct().count()).intValue();
        }
        return 0;
    }

    private Integer getSourceInteger(List<PerformancePolicyVO> performancePolicyVOList, Integer num) {
        Map<String, Map<String, List<PerformancePolicyVO>>> collect = performancePolicyVOList.stream().collect(Collectors.groupingBy(PerformancePolicyVO::getSource, Collectors.groupingBy(PerformancePolicyVO::getPolicyNo)));
        for (String source : collect.keySet()) {
            Map<String, List<PerformancePolicyVO>> policyNoMap = collect.get(source);
            for (String policyNo : policyNoMap.keySet()) {
                List<PerformancePolicyVO> policyVOList = policyNoMap.get(policyNo);
                long pos = policyVOList.stream().filter(o -> ObjectUtils.isEmpty(o.getPremium()) || o.getPremium().compareTo(BigDecimal.ZERO) >= 0).count();
                long neg = policyVOList.size() - pos;
                if (pos>neg){
                    num++;
                }else if (pos<neg){
                    num--;
                }
            }
        }
        return num;
    }

    private Integer getInteger(List<PerformancePolicyVO> performancePolicyVOList, Integer Num) {
        Map<String, List<PerformancePolicyVO>> policyNoMap = performancePolicyVOList.stream().collect(Collectors.groupingBy(PerformancePolicyVO::getPolicyNo));
        for (String policyNo : policyNoMap.keySet()) {
            List<PerformancePolicyVO> policyVOList = policyNoMap.get(policyNo);
            long pos = policyVOList.stream().filter(o -> ObjectUtils.isEmpty(o.getPremium()) || o.getPremium().compareTo(BigDecimal.ZERO) >= 0).count();
            long neg = policyVOList.size() - pos;
            if (pos>neg){
                Num++;
            }else if (pos<neg){
                Num--;
            }
        }
        return Num;
    }

    private Integer addInteger(Integer num1, Integer num2) {
        if (ObjectUtils.isEmpty(num1)){
            num1=0;
        }
        if (ObjectUtils.isEmpty(num2)){
            num2=0;
        }
        return num1+num2;
    }

    private BigDecimal addBigDecimal(BigDecimal num1, BigDecimal num2) {
        if (ObjectUtils.isEmpty(num1)){
            num1=BigDecimal.ZERO;
        }
        if (ObjectUtils.isEmpty(num2)){
            num2=BigDecimal.ZERO;
        }
        return num1.add(num2);
    }

    private List<PolicyEsVo> getEsCurrentPolicyList(String empCode, List<String> agentCodeList, List<String> policyNoList, String appntName) {
        if (!StringUtils.isEmpty(empCode) || !CollectionUtils.isEmpty(agentCodeList)){
            return new ArrayList<>();
        }
        //查询业绩
        PolicyEsQueryRequestVo build = PolicyEsQueryRequestVo.builder()
                .appFlag("1")
                .signDateStartTime(LocalDateTimeUtil.beginOfDay(LocalDateTime.now()))
                .signDateEndTime(LocalDateTimeUtil.endOfDay(LocalDateTime.now()))
                .from(0)
                .size(999999)
                .esSortVoList(Collections.singletonList(EsSortVo.builder().field("signDateTime").sortValue("ASC").build()))
                .fields(filter)
                .build();
        if (!StringUtils.isEmpty(empCode)){
            build.setAgentCodeList(Collections.singletonList(empCode));
        }else if (CollectionUtils.isEmpty(agentCodeList) && CollectionUtils.isEmpty(policyNoList)){
            return new ArrayList<>();
        }
        if (!StringUtils.isEmpty(appntName)){
            build.setAppntName(appntName);
        }
        if (!CollectionUtils.isEmpty(agentCodeList)){
            build.setAgentCodeList(agentCodeList);
        }
        if (!CollectionUtils.isEmpty(policyNoList)){
            build.setContNoList(policyNoList);
        }
        log.info("查询ES,{}",JSON.toJSON(build));
        EsQueryResponseVo esQueryResponseVo = policyEsApi.queryPolicyList(build);
        if (null == esQueryResponseVo || CollectionUtils.isEmpty(esQueryResponseVo.getDataList())) {
            log.info("policy没有返回值");
            return null;
        }
        List<PolicyEsVo> dataList = esQueryResponseVo.getDataList();
        dataList.forEach(o -> {
            log.info("policy返回值:{}", o.toString());
        });
        return dataList;
    }

    @Override
    public PerformanceVO getPerformanceRycMonthInfo(String empCode, String performanceMonth) {
        PerformanceVO performanceVO = new PerformanceVO();
        if (StringUtils.isEmpty(empCode)){
            empCode = getCurrentLoginEmpCode();
            if (StringUtils.isEmpty(empCode)){
                log.info("校验失败,未获取到当前登录人员信息");
                return performanceVO;
            }
        }else if (!checkEmpAccess(empCode, performanceMonth)){
            log.info("查询失败,权限不足:{}",empCode);
            return performanceVO;
        }
        log.info("个人当月业绩查询,{}", empCode);
//        performanceVO.setFycPolicyNum(10);
//        performanceVO.setFycPremium(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        performanceVO.setFycAmount(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        //判断是否当月
//        boolean isCurrentMonth = performanceMonth.equals(new SimpleDateFormat("yyyy-MM").format(new Date()));
//        //获取ES当天数据
//        List<PolicyEsVo> policyEsVoList = new ArrayList<>();
//        if (isCurrentMonth){
//            policyEsVoList = getEsCurrentPolicyList(empCode, performanceMonth);
//            if (!CollectionUtils.isEmpty(policyEsVoList)){
//                //筛选承保单和犹豫退单
//
//            }
//        }
        //获取销管业绩数据
        getPerformanceRYCInfo(empCode, performanceMonth, null, performanceVO);
        return performanceVO;
//        PerformanceVO performanceVO = new PerformanceVO();
//        performanceVO.setRycActualNum(5);
//        performanceVO.setRycActualPremium(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        performanceVO.setRycAmount(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        performanceVO.setRycNum(5);
//        performanceVO.setRycPremium(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        performanceVO.setZPRycNum(5);
//        performanceVO.setZPRycPremium(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        performanceVO.setZPRycAmount(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//        return performanceVO;
    }

    private void getPerformanceRYCInfo(String empCode, String performanceMonth, List<String> wtPolNoList, PerformanceVO performanceVO) {
        List<PerformancePolicyVO> policyVOList= performanceDao.getPerformanceMonthPolicyInfo(empCode, performanceMonth, wtPolNoList, "RYC", null);
        if (!CollectionUtils.isEmpty(policyVOList)){
            List<PerformancePolicyVO> QQDList = policyVOList.stream().filter(o -> "QQD".equals(o.getSource())).collect(Collectors.toList());
            List<PerformancePolicyVO> YTList = policyVOList.stream().filter(o -> "YT".equals(o.getSource())).collect(Collectors.toList());
            List<PerformancePolicyVO> ZPList = policyVOList.stream().filter(o -> "ZP".equals(o.getSource())).collect(Collectors.toList());
            performanceVO.setRscAmount(policyVOList.stream().map(PerformancePolicyVO::getRsc).reduce(BigDecimal.ZERO, BigDecimal::add));
            performanceVO.setRycActualNum(0);
            performanceVO.setRycActualPremium(BigDecimal.ZERO);
            performanceVO.setRycAmount(BigDecimal.ZERO);
            performanceVO.setRycNum(0);
            performanceVO.setRycPremium(BigDecimal.ZERO);
            performanceVO.setZPRycNum(0);
            performanceVO.setZPRycPremium(BigDecimal.ZERO);
            performanceVO.setZPRycAmount(BigDecimal.ZERO);
            if (!CollectionUtils.isEmpty(QQDList)){
                Integer qqdNum = getPolicyVoPolicyNum(QQDList, "QQD");
                performanceVO.setRycActualNum(qqdNum);
                performanceVO.setRycNum(qqdNum);
                performanceVO.setRycActualPremium(QQDList.stream().map(PerformancePolicyVO::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add));
                performanceVO.setRycPremium(performanceVO.getRycActualPremium());
                performanceVO.setRycAmount(QQDList.stream().map(PerformancePolicyVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            if (!CollectionUtils.isEmpty(YTList)){
                Integer ytNum = getPolicyVoPolicyNum(YTList, "YT");
                performanceVO.setRycNum(performanceVO.getRycNum()+ ytNum);
                performanceVO.setRycPremium(performanceVO.getRycPremium().add(YTList.stream().map(PerformancePolicyVO::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add)));
            }
            if (!CollectionUtils.isEmpty(ZPList)){
                Integer zpNum = getPolicyVoPolicyNum(ZPList, "ZP");
                performanceVO.setZPRycNum(zpNum);
                performanceVO.setZPRycPremium(ZPList.stream().map(PerformancePolicyVO::getPremium).reduce(BigDecimal.ZERO, BigDecimal::add));
                performanceVO.setZPRycAmount(ZPList.stream().map(PerformancePolicyVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
        }
    }

    @Override
    public PerformanceVO getPerformanceCr13Info(String empCode, String performanceMonth) {
        PerformanceVO performanceVO = new PerformanceVO();
        if (StringUtils.isEmpty(empCode)){
            empCode = getCurrentLoginEmpCode();
            if (StringUtils.isEmpty(empCode)){
                log.info("校验失败,未获取到当前登录人员信息");
                return performanceVO;
            }
        }else if (!checkEmpAccess(empCode, performanceMonth)){
            log.info("查询失败,权限不足:{}",empCode);
            return performanceVO;
        }
        //获取继续率
        log.info("个人继续率查询,{}", empCode);
        PersonalVO empCr = performanceDao.getEmpCr(empCode, performanceMonth);
        if (!ObjectUtils.isEmpty(empCr)){
            String cr13 = empCr.getCr13();
            if (StringUtils.isEmpty(cr13)){
                return null;
            }
            performanceVO.setCr13(new BigDecimal(cr13).multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP));
            String remark = empCr.getRemark();
            getCrDenAndNum(performanceVO, remark);
        }
        return performanceVO;
    }

    private void getCrDenAndNum(PerformanceVO performanceVO, String remark) {
        if (canJsonArray(remark)){
            List<ContinueRateInfo> continueRateInfoList = JSONArray.parseArray(remark, ContinueRateInfo.class);
            if (!CollectionUtils.isEmpty(continueRateInfoList)){
                List<ContinueRateInfo> r13List = continueRateInfoList.stream().filter(o -> !org.apache.commons.lang3.StringUtils.isEmpty(o.CONTINUE_RATE_CODE) && "R13".equals(o.CONTINUE_RATE_CODE)).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(r13List)){
                    ContinueRateInfo continueRateInfo = r13List.get(0);
                    if (!StringUtils.isEmpty(continueRateInfo.NUMERATOR)){
                        performanceVO.setCr13ActualPremium(new BigDecimal(continueRateInfo.NUMERATOR).setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                    if (!StringUtils.isEmpty(continueRateInfo.DENOMINATOR)){
                        performanceVO.setCr13Premium(new BigDecimal(continueRateInfo.DENOMINATOR).setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                }
            }
        }
    }

    private boolean canJsonArray(String remark) {
        try {
            JSONArray.parseArray(remark);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    @Override
    public List<PerformancePolicyVO> getPerformancePolicyInfo(String queryType, String performanceMonth) {
        List<PerformancePolicyVO> performancePolicyVOList = new ArrayList<>();
        String empCode = getCurrentLoginEmpCode();
        log.info("个人业绩保单查询,{}", empCode);
        if (StringUtils.isEmpty(empCode)){
            return performancePolicyVOList;
        }
        if (StringUtils.isEmpty(performanceMonth)){
            log.info("个人业绩保单查询缺少类型");
            return performancePolicyVOList;
        }
        //判断是否当月
        boolean isCurrentMonth = performanceMonth.equals(new SimpleDateFormat("yyyy-MM").format(new Date()));
        //获取ES当天数据
        List<PolicyEsVo> policyEsVoList = new ArrayList<>();
        List<String> wtPolNoList = new ArrayList<>();
        if (isCurrentMonth){
            if ("UW".equals(queryType)){
                policyEsVoList = getEsCurrentPolicyList(empCode, null, null, null);
                wtPolNoList = getEsCurrentDayWtPolNoList(empCode, performanceMonth);
            }
        }
        List<PerformancePolicyVO> performancePolicyInfoList = performanceDao.getPerformanceMonthPolicyInfo(empCode, performanceMonth, wtPolNoList, null, queryType);
        //转换添加ES今日承保数据
        performancePolicyInfoList.addAll(transEsVoTOPolicyVO(policyEsVoList));
        if (!CollectionUtils.isEmpty(performancePolicyInfoList)){
            //按保单号以及主附加险排重
            performancePolicyInfoList = transByPolicyNo(performancePolicyInfoList);
//            //附加险转换
//            List<PerformancePolicyVO>  subPolicyList = performancePolicyInfoList.stream().filter(o->"S".equals(o.getSubRiskFlag())).collect(Collectors.toList());
//            if (!CollectionUtils.isEmpty(subPolicyList)){
//                performancePolicyInfoList = performancePolicyInfoList.stream().filter(o->!"S".equals(o.getSubRiskFlag())).collect(Collectors.toList());
//                if (!CollectionUtils.isEmpty(performancePolicyInfoList)){
//                    for (PerformancePolicyVO subPolicy : subPolicyList) {
//                        performancePolicyInfoList.stream().filter(o -> o.getPolicyNo().equals(subPolicy.getPolicyNo()) && o.getSource().equals(subPolicy.getSource())).findFirst().ifPresent(o -> {
//                            o.setAmount(addBigDecimal(o.getAmount(), subPolicy.getAmount()));
//                            o.setPremium(addBigDecimal(o.getPremium(), subPolicy.getPremium()));
//                        });
//                    }
//                }
//            }
        }
        return performancePolicyInfoList;
    }

    private List<PerformancePolicyVO> transEsVoTOPolicyVO(List<PolicyEsVo> policyEsVoList) {
        if (CollectionUtils.isEmpty(policyEsVoList)){
            return new ArrayList<>();
        }
        List<PerformancePolicyVO>  policyVOList = policyEsVoList.stream().map(esVo -> {
            PerformancePolicyVO policyVO = new PerformancePolicyVO();
            policyVO.setPolicyNo(esVo.getContNo());
            policyVO.setPolicyState("承保");
            policyVO.setSettleFlag("未结算");
            policyVO.setAgentCode(esVo.getAgentCode());
            policyVO.setAgentName(esVo.getAgentName());
            policyVO.setPremium(esVo.getPrem());
            AppntEsVo appntEsVo = esVo.getAppntEsVo();
            if (!ObjectUtils.isEmpty(appntEsVo)){
                policyVO.setAppntName(appntEsVo.getName());
            }
            policyVO.setTradeType("承保收费");
            policyVO.setInsuranceAmount(esVo.getAmnt());
            List<RiskEsVo> riskVoList = esVo.getRiskVoList();
            if (!CollectionUtils.isEmpty(riskVoList)){
                policyVO.setRiskCode(riskVoList.get(0).getRiskCode());
                policyVO.setRiskName(riskVoList.get(0).getRiskName());
                policyVO.setSubRiskFlag(riskVoList.get(0).getPrimaryOrSecondary());
                String riskPeriod = riskVoList.get(0).getRiskPeriod();
                policyVO.setRiskPeriod(riskPeriod);
                if ("L".equals(riskPeriod)){
                    policyVO.setRiskPeriodName("长险");
                }else if ("M".equals(riskPeriod)){
                    policyVO.setRiskPeriodName("短险");
                }else if ("S".equals(riskPeriod)){
                    policyVO.setRiskPeriodName("极短险");
                }
                policyVO.setPaymentYears(getRiskVoPaymentYears(riskVoList.get(0)));
            }
            LocalDateTime signDateTime = esVo.getSignDateTime();
            if (!ObjectUtils.isEmpty(signDateTime)){
                policyVO.setSignDate(signDateTime.toLocalDate());
                policyVO.setSettleDate(signDateTime.toLocalDate());
                policyVO.setPerformanceDate(signDateTime.toLocalDate());
            }
            return policyVO;
        }).collect(Collectors.toList());
        return policyVOList;
    }

    private String getRiskVoPaymentYears(RiskEsVo riskEsVo) {
        /**
         * 缴费期间:
         * 如果 PAYINTV=0 那么，缴费期间= ‘趸交’
         * 否则 如果 PAYINTV in (‘1’,‘12’) 并且 PAYENDYEARFLAG = ‘Y’ 那么 缴费期间= PAYYEARS & “年”
         * 否则 如果 PAYINTV in (‘1’,‘12’) 并且 PAYENDYEARFLAG = ‘A’ 那么 缴费期间= “交至” & PAYENDYEAR & “岁”
         * PAYENDYEARFLAG = ‘M’ ，单位是“月”
         * else是“天”
         */
        String payIntv = riskEsVo.getPayIntv();
        BigDecimal payYears = riskEsVo.getPayYears();
        String payEndYear = riskEsVo.getPayEndYear();
        String payEndYearFlag = riskEsVo.getPayEndYearFlag();
        if ("0".equals(payIntv)){
            return "趸交";
        }else if (("1".equals(payIntv) || "12".equals(payIntv)) && "Y".equals(payEndYearFlag)){
            return payYears.toString() + "年";
        }else if (("1".equals(payIntv) || "12".equals(payIntv)) && "A".equals(payEndYearFlag)){
            return "交至" + payEndYear + "岁";
        }else if ("M".equals(payEndYearFlag)){
            return payEndYear + "月";
        }else {
            return payEndYear + "天";
        }
//        Integer payPeriod = riskEsVo.getPayPeriod();
//        if (ObjectUtils.isEmpty(payPeriod)){
//            return payPeriod+"年";
//        }else{
//            return null;
//        }
    }

    @Override
    public TeamManageVO getTeamCurrentPersonQuitList(String saleTeamCode) {
        TeamManageVO teamManageVO = new TeamManageVO();
        List<PersonalVO> personalVOList = performanceDao.getTeamCurrentPersonQuitList(saleTeamCode);
        teamManageVO.setPersonalVOList(personalVOList);
        return teamManageVO;
    }

    @Override
    public TeamManageVO getTeamCurrentPersonList(String saleTeamCode) {
        TeamManageVO teamManageVO = new TeamManageVO();
        log.info("获取团队团队在职人力,{},{}",saleTeamCode,getCurrentLoginEmpCode());
        if (!checkTeamAccess(saleTeamCode,teamManageVO)){
            log.info("当前登录代理人无权限访问团队");
            return teamManageVO;
        }
        //团队转换
        if (getCurrentLoginEmpCode().startsWith("S")){
            Tbsaleteam saleTeam = saleTeamDao.getTeamSaleTeam(saleTeamCode);
            if (ObjectUtils.isEmpty(saleTeam)){
                log.info("督导账号查询组级团队失败,{}",saleTeamCode);
                return teamManageVO;
            }
            saleTeamCode = saleTeam.getSaleteamcode();
        }
        teamManageVO.setPersonNum(0);
        List<PersonalVO> personalVOList = performanceDao.getTeamCurrentPersonList(saleTeamCode);
        if (!CollectionUtils.isEmpty(personalVOList)){
            teamManageVO.setPersonNum(personalVOList.size());
            for (PersonalVO vo : personalVOList) {
                //获取入职日期
                Date entryDate = vo.getEntryDate();
                // 格式化输出
                String duration = calculateWorkDuration(entryDate);
                vo.setEntryYearMonth(duration);
            }
            //按自定义职级序列排序
            Map<String, Integer> rankPriorityMap = new HashMap<>();
            rankPriorityMap.put("SWS02", 1);
            rankPriorityMap.put("SWS01", 2);
            rankPriorityMap.put("FHWC04", 3);
            rankPriorityMap.put("FHWC03", 4);
            rankPriorityMap.put("FHWC02", 5);
            rankPriorityMap.put("FHWC01", 6);
            rankPriorityMap.put("其它", 7);
            personalVOList = personalVOList.stream()
                    .sorted(Comparator.comparing(personalVO -> rankPriorityMap.getOrDefault(personalVO.getRankCode(), 7)))
                    .collect(Collectors.toList());
        }
        teamManageVO.setPersonalVOList(personalVOList);
        return teamManageVO;
    }

    @Override
    public TeamManageVO getCurrentTeamPerformance(String saleTeamCode) {
        log.info("获取团队当月业绩,{},{}",saleTeamCode,getCurrentLoginEmpCode());
        TeamManageVO teamManageVO = new TeamManageVO();
        if (!checkTeamAccess(saleTeamCode,teamManageVO)){
            log.info("当前登录代理人无权限访问团队");
            return teamManageVO;
        }
        //团队转换
        if (getCurrentLoginEmpCode().startsWith("S")){
            Tbsaleteam saleTeam = saleTeamDao.getTeamSaleTeam(saleTeamCode);
            if (ObjectUtils.isEmpty(saleTeam)){
                log.info("督导账号查询组级团队失败,{}",saleTeamCode);
                return teamManageVO;
            }
            saleTeamCode = saleTeam.getSaleteamcode();
        }
        //获取团队数据
//        assembleTeamCommissionInfo(saleTeamCode,teamManageVO);
        //获取各个人员数据
        assembleTeamPerformanceInfo(saleTeamCode,teamManageVO);

//        teamManageVO.setActivePersonNum(10);
//        teamManageVO.setStandardPersonNum(10);
//        teamManageVO.setCurrentMonthCr(BigDecimal.valueOf(Math.random()*100).setScale(2, RoundingMode.HALF_UP));
//        teamManageVO.setCurrentMonthFyp(BigDecimal.valueOf(Math.random()*100).setScale(2, RoundingMode.HALF_UP));
//        teamManageVO.setCurrentMonthFyc(BigDecimal.valueOf(Math.random()*100).setScale(2, RoundingMode.HALF_UP));
//        teamManageVO.setCurrentMonthPolicyNum(10);
//        List<PersonalVO> FYPList = new ArrayList<>();
//        for (int i = 5; i >= 1; i--) {
//            PersonalVO personalVO = new PersonalVO();
//            personalVO.setAgentCode("000" + i);
//            personalVO.setAgentName("测试代理人" + i);
//            personalVO.setCurrentMonthFYP(BigDecimal.valueOf(i* 10000L).setScale(2, RoundingMode.HALF_UP));
//            FYPList.add(personalVO);
//        }
//        teamManageVO.setFYPPersonalVOList(FYPList);
//        List<PersonalVO> FycList = new ArrayList<>();
//        for (int i = 5; i >= 1; i--) {
//            PersonalVO personalVO = new PersonalVO();
//            personalVO.setAgentCode("000" + i);
//            personalVO.setAgentName("测试代理人" + i);
//            personalVO.setCurrentMonthFYC(BigDecimal.valueOf(i* 10000L).setScale(2, RoundingMode.HALF_UP));
//            FycList.add(personalVO);
//        }
//        teamManageVO.setFYCPersonalVOList(FycList);
//        List<PersonalVO> PolicyNumList = new ArrayList<>();
//        for (int i = 5; i >= 1; i--) {
//            PersonalVO personalVO = new PersonalVO();
//            personalVO.setAgentCode("000" + i);
//            personalVO.setAgentName("测试代理人" + i);
//            personalVO.setCurrentMonthPolicyNum(i* 10);
//            PolicyNumList.add(personalVO);
//        }
//        teamManageVO.setPolicyNumPersonalVOList(PolicyNumList);
        return teamManageVO;
    }

    private void assembleTeamPerformanceInfo(String saleTeamCode, TeamManageVO teamManageVO) {
        //获取人员数据
        List<PersonalVO> empInfoList = performanceDao.getTeamCurrentPersonQuitList(saleTeamCode);
        if (CollectionUtils.isEmpty(empInfoList)){
            return;
        }
        List<String> agentCodeList = empInfoList.stream().map(PersonalVO::getAgentCode).collect(Collectors.toList());
        log.info("获取团队人员数据,{},{}",saleTeamCode,JSON.toJSON(agentCodeList));
        //按人员数据查询数据
        List<PersonalVO> allPerformanceVoList = new ArrayList<>();
        String performanceMonth = getCommissionMonth(new Date());
        List<String> wtPolNoList = getEsCurrentDayWtPolNoList(null, performanceMonth);
        List<PolicyEsVo> esCurrentPolicyList = getEsCurrentPolicyList(null, agentCodeList, null, null);
        List<PersonalVO> esPersonalVOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(esCurrentPolicyList)){
            esPersonalVOList = transESVoToPersonalVO(esCurrentPolicyList);
        }
        List<PersonalVO> performanceVoList = performanceDao.getPerformanceListByCodeList(agentCodeList,performanceMonth,wtPolNoList);
        initPerformanceVoList(performanceVoList,empInfoList);
        if (!CollectionUtils.isEmpty(performanceVoList)){
            if (!CollectionUtils.isEmpty(esPersonalVOList)){
                combinedPersonalVoList(esPersonalVOList,performanceVoList);
            }
            allPerformanceVoList.addAll(performanceVoList);
        }else if (!CollectionUtils.isEmpty(esPersonalVOList)){
            allPerformanceVoList.addAll(esPersonalVOList);
        }
        if (!CollectionUtils.isEmpty(allPerformanceVoList)){
            //排名
            List<PersonalVO> FYPList = allPerformanceVoList.stream().filter(o->!ObjectUtils.isEmpty(o.getCurrentMonthFYP())).sorted(Comparator.comparing(PersonalVO::getCurrentMonthFYP,Comparator.nullsFirst(Comparator.naturalOrder())).reversed()).peek(o->o.setCurrentMonthFYP(o.getCurrentMonthFYP().setScale(0, RoundingMode.HALF_UP))).limit(5).collect(Collectors.toList());
            teamManageVO.setFYPPersonalVOList(FYPList);
            List<PersonalVO> FYCList = allPerformanceVoList.stream().filter(o->!ObjectUtils.isEmpty(o.getCurrentMonthFYC())).sorted(Comparator.comparing(PersonalVO::getCurrentMonthFYC,Comparator.nullsFirst(Comparator.naturalOrder())).reversed()).peek(o->o.setCurrentMonthFYC(o.getCurrentMonthFYC().setScale(0, RoundingMode.HALF_UP))).limit(5).collect(Collectors.toList());
            teamManageVO.setFYCPersonalVOList(FYCList);
            List<PersonalVO> NumList = allPerformanceVoList.stream().filter(o->!ObjectUtils.isEmpty(o.getCurrentMonthPolicyNum())).sorted(Comparator.comparing(PersonalVO::getCurrentMonthPolicyNum,Comparator.nullsFirst(Comparator.naturalOrder())).reversed()).limit(5).collect(Collectors.toList());
            teamManageVO.setPolicyNumPersonalVOList(NumList);
            List<PersonalVO> DCPList = allPerformanceVoList.stream().filter(o->!ObjectUtils.isEmpty(o.getCurrentMonthDCP())).sorted(Comparator.comparing(PersonalVO::getCurrentMonthDCP,Comparator.nullsFirst(Comparator.naturalOrder())).reversed()).peek(o->o.setCurrentMonthDCP(o.getCurrentMonthDCP().setScale(0, RoundingMode.HALF_UP))).limit(5).collect(Collectors.toList());
            teamManageVO.setDCPPersonalVOList(DCPList);
//            //总和
//            teamManageVO.setCurrentMonthFyp(allPerformanceVoList.stream().map(PersonalVO::getCurrentMonthFYP).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
//            teamManageVO.setCurrentMonthFyc(allPerformanceVoList.stream().map(PersonalVO::getCurrentMonthFYC).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
//            teamManageVO.setCurrentMonthPolicyNum(allPerformanceVoList.stream().map(PersonalVO::getCurrentMonthPolicyNum).reduce(0, Integer::sum));
//            teamManageVO.setCurrentMonthDCP(allPerformanceVoList.stream().map(PersonalVO::getCurrentMonthDCP).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
    }

    private void initPerformanceVoList(List<PersonalVO> performanceVoList, List<PersonalVO> agentList) {
        List<String> perAgentCodeList = performanceVoList.stream().map(PersonalVO::getAgentCode).collect(Collectors.toList());
        List<PersonalVO> collect = agentList.stream().filter(o -> !perAgentCodeList.contains(o.getAgentCode())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)){
            for (PersonalVO agent : collect) {
                performanceVoList.add(PersonalVO.builder()
                        .agentCode(agent.getAgentCode())
                        .agentName(agent.getAgentName())
                        .currentMonthFYP(BigDecimal.ZERO)
                        .currentMonthFYC(BigDecimal.ZERO)
                        .currentMonthDCP(BigDecimal.ZERO)
                        .currentMonthPolicyNum(0)
                        .currentDayPolicyNoList(new ArrayList<>())
                        .build());
            }
        }
    }

    private void combinedPersonalVoList(List<PersonalVO> esPersonalVOList, List<PersonalVO> performanceVoList) {
        Map<String, List<PersonalVO>> collect = performanceVoList.stream().collect(Collectors.groupingBy(PersonalVO::getAgentCode));
        for (PersonalVO esPersonalVO : esPersonalVOList) {
            if (collect.containsKey(esPersonalVO.getAgentCode())){
                List<PersonalVO> personalVOList = collect.get(esPersonalVO.getAgentCode());
                PersonalVO personalVO = personalVOList.get(0);
                personalVO.setCurrentMonthPolicyNum(personalVO.getCurrentMonthPolicyNum()+esPersonalVO.getCurrentMonthPolicyNum());
                personalVO.setCurrentMonthFYP(personalVO.getCurrentMonthFYP().add(esPersonalVO.getCurrentMonthFYP()));
                personalVO.setCurrentDayPolicyNoList(esPersonalVO.getCurrentDayPolicyNoList());
            }else{
                performanceVoList.add(esPersonalVO);
            }
        }
    }

    private List<PersonalVO> transESVoToPersonalVO(List<PolicyEsVo> esCurrentPolicyList) {
        if (CollectionUtils.isEmpty(esCurrentPolicyList)){
            return null;
        }
        List<PersonalVO> personalVOList = new ArrayList<>();

        Map<String, Map<String, List<PolicyEsVo>>> collect = esCurrentPolicyList.stream().collect(Collectors.groupingBy(PolicyEsVo::getAgentCode, Collectors.groupingBy(PolicyEsVo::getContNo)));
        for (String agentCode : collect.keySet()) {
            PersonalVO personalVO = new PersonalVO();
            personalVO.setAgentCode(agentCode);

            Map<String, List<PolicyEsVo>> policyNoMap = collect.get(agentCode);
            BigDecimal premACC = BigDecimal.ZERO;
            Integer Num = 0;
            for (String policyNo : policyNoMap.keySet()) {
                List<PolicyEsVo> policyEsVos = policyNoMap.get(policyNo);
                long pos = policyEsVos.stream().filter(o -> ObjectUtils.isEmpty(o.getPrem()) || o.getPrem().compareTo(BigDecimal.ZERO) >= 0).count();
                long neg = policyEsVos.size() - pos;
                if (pos>neg){
                    Num++;
                }else if (pos<neg){
                    Num--;
                }
                BigDecimal prem = policyEsVos.stream().map(PolicyEsVo::getPrem).reduce(BigDecimal.ZERO, BigDecimal::add);
                premACC = premACC.add(prem);
            }
            personalVO.setAgentName(policyNoMap.get(policyNoMap.keySet().iterator().next()).get(0).getAgentName());
            premACC = premACC.setScale(2,RoundingMode.HALF_UP);
            personalVO.setCurrentMonthFYP(premACC);
            personalVO.setCurrentMonthPolicyNum(Num);
            personalVO.setCurrentDayPolicyNoList(new ArrayList<>(policyNoMap.keySet()));
            personalVOList.add(personalVO);
        }

//        Map<String, List<PolicyEsVo>> collect1 = esCurrentPolicyList.stream().collect(Collectors.groupingBy(PolicyEsVo::getAgentCode));
//        for (String agentCode : collect1.keySet()) {
//            PersonalVO personalVO = new PersonalVO();
//            List<PolicyEsVo> policyEsVos = collect1.get(agentCode);
//            personalVO.setAgentCode(agentCode);
//            personalVO.setCurrentMonthFYP(policyEsVos.stream().map(PolicyEsVo::getPrem).reduce(BigDecimal.ZERO, BigDecimal::add));
//            personalVO.setCurrentMonthPolicyNum(policyEsVos.size());
//            personalVOList.add(personalVO);
//        }

        return personalVOList;
    }

    private void assembleTeamCommissionInfo(String saleTeamCode, TeamManageVO teamManageVO) {
        String commissionMonth = getCommissionMonth(new Date());
        TeamManageVO commissionVo = performanceDao.getCommissionTeamInfo(commissionMonth,saleTeamCode,teamManageVO.getEmpCode());
        if (!ObjectUtils.isEmpty(commissionVo)){
            teamManageVO.setActivePersonNum(commissionVo.getActivePersonNum());
            teamManageVO.setStandardPersonNum(commissionVo.getStandardPersonNum());
            teamManageVO.setCurrentMonthCr(commissionVo.getCurrentMonthCr());
        }
    }

    private String getCommissionMonth(Date date) {
        if (ObjectUtils.isEmpty(date)){
            return null;
        }
        return new SimpleDateFormat("yyyy-MM").format(date);
    }

    private boolean checkTeamAccess(String saleTeamCode, TeamManageVO teamManageVO) {
        String loginEmpCode = getCurrentLoginEmpCode();
        if (StringUtils.isEmpty(loginEmpCode)) {
            log.info("校验失败,未获取到当前登录人员信息");
            return false;
        }
        //查询目标人信息
        Tbsaleteam targetSaleTeam = saleTeamDao.getPerformanceSaleTeamByCode(saleTeamCode);
        if (ObjectUtils.isEmpty(targetSaleTeam) ){
            log.info("校验失败,未获取到当前团队信息");
            return false;
        }
        if (!ObjectUtils.isEmpty(teamManageVO)){
            teamManageVO.setTeamCode(targetSaleTeam.getSaleteamcode());
            teamManageVO.setTeamName(targetSaleTeam.getSaleteamname());
            teamManageVO.setSaleTeamInCode(targetSaleTeam.getSaleteamincode());
            teamManageVO.setEmpCode(targetSaleTeam.getEmpincode());
        }
        if (loginEmpCode.startsWith("S")) {
            if (StringUtils.isEmpty(targetSaleTeam.getInstCode())){
                log.info("校验失败,未获取到目标团队机构信息:{}", saleTeamCode);
                return false;
            }
            //督导账号查询机构权限
            List<String> orgList = getSupervisorOrgList(loginEmpCode);
            if (CollectionUtils.isEmpty(orgList)) {
                log.info("校验失败,未获取到当前登录人员机构权限信息:{}", loginEmpCode);
                return false;
            }
            if (orgList.contains(targetSaleTeam.getInstCode())) {
                return true;
            } else {
                log.info("校验失败,督导账号机构权限匹配失败,当前登录人员机构权限信息:{},{},目标人员机构信息:{},{}", loginEmpCode, orgList, loginEmpCode, targetSaleTeam.getInstCode());
                return false;
            }
        }else{
            if (ObjectUtils.isEmpty(targetSaleTeam.getEmpincode()) ){
                log.info("校验失败,未获取到当前团队主管信息");
                return false;
            }
            if (targetSaleTeam.getEmpincode().equals(loginEmpCode)){
                return true;
            }else{
                log.info("校验失败,当前登录人员不是该团队主管,无查看权限:{},{}", loginEmpCode, targetSaleTeam.getEmpincode());
                return false;
            }
        }
    }

    @Override
    public List<PerformanceTrendVO> getTeamTrendPerformanceTrend(String saleTeamCode, String type) {
        log.info("获取团队当月业绩趋势,{},{},{}",saleTeamCode,getCurrentLoginEmpCode(),type);
        List<PerformanceTrendVO> performanceTrendVOList = new ArrayList<>();
        TeamManageVO teamManageVO = new TeamManageVO();
        boolean acess = checkTeamAccess(saleTeamCode, teamManageVO);
        if (!checkTeamAccess(saleTeamCode,teamManageVO)){
            log.info("当前登录代理人无权限访问团队");
            return performanceTrendVOList;
        }
        //团队转换
        if (getCurrentLoginEmpCode().startsWith("S")){
            Tbsaleteam saleTeam = saleTeamDao.getTeamSaleTeam(saleTeamCode);
            if (ObjectUtils.isEmpty(saleTeam)){
                log.info("督导账号查询组级团队失败,{}",saleTeamCode);
                return performanceTrendVOList;
            }
            saleTeamCode = saleTeam.getSaleteamcode();
        }
        //查询人员信息

        if ("month".equals(type)){
            List<PerformanceTrendVO> trendVOList = getPerformanceTrendVOList(type);
            if (CollectionUtils.isEmpty(trendVOList)){
                return performanceTrendVOList;
            }
            if (!acess){
                log.info("当前登录代理人无权限访问团队");
                return trendVOList;
            }
            //获取数据
            assembleTeamPerformanceTrendInfo(trendVOList,saleTeamCode,type);
            return trendVOList;
//            for (int i = 1; i <= 30; i++) {
//                PerformanceTrendVO performanceTrendVO = new PerformanceTrendVO();
//                performanceTrendVO.setTrendDay(i + "");
//                performanceTrendVO.setFYCAmount(BigDecimal.valueOf(Math.random()*100).setScale(2, RoundingMode.HALF_UP));
//                performanceTrendVO.setFYPAmount(BigDecimal.valueOf(Math.random()*100).setScale(2, RoundingMode.HALF_UP));
//                performanceTrendVOList.add(performanceTrendVO);
//            }
        }else
        if ("half_year".equals(type)){
            List<PerformanceTrendVO> trendVOList = getPerformanceTrendVOList(type);
            if (CollectionUtils.isEmpty(trendVOList)){
                return performanceTrendVOList;
            }
            if (!acess){
                log.info("当前登录代理人无权限访问团队");
                return trendVOList;
            }
            assembleTeamPerformanceTrendInfo(trendVOList,saleTeamCode,type);
            //环比计算
            PerformanceTrendVO ATrendVO = trendVOList.get(trendVOList.size()-1);
            PerformanceTrendVO BTrendVO = trendVOList.get(trendVOList.size()-2);
            BigDecimal AFyc = ATrendVO.getFYCAmount();
            BigDecimal AFyp = ATrendVO.getFYPAmount();
            BigDecimal ADcp = ATrendVO.getDCPAmount();
            BigDecimal BFyc = BTrendVO.getFYCAmount();
//            BFyc = BigDecimal.valueOf(1000);//测试数据
            BigDecimal BFyp = BTrendVO.getFYPAmount();
            BigDecimal BDcp = BTrendVO.getDCPAmount();

//            BFyp = BigDecimal.valueOf(1000);//测试数据
            if (BigDecimal.ZERO.compareTo(BFyc)!=0){
                ATrendVO.setFYCAmountRate((AFyc.subtract(BFyc).divide(BFyc,2,RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100))));
            }else{
                if (BigDecimal.ZERO.compareTo(AFyc)==0){
                    ATrendVO.setFYCAmountRate(BigDecimal.ZERO);
                }else if (BigDecimal.ZERO.compareTo(BFyc)==0){
                    ATrendVO.setFYCAmountRate(new BigDecimal("100"));
                }
            }

            if (BigDecimal.ZERO.compareTo(BFyp)!=0){
                ATrendVO.setFYPAmountRate((AFyp.subtract(BFyp).divide(BFyp,2,RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100))));
            }else if (BigDecimal.ZERO.compareTo(AFyp)==0){
                ATrendVO.setFYPAmountRate(BigDecimal.ZERO);
            }else if(BigDecimal.ZERO.compareTo(BFyp)==0){
                ATrendVO.setFYPAmountRate(new BigDecimal("100"));
            }


            if (BigDecimal.ZERO.compareTo(BDcp)!=0){
                ATrendVO.setDCPAmountRate((ADcp.subtract(BDcp).divide(BDcp,2,RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100))));
            }else if (BigDecimal.ZERO.compareTo(ADcp)==0){
                ATrendVO.setDCPAmountRate(BigDecimal.ZERO);
            }else if (BigDecimal.ZERO.compareTo(BDcp)==0){
                ATrendVO.setDCPAmountRate(new BigDecimal("100"));
            }

            return trendVOList;
//            for (int i = 1; i <= 6; i++) {
//                PerformanceTrendVO performanceTrendVO = new PerformanceTrendVO();
//                performanceTrendVO.setTrendMonth(i + "");
//                performanceTrendVO.setFYCAmount(BigDecimal.valueOf(Math.random()*100).setScale(2, RoundingMode.HALF_UP));
//                performanceTrendVO.setFYCAmountRate(BigDecimal.valueOf(Math.random()*100).setScale(0, RoundingMode.HALF_UP));
//                performanceTrendVO.setFYPAmountRate(BigDecimal.valueOf(Math.random()*100).setScale(0, RoundingMode.HALF_UP));
//                performanceTrendVO.setFYPAmount(BigDecimal.valueOf(Math.random()*100).setScale(2, RoundingMode.HALF_UP));
//                performanceTrendVOList.add(performanceTrendVO);
//            }
        }else
        if ("active".equals(type)){
            List<PerformanceTrendVO> trendVOList = getPerformanceTrendVOList(type);
            if (CollectionUtils.isEmpty(trendVOList)){
                return performanceTrendVOList;
            }
            if (!acess){
                log.info("当前登录代理人无权限访问团队");
//                return trendVOList;
            }
            assembleTeamActiveTrendInfo(trendVOList,saleTeamCode);
            return trendVOList;
//            for (int i = 1; i <= 6; i++) {
//                PerformanceTrendVO performanceTrendVO = new PerformanceTrendVO();
//                performanceTrendVO.setTrendMonth(i + "");
//                performanceTrendVO.setActivePersonNum(i);
//                performanceTrendVO.setActivePersonNumRate(BigDecimal.valueOf(Math.random()*100).setScale(0, RoundingMode.HALF_UP));
//                performanceTrendVOList.add(performanceTrendVO);
//            }

        }
        return performanceTrendVOList;
    }

    private void assembleTeamActiveTrendInfo(List<PerformanceTrendVO> trendVOList, String saleTeamCode) {
//        List<PersonalVO> empInfoList = performanceDao.getTeamCurrentPersonQuitList(saleTeamCode);
//        if (CollectionUtils.isEmpty(empInfoList)){
//            trendVOList.clear();
//            return;
//        }
        List<String> commissionMonthList = trendVOList.stream().map(PerformanceTrendVO::getTrendTimeString).distinct().collect(Collectors.toList());
//        List<String> agentCodeList = empInfoList.stream().map(PersonalVO::getAgentCode).distinct().collect(Collectors.toList());
        List<PerformanceTrendVO> activePersonList = performanceDao.getTeamActiveTrendInfoList(saleTeamCode,commissionMonthList,null);
        if (!CollectionUtils.isEmpty(activePersonList)){
            Map<String, List<PerformanceTrendVO>> timeMap= activePersonList.stream().collect(Collectors.groupingBy(PerformanceTrendVO::getTrendTimeString));
            for (PerformanceTrendVO performanceTrendVO : trendVOList) {
                List<PerformanceTrendVO> monthVoList = timeMap.get(performanceTrendVO.getTrendTimeString());
                if (!CollectionUtils.isEmpty(monthVoList)){
                    performanceTrendVO.setActivePersonNum(monthVoList.get(0).getActivePersonNum());
                }
            }

        }

        PerformanceTrendVO performanceTrendVO = trendVOList.get(trendVOList.size() - 1);
        Integer ANum = performanceTrendVO.getActivePersonNum();
        Integer BNum = trendVOList.get(trendVOList.size() - 2).getActivePersonNum();
        if (BNum!=0){
            performanceTrendVO.setActivePersonNumRate(new BigDecimal((ANum-BNum)*1.0/BNum).setScale(2,RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
        }else if (ANum==0){
            performanceTrendVO.setActivePersonNumRate(BigDecimal.ZERO);
        }else if (BNum==0){
            performanceTrendVO.setActivePersonNumRate(new BigDecimal("100"));
        }else{
            performanceTrendVO.setActivePersonNumRate(BigDecimal.ZERO);
        }


    }

    private void assembleTeamPerformanceTrendInfo(List<PerformanceTrendVO> performanceTrendVOList, String saleTeamCode, String type) {
        List<PersonalVO> empInfoList = performanceDao.getTeamCurrentPersonQuitList(saleTeamCode);
        if (CollectionUtils.isEmpty(empInfoList)){
//            performanceTrendVOList.clear();
            return;
        }
        List<String> agentCodeList = empInfoList.stream().map(PersonalVO::getAgentCode).distinct().collect(Collectors.toList());
        List<String> wtPolNoList = getEsCurrentDayWtPolNoList(null, null);
        List<PerformanceTrendVO> trendVOList = performanceDao.getTeamTrendPerformanceList(saleTeamCode,agentCodeList,wtPolNoList,type,performanceTrendVOList.get(0).getTrendTimeString(),performanceTrendVOList.get(performanceTrendVOList.size()-1).getTrendTimeString());
        if (!CollectionUtils.isEmpty(trendVOList)){
            Map<String, List<PerformanceTrendVO>> timeMap= trendVOList.stream().collect(Collectors.groupingBy(PerformanceTrendVO::getTrendTimeString));
            for (PerformanceTrendVO performanceTrendVO : performanceTrendVOList) {
                List<PerformanceTrendVO> dayVoList = timeMap.get(performanceTrendVO.getTrendTimeString());
                if (!CollectionUtils.isEmpty(dayVoList)){
                    performanceTrendVO.setFYCAmount(dayVoList.get(0).getFYCAmount().divide(BigDecimal.valueOf(10000),2, RoundingMode.HALF_UP));
                    performanceTrendVO.setFYPAmount(dayVoList.get(0).getFYPAmount().divide(BigDecimal.valueOf(10000),2, RoundingMode.HALF_UP));
                    performanceTrendVO.setDCPAmount(dayVoList.get(0).getDCPAmount().divide(BigDecimal.valueOf(10000),2, RoundingMode.HALF_UP));
                }
            }
        }
        //查询当日业绩
        List<PolicyEsVo> esCurrentPolicyList = getEsCurrentPolicyList(null, agentCodeList, null, null);
        if (!CollectionUtils.isEmpty(esCurrentPolicyList)){
            PerformanceTrendVO performanceTrendVO = performanceTrendVOList.get(performanceTrendVOList.size() - 1);
            performanceTrendVO.setFYPAmount(performanceTrendVO.getFYPAmount().add(esCurrentPolicyList.stream().map(PolicyEsVo::getPrem).filter(prem ->!ObjectUtils.isEmpty(prem)).reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(10000),2, RoundingMode.HALF_UP)));
        }



    }

    private List<PerformanceTrendVO> getPerformanceTrendVOList(String type) {
        List<PerformanceTrendVO> performanceTrendVOList = new ArrayList<>();
        //获取时间戳
        if ("month".equals(type)){
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            for (int i = 29; i >= 0; i--) {
                LocalDate date = LocalDate.now().minusDays(i);
                PerformanceTrendVO performanceTrendVO = new PerformanceTrendVO();
                String format = date.format(formatter);
                performanceTrendVO.setTrendTimeString(format);
                performanceTrendVO.setTrendDay(date.getDayOfMonth() + "");
                performanceTrendVO.setFYCAmount(BigDecimal.ZERO);
                performanceTrendVO.setFYPAmount(BigDecimal.ZERO);
                performanceTrendVO.setDCPAmount(BigDecimal.ZERO);
                performanceTrendVOList.add(performanceTrendVO);
            }
            return performanceTrendVOList;
        }else if ("half_year".equals(type)){
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            for (int i = 5; i >= 0; i--) {
                LocalDate date = LocalDate.now().minusMonths(i);
                PerformanceTrendVO performanceTrendVO = new PerformanceTrendVO();
                String format = date.format(formatter);
                performanceTrendVO.setTrendTimeString(format);
                performanceTrendVO.setTrendMonth(date.getMonthValue() + "");
                performanceTrendVO.setFYCAmount(BigDecimal.ZERO);
                performanceTrendVO.setFYPAmount(BigDecimal.ZERO);
                performanceTrendVO.setDCPAmount(BigDecimal.ZERO);
                performanceTrendVOList.add(performanceTrendVO);
            }
            return performanceTrendVOList;
        }else if ("active".equals(type)){
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            for (int i = 5; i >= 0; i--) {
                LocalDate date = LocalDate.now().minusMonths(i);
                PerformanceTrendVO performanceTrendVO = new PerformanceTrendVO();
                String format = date.format(formatter);
                performanceTrendVO.setTrendTimeString(format);
                performanceTrendVO.setTrendMonth(date.getMonthValue() + "");
                performanceTrendVO.setActivePersonNum(0);
                performanceTrendVOList.add(performanceTrendVO);
            }
            return performanceTrendVOList;
        }
        return null;
    }

    @Override
    public TeamVersionVO getTeamRangePerformance(String saleTeamCode, String type, String performanceMonth, String paymentYears, Date startDate, Date endDate) {
        TeamVersionVO teamVo = new TeamVersionVO();
        List<TeamPerformanceVO> teamPerformanceVOList = new ArrayList<>();
        teamVo.setTeamPerformanceVOList(teamPerformanceVOList);
        log.info("团队业绩查询,{},{},{}", saleTeamCode, getCurrentLoginEmpCode(), type);
        TeamManageVO teamManageVO = new TeamManageVO();
        if (!checkTeamAccess(saleTeamCode,teamManageVO)){
            log.info("当前登录代理人无权限访问团队");
            return teamVo;
        }
        //团队转换
        if (getCurrentLoginEmpCode().startsWith("S")){
            Tbsaleteam saleTeam = saleTeamDao.getTeamSaleTeam(saleTeamCode);
            if (ObjectUtils.isEmpty(saleTeam)){
                log.info("督导账号查询组级团队失败,{}",saleTeamCode);
                return teamVo;
            }
            saleTeamCode = saleTeam.getSaleteamcode();
        }
        String finalSaleTeamCode = saleTeamCode;
        CompletableFuture<String> versionTypeFuture = CompletableFuture.supplyAsync(() -> {
            if (!"team".equals(type)){
                return getBasicLawVersionType(null, finalSaleTeamCode,getCommissionMonth(endDate));
            }else{
                return getBasicLawVersionType(null, finalSaleTeamCode,performanceMonth);
            }
        });
        if ("team".equals(type)){
            TeamPerformanceVO teamPerformanceVO = new TeamPerformanceVO();
            if (StringUtils.isEmpty(performanceMonth)){
                return teamVo;
            }
            assembleTeamPerformanceMonthInfo(teamPerformanceVO,saleTeamCode,performanceMonth);
            teamPerformanceVOList.add(teamPerformanceVO);
//            teamPerformanceVO.setVersionType(versionTypeFuture.join());
            teamVo.setVersionType(versionTypeFuture.join());
            return teamVo;

//            teamPerformanceVO.setPolicyNum(10);
//            teamPerformanceVO.setPolicyIdList(Arrays.asList("0001", "0002", "0003", "0004", "0005", "0006", "0007", "0008", "0009", "0010"));
//            teamPerformanceVO.setCurrentDayPolicyNoList(Arrays.asList("P00001", "P00002"));
//            teamPerformanceVO.setFYP(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//            teamPerformanceVO.setFYC(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//            teamPerformanceVO.setActivePersonNum(10);
//            teamPerformanceVO.setStandardPersonNum(10);
//            teamPerformanceVO.setFYPPerPolicy(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//            teamPerformanceVO.setFYPPerPerson(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//            teamPerformanceVO.setFYCPerPolicy(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//            teamPerformanceVO.setFYCPerPerson(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//            teamPerformanceVO.setCr13(BigDecimal.valueOf(Math.random()*100).setScale(2, RoundingMode.HALF_UP));
//            teamPerformanceVO.setCr13ActualPremium(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//            teamPerformanceVO.setCr13Premium(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//            teamPerformanceVOList.add(teamPerformanceVO);
        }
        if ("personal".equals(type)){
            assembleTeamPerformanceRangeInfo(teamPerformanceVOList,saleTeamCode,startDate,endDate,type,paymentYears);
            teamPerformanceVOList = teamPerformanceVOList.stream().sorted(Comparator.comparing(TeamPerformanceVO::getFYC,Comparator.nullsFirst(Comparator.naturalOrder())).thenComparing(TeamPerformanceVO::getFYP,Comparator.nullsFirst(Comparator.naturalOrder())).reversed()).collect(Collectors.toList());
            teamVo.setTeamPerformanceVOList(teamPerformanceVOList);
            teamVo.setVersionType(versionTypeFuture.join());
            return teamVo;
//            for (int i = 1; i <= 5; i++) {
//                TeamPerformanceVO teamPerformanceVO = new TeamPerformanceVO();
//                teamPerformanceVO.setAgentCode("000"+i);
//                teamPerformanceVO.setAgentName("测试代理人"+i);
//                teamPerformanceVO.setPolicyNum(i);
//                teamPerformanceVO.setPolicyIdList(Arrays.asList("0001", "0002"));
//                teamPerformanceVO.setCurrentDayPolicyNoList(Arrays.asList("P00001", "P00002"));
//                teamPerformanceVO.setFYP(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//                teamPerformanceVO.setFYC(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//                teamPerformanceVO.setFYPPerPolicy(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//                teamPerformanceVO.setFYCPerPolicy(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//                teamPerformanceVOList.add(teamPerformanceVO);
//            }
        }
        if ("product".equals(type)){
            assembleTeamPerformanceRangeInfo(teamPerformanceVOList,saleTeamCode,startDate,endDate,type,paymentYears);
            teamPerformanceVOList.forEach(o->{
                o.setRiskCode(o.getAgentCode());
                o.setRiskName(o.getAgentName());
//                o.setVersionType(versionTypeFuture.join());
            });
            teamVo.setTeamPerformanceVOList(teamPerformanceVOList);
            teamVo.setVersionType(versionTypeFuture.join());
            return teamVo;
//            for (int i = 1; i <= 5; i++) {
//                TeamPerformanceVO teamPerformanceVO = new TeamPerformanceVO();
//                teamPerformanceVO.setRiskCode("000"+i);
//                teamPerformanceVO.setRiskCode("测试产品"+i);
//                teamPerformanceVO.setPolicyNum(i);
//                teamPerformanceVO.setPolicyIdList(Arrays.asList("0001", "0002"));
//                teamPerformanceVO.setCurrentDayPolicyNoList(Arrays.asList("P00001", "P00002"));
//                teamPerformanceVO.setFYP(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//                teamPerformanceVO.setFYC(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//                teamPerformanceVO.setFYPPerPolicy(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//                teamPerformanceVO.setFYCPerPolicy(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//                teamPerformanceVOList.add(teamPerformanceVO);
//            }
        }
        if ("rankSeqCode".equals(type)){
            assembleTeamPerformanceRangeInfo(teamPerformanceVOList,saleTeamCode,startDate,endDate,type,paymentYears);
            teamPerformanceVOList.forEach(o->{
                o.setRankSeqCode(o.getAgentCode());
                o.setRankSeqName(o.getAgentName());
//                o.setVersionType(versionTypeFuture.join());
            });
            teamVo.setTeamPerformanceVOList(teamPerformanceVOList);
            teamVo.setVersionType(versionTypeFuture.join());
            return teamVo;
//            for (int i = 1; i <= 5; i++) {
//                TeamPerformanceVO teamPerformanceVO = new TeamPerformanceVO();
//                teamPerformanceVO.setRankSeqCode("000"+i);
//                teamPerformanceVO.setRankSeqName("测试职级"+i);
//                teamPerformanceVO.setPolicyNum(i);
//                teamPerformanceVO.setPolicyIdList(Arrays.asList("0001", "0002"));
//                teamPerformanceVO.setCurrentDayPolicyNoList(Arrays.asList("P00001", "P00002"));
//                teamPerformanceVO.setFYP(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//                teamPerformanceVO.setFYC(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//                teamPerformanceVO.setFYPPerPolicy(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//                teamPerformanceVO.setFYCPerPolicy(BigDecimal.valueOf(Math.random()*1000000).setScale(2, RoundingMode.HALF_UP));
//                teamPerformanceVOList.add(teamPerformanceVO);
//            }
        }
        return teamVo;
    }

    private void assembleTeamPerformanceRangeInfo(List<TeamPerformanceVO> teamPerformanceVOList, String saleTeamCode, Date startDate, Date endDate, String type, String paymentYears) {
        if (ObjectUtils.isEmpty(startDate) || ObjectUtils.isEmpty(endDate)){
            return;
        }
        //查询人员数据
        List<PersonalVO> empInfoList = performanceDao.getTeamCurrentPersonQuitList(saleTeamCode);
        if (CollectionUtils.isEmpty(empInfoList)) {
            return;
        }
        boolean currentDay = new SimpleDateFormat("yyyy-MM-dd").format(new Date()).compareTo(new SimpleDateFormat("yyyy-MM-dd").format(endDate)) <= 0;
        List<String> agentCodeList = empInfoList.stream().map(PersonalVO::getAgentCode).distinct().collect(Collectors.toList());
        List<String> wtPolNoList = new ArrayList<>();
        List<PolicyEsVo> esCurrentPolicyList = new ArrayList<>();
        if (currentDay){
            wtPolNoList = getEsCurrentDayWtPolNoList(null, null);
            esCurrentPolicyList = getEsCurrentPolicyList(null, agentCodeList, null, null);
        }
        List<TeamPerformanceVO> performanceVoList = performanceDao.getRangePerformanceVOList(saleTeamCode,agentCodeList,wtPolNoList,startDate,endDate,paymentYears,type);
        List<TeamPerformanceVO> esPerformanceVOList = new ArrayList<>();
        esCurrentPolicyList = filetESPolicyByPaymentYears(esCurrentPolicyList,paymentYears);
        if (!CollectionUtils.isEmpty(esCurrentPolicyList)){
            esPerformanceVOList = transESVoToPerformanceVO(esCurrentPolicyList, type);
        }
        if (CollectionUtils.isEmpty(performanceVoList)){
            performanceVoList = new ArrayList<>();
        }
        combinedPerformanceVoList(performanceVoList,esPerformanceVOList);
        if ("personal".equals(type)){
            initTeamPerformanceVO(empInfoList,performanceVoList);
        }
        if (!CollectionUtils.isEmpty(performanceVoList)){
            performanceVoList.forEach(o->{
                Integer policyNum = o.getPolicyNum();
                if (!ObjectUtils.isEmpty(policyNum) && policyNum != 0){
                    if (!ObjectUtils.isEmpty(o.getFYC())){
                        o.setFYCPerPolicy(o.getFYC().divide(BigDecimal.valueOf(policyNum),2,RoundingMode.HALF_UP));
                    }else{
                        o.setFYC(BigDecimal.ZERO);
                        o.setFYCPerPolicy(BigDecimal.ZERO);
                    }
                    if (!ObjectUtils.isEmpty(o.getFYP())){
                        o.setFYPPerPolicy(o.getFYP().divide(BigDecimal.valueOf(policyNum),2,RoundingMode.HALF_UP));
                    }else{
                        o.setFYP(BigDecimal.ZERO);
                        o.setFYPPerPolicy(BigDecimal.ZERO);
                    }
                    if (!ObjectUtils.isEmpty(o.getDCP())){
                        o.setDCPPerPolicy(o.getDCP().divide(BigDecimal.valueOf(policyNum),2,RoundingMode.HALF_UP));
                    }else{
                        o.setDCP(BigDecimal.ZERO);
                        o.setDCPPerPolicy(BigDecimal.ZERO);
                    }
                }else{
                    o.setPolicyNum(0);
                }
//                String ids = o.getIds();
//                if (!StringUtils.isEmpty(ids)){
//                    o.setPolicyIdList(Arrays.asList(ids.split(",")));
//                    o.setIds(null);
//                }
            });
            teamPerformanceVOList.addAll(performanceVoList);
        }
    }

    private void initTeamPerformanceVO(List<PersonalVO> empInfoList, List<TeamPerformanceVO> performanceVoList) {
        List<String> agentCodeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(performanceVoList)){
            agentCodeList = performanceVoList.stream().map(TeamPerformanceVO::getAgentCode).distinct().collect(Collectors.toList());
        }
        for (PersonalVO personalVO : empInfoList) {
            if (!agentCodeList.contains(personalVO.getAgentCode())){
                TeamPerformanceVO teamPerformanceVO = new TeamPerformanceVO();
                teamPerformanceVO.setAgentCode(personalVO.getAgentCode());
                teamPerformanceVO.setAgentName(personalVO.getAgentName());
                teamPerformanceVO.setPolicyNum(0);
                teamPerformanceVO.setFYP(BigDecimal.ZERO);
                teamPerformanceVO.setFYC(BigDecimal.ZERO);
                teamPerformanceVO.setDCP(BigDecimal.ZERO);
                teamPerformanceVO.setFYPPerPolicy(BigDecimal.ZERO);
                teamPerformanceVO.setFYCPerPolicy(BigDecimal.ZERO);
                teamPerformanceVO.setDCPPerPolicy(BigDecimal.ZERO);
                performanceVoList.add(teamPerformanceVO);
            }
        }
    }

    private List<PolicyEsVo> filetESPolicyByPaymentYears(List<PolicyEsVo> esCurrentPolicyList, String paymentYears) {
        if (CollectionUtils.isEmpty(esCurrentPolicyList)){
            return esCurrentPolicyList;
        }
        if (!StringUtils.isEmpty(paymentYears) && !"全部".equals(paymentYears)){
            switch (paymentYears) {
                case "趸缴":
                    return esCurrentPolicyList.stream().filter(o -> "0".equals(o.getPayIntv())).collect(Collectors.toList());
                case "1年及以下":
                    return esCurrentPolicyList.stream().filter(o -> {
                        List<RiskEsVo> riskVoList = o.getRiskVoList();
                        if (CollectionUtils.isEmpty(riskVoList)) {
                            return false;
                        }
                        RiskEsVo riskEsVo = riskVoList.get(0);
                        if ("M".equals(riskEsVo.getPayEndYearFlag())) {
                            return true;
                        }
                        if (("1".equals(riskEsVo.getPayIntv()) || "12".equals(riskEsVo.getPayIntv())) && !StringUtils.isEmpty(riskEsVo.getPayYears())) {
                            return "1".equals(riskEsVo.getPayYears().toString());
                        }
                        return false;
                    }).collect(Collectors.toList());
                case "2-3年":
                    return esCurrentPolicyList.stream().filter(o -> {
                        List<RiskEsVo> riskVoList = o.getRiskVoList();
                        if (CollectionUtils.isEmpty(riskVoList)) {
                            return false;
                        }
                        RiskEsVo riskEsVo = riskVoList.get(0);
                        String payIntv = riskEsVo.getPayIntv();
                        if (StringUtils.isEmpty(payIntv)) {
                            return false;
                        }
                        if ("Y".equals(riskEsVo.getPayEndYearFlag()) && ("1".equals(payIntv) || "12".equals(payIntv)) && !StringUtils.isEmpty(riskEsVo.getPayYears())) {
                            return "2".equals(riskEsVo.getPayYears().toString()) || "3".equals(riskEsVo.getPayYears().toString());
                        }
                        return false;
                    }).collect(Collectors.toList());
                case "4-5年":
                    return esCurrentPolicyList.stream().filter(o -> {
                        List<RiskEsVo> riskVoList = o.getRiskVoList();
                        if (CollectionUtils.isEmpty(riskVoList)) {
                            return false;
                        }
                        RiskEsVo riskEsVo = riskVoList.get(0);
                        String payIntv = riskEsVo.getPayIntv();
                        if (StringUtils.isEmpty(payIntv)) {
                            return false;
                        }
                        if ("Y".equals(riskEsVo.getPayEndYearFlag()) && ("1".equals(payIntv) || "12".equals(payIntv)) && !StringUtils.isEmpty(riskEsVo.getPayYears())) {
                            return "4".equals(riskEsVo.getPayYears().toString()) || "5".equals(riskEsVo.getPayYears().toString());
                        }
                        return false;
                    }).collect(Collectors.toList());
                case "6-9年":
                    return esCurrentPolicyList.stream().filter(o -> {
                        List<RiskEsVo> riskVoList = o.getRiskVoList();
                        if (CollectionUtils.isEmpty(riskVoList)) {
                            return false;
                        }
                        RiskEsVo riskEsVo = riskVoList.get(0);
                        String payIntv = riskEsVo.getPayIntv();
                        if (StringUtils.isEmpty(payIntv)) {
                            return false;
                        }
                        if ("Y".equals(riskEsVo.getPayEndYearFlag()) && ("1".equals(payIntv) || "12".equals(payIntv)) && !StringUtils.isEmpty(riskEsVo.getPayYears())) {
                            return "6".equals(riskEsVo.getPayYears().toString()) || "7".equals(riskEsVo.getPayYears().toString()) || "8".equals(riskEsVo.getPayYears().toString()) || "9".equals(riskEsVo.getPayYears().toString());
                        }
                        return false;
                    }).collect(Collectors.toList());
                case "10年及以上":
                    return esCurrentPolicyList.stream().filter(o -> {
                        List<RiskEsVo> riskVoList = o.getRiskVoList();
                        if (CollectionUtils.isEmpty(riskVoList)) {
                            return false;
                        }
                        RiskEsVo riskEsVo = riskVoList.get(0);
                        String payIntv = riskEsVo.getPayIntv();
                        if (StringUtils.isEmpty(payIntv)) {
                            return false;
                        }
                        return ("Y".equals(riskEsVo.getPayEndYearFlag()) && ("1".equals(payIntv) || "12".equals(payIntv))) && getValue(riskEsVo.getPayYears().toString()) >=10 ;
                    }).collect(Collectors.toList());
                default:
                    return new ArrayList<>();
            }
        }
        return esCurrentPolicyList;
    }

    private float getValue(String number) {
        try {
            return Float.parseFloat(number);
        }catch (Exception e){
            return -1;
        }
    }

    private void combinedPerformanceVoList(List<TeamPerformanceVO> performanceVoList, List<TeamPerformanceVO> esPerformanceVOList) {
        if (CollectionUtils.isEmpty(esPerformanceVOList)){
            return ;
        }
        Map<String, List<TeamPerformanceVO>> collect = performanceVoList.stream().collect(Collectors.groupingBy(TeamPerformanceVO::getAgentCode));
        for (TeamPerformanceVO teamPerformanceVO : esPerformanceVOList) {
            if (collect.containsKey(teamPerformanceVO.getAgentCode())){
                TeamPerformanceVO performanceVO = collect.get(teamPerformanceVO.getAgentCode()).get(0);
                performanceVO.setPolicyNum(performanceVO.getPolicyNum()+teamPerformanceVO.getPolicyNum());
                performanceVO.setFYP(performanceVO.getFYP().add(teamPerformanceVO.getFYP()));
                performanceVO.setCurrentDayPolicyNoList(teamPerformanceVO.getCurrentDayPolicyNoList());
            }else{
                performanceVoList.add(teamPerformanceVO);
            }
        }
    }

    private List<TeamPerformanceVO> transESVoToPerformanceVO(List<PolicyEsVo> esCurrentPolicyList, String type) {
        if (CollectionUtils.isEmpty(esCurrentPolicyList)){
            return new ArrayList<>();
        }
        List<TeamPerformanceVO> performanceVOS = new ArrayList<>();
        if ("personal".equals(type)){
            extracted(esCurrentPolicyList, performanceVOS);
        }else if ("product".equals(type)){
            esCurrentPolicyList.forEach(o->{
                o.setAgentCode(null);
                o.setAgentName(null);
                List<RiskEsVo> riskVoList = o.getRiskVoList();
                if (!CollectionUtils.isEmpty(riskVoList)){
                    o.setAgentCode(riskVoList.get(0).getRiskCode());
                    o.setAgentName(riskVoList.get(0).getRiskName());
                }
            });
            extracted(esCurrentPolicyList, performanceVOS);
//            Map<String, Map<String, List<PolicyEsVo>>> collect = esCurrentPolicyList.stream().filter(o->!ObjectUtils.isEmpty(o.getAgentCode())).collect(Collectors.groupingBy(PolicyEsVo::getAgentCode, Collectors.groupingBy(PolicyEsVo::getContNo)));
//            for (String agentCode : collect.keySet()) {
//                TeamPerformanceVO personalVO = new TeamPerformanceVO();
//                personalVO.setAgentCode(agentCode);
//                Map<String, List<PolicyEsVo>> policyNoMap = collect.get(agentCode);
//                BigDecimal premACC = BigDecimal.ZERO;
//                Integer Num = 0;
//                for (String policyNo : policyNoMap.keySet()) {
//                    List<PolicyEsVo> policyEsVos = policyNoMap.get(policyNo);
//                    long pos = policyEsVos.stream().filter(o -> ObjectUtils.isEmpty(o.getPrem()) || o.getPrem().compareTo(BigDecimal.ZERO) >= 0).count();
//                    long neg = policyEsVos.size() - pos;
//                    if (pos>neg){
//                        Num++;
//                    }else if (pos<neg){
//                        Num--;
//                    }
//                    BigDecimal prem = policyEsVos.stream().map(PolicyEsVo::getPrem).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    premACC = premACC.add(prem);
//                }
//                personalVO.setAgentName(policyNoMap.get(policyNoMap.keySet().iterator().next()).get(0).getAgentName());
//                premACC = premACC.setScale(2,RoundingMode.HALF_UP);
//                personalVO.setFYP(premACC);
//                personalVO.setPolicyNum(Num);
//                personalVO.setCurrentDayPolicyNoList(new ArrayList<>(policyNoMap.keySet()));
//                performanceVOS.add(personalVO);
//            }
        }else if ("rankSeqCode".equals(type)){
            List<String> agentCodeList= esCurrentPolicyList.stream().map(PolicyEsVo::getAgentCode).distinct().collect(Collectors.toList());
            List<TeamPerformanceVO> agentRankSeqList = performanceDao.getAgentRankSeqInfoList(agentCodeList);
            if (CollectionUtils.isEmpty(agentRankSeqList)){
                return new ArrayList<>();
            }
            esCurrentPolicyList.forEach(o->{
                Optional<TeamPerformanceVO> first = agentRankSeqList.stream().filter(s -> s.getAgentCode().equals(o.getAgentCode())).findFirst();
                first.ifPresent(s->{
                    o.setAgentCode(s.getRankSeqCode());
                    o.setAgentName(s.getRankSeqName());
                });
                // 如果未找到，则设置默认值
                if (!first.isPresent()) {
                    log.info("未找到保单对应的职级序列代码,{},{}",o.getContNo(),o.getAgentCode());
                    o.setAgentCode(null);
                    o.setAgentName(null);
                }
            });
            extracted(esCurrentPolicyList, performanceVOS);
//            Map<String, Map<String, List<PolicyEsVo>>> collect = esCurrentPolicyList.stream().filter(o->!ObjectUtils.isEmpty(o.getAgentCode())).collect(Collectors.groupingBy(PolicyEsVo::getAgentCode, Collectors.groupingBy(PolicyEsVo::getContNo)));
//            for (String agentCode : collect.keySet()) {
//                TeamPerformanceVO personalVO = new TeamPerformanceVO();
//                personalVO.setAgentCode(agentCode);
//                Map<String, List<PolicyEsVo>> policyNoMap = collect.get(agentCode);
//                BigDecimal premACC = BigDecimal.ZERO;
//                Integer Num = 0;
//                for (String policyNo : policyNoMap.keySet()) {
//                    List<PolicyEsVo> policyEsVos = policyNoMap.get(policyNo);
//                    long pos = policyEsVos.stream().filter(o -> ObjectUtils.isEmpty(o.getPrem()) || o.getPrem().compareTo(BigDecimal.ZERO) >= 0).count();
//                    long neg = policyEsVos.size() - pos;
//                    if (pos>neg){
//                        Num++;
//                    }else if (pos<neg){
//                        Num--;
//                    }
//                    BigDecimal prem = policyEsVos.stream().map(PolicyEsVo::getPrem).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    premACC = premACC.add(prem);
//                }
//                personalVO.setAgentName(policyNoMap.get(policyNoMap.keySet().iterator().next()).get(0).getAgentName());
//                premACC = premACC.setScale(2,RoundingMode.HALF_UP);
//                personalVO.setFYP(premACC);
//                personalVO.setPolicyNum(Num);
//                personalVO.setCurrentDayPolicyNoList(new ArrayList<>(policyNoMap.keySet()));
//                performanceVOS.add(personalVO);
//            }
        }


        return performanceVOS;
    }

    private void extracted(List<PolicyEsVo> esCurrentPolicyList, List<TeamPerformanceVO> performanceVOS) {
        Map<String, Map<String, List<PolicyEsVo>>> collect = esCurrentPolicyList.stream().collect(Collectors.groupingBy(PolicyEsVo::getAgentCode, Collectors.groupingBy(PolicyEsVo::getContNo)));
        for (String agentCode : collect.keySet()) {
            TeamPerformanceVO personalVO = new TeamPerformanceVO();
            personalVO.setAgentCode(agentCode);
            Map<String, List<PolicyEsVo>> policyNoMap = collect.get(agentCode);
            BigDecimal premACC = BigDecimal.ZERO;
            Integer Num = 0;
            for (String policyNo : policyNoMap.keySet()) {
                List<PolicyEsVo> policyEsVos = policyNoMap.get(policyNo);
                long pos = policyEsVos.stream().filter(o -> ObjectUtils.isEmpty(o.getPrem()) || o.getPrem().compareTo(BigDecimal.ZERO) >= 0).count();
                long neg = policyEsVos.size() - pos;
                if (pos>neg){
                    Num++;
                }else if (pos<neg){
                    Num--;
                }
                BigDecimal prem = policyEsVos.stream().map(PolicyEsVo::getPrem).reduce(BigDecimal.ZERO, BigDecimal::add);
                premACC = premACC.add(prem);
            }
            personalVO.setAgentName(policyNoMap.get(policyNoMap.keySet().iterator().next()).get(0).getAgentName());
            premACC = premACC.setScale(2,RoundingMode.HALF_UP);
            personalVO.setFYP(premACC);
            personalVO.setPolicyNum(Num);
            personalVO.setCurrentDayPolicyNoList(new ArrayList<>(policyNoMap.keySet()));
            performanceVOS.add(personalVO);
        }

    }

    private void assembleTeamPerformanceMonthInfo(TeamPerformanceVO teamPerformanceVO, String saleTeamCode, String performanceMonth) {
        //查询人员数据
        List<PersonalVO> empInfoList = getTeamPerformanceMonthEmpList(saleTeamCode,performanceMonth);
        if (CollectionUtils.isEmpty(empInfoList)){
            return;
        }
        String commissionMonth = getCommissionMonth(new Date());
        boolean currentMonth = performanceMonth.equals(commissionMonth);
        List<String> agentCodeList = empInfoList.stream().map(PersonalVO::getAgentCode).distinct().collect(Collectors.toList());
        List<String> wtPolNoList = new ArrayList<>();
        List<PolicyEsVo> esCurrentPolicyList = new ArrayList<>();
        if (currentMonth){
            //查询ES今日承保数据和核心犹退数据
            wtPolNoList = getEsCurrentDayWtPolNoList(null, null);
            esCurrentPolicyList = getEsCurrentPolicyList(null, agentCodeList, null, null);
        }
        List<PersonalVO> allPerformanceVoList = new ArrayList<>();
        //获取销管业绩数据
        List<PersonalVO> performanceVoList = performanceDao.getPerformancemonthListByCodeList(agentCodeList,performanceMonth,wtPolNoList);
        List<PersonalVO> esPersonalVOList = new ArrayList<>();
        List<String> esPolicyNoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(esCurrentPolicyList)){
            //转换数据
            esPersonalVOList = transESVoToPersonalVO(esCurrentPolicyList);
            esPolicyNoList = esCurrentPolicyList.stream().map(PolicyEsVo::getContNo).distinct().collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(performanceVoList) && !ObjectUtils.isEmpty(performanceVoList)){
            //合并数据
            if (!CollectionUtils.isEmpty(esPersonalVOList)){
                combinedPersonalVoList(esPersonalVOList,performanceVoList);
            }
            allPerformanceVoList.addAll(performanceVoList);
        }else if (!CollectionUtils.isEmpty(esPersonalVOList)){
            allPerformanceVoList.addAll(esPersonalVOList);
        }
        TeamManageVO commissionVo = performanceDao.getCommissionTeamInfo(performanceMonth,saleTeamCode,null);

        if (!CollectionUtils.isEmpty(allPerformanceVoList)) {
            //获取各个指标项数据
            teamPerformanceVO.setPolicyNum(allPerformanceVoList.stream().map(PersonalVO::getCurrentMonthPolicyNum).filter(Objects::nonNull).reduce(0, Integer::sum));
            teamPerformanceVO.setFYC(allPerformanceVoList.stream().map(PersonalVO::getCurrentMonthFYC).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
            teamPerformanceVO.setFYP(allPerformanceVoList.stream().map(PersonalVO::getCurrentMonthFYP).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
            teamPerformanceVO.setDCP(allPerformanceVoList.stream().map(PersonalVO::getCurrentMonthDCP).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
            if (!ObjectUtils.isEmpty(commissionVo)){
                teamPerformanceVO.setActivePersonNum(commissionVo.getActivePersonNum());
                teamPerformanceVO.setStandardPersonNum(commissionVo.getStandardPersonNum());
            }else{
                teamPerformanceVO.setActivePersonNum((int)allPerformanceVoList.stream().filter(o -> !ObjectUtils.isEmpty(o.getCurrentMonthFYC()) && o.getCurrentMonthFYC().compareTo(BigDecimal.ZERO) > 0).count());
                teamPerformanceVO.setStandardPersonNum((int)allPerformanceVoList.stream().filter(o -> !ObjectUtils.isEmpty(o.getCurrentMonthFYC()) && o.getCurrentMonthFYC().compareTo(new BigDecimal("1000")) >= 0).count());
            }
            teamPerformanceVO.setPremiumPersonNum((int)allPerformanceVoList.stream().filter(o -> !ObjectUtils.isEmpty(o.getCurrentMonthFYP()) &&  o.getCurrentMonthFYP().compareTo(new BigDecimal("0")) > 0).count());
            teamPerformanceVO.setDCPPersonNum((int)allPerformanceVoList.stream().filter(o -> !ObjectUtils.isEmpty(o.getCurrentMonthDCP()) &&  o.getCurrentMonthDCP().compareTo(new BigDecimal("0")) > 0).count());
            if (teamPerformanceVO.getPolicyNum()!= null && teamPerformanceVO.getPolicyNum() != 0){
                teamPerformanceVO.setFYPPerPolicy(teamPerformanceVO.getFYP().divide(BigDecimal.valueOf(teamPerformanceVO.getPolicyNum()),2, RoundingMode.HALF_UP));
                teamPerformanceVO.setFYCPerPolicy(teamPerformanceVO.getFYC().divide(BigDecimal.valueOf(teamPerformanceVO.getPolicyNum()),2, RoundingMode.HALF_UP));
                teamPerformanceVO.setDCPPerPolicy(teamPerformanceVO.getDCP().divide(BigDecimal.valueOf(teamPerformanceVO.getPolicyNum()),2, RoundingMode.HALF_UP));
            }
            if (teamPerformanceVO.getPremiumPersonNum()!= null && teamPerformanceVO.getPremiumPersonNum()!= 0 ){
                teamPerformanceVO.setFYPPerPerson(teamPerformanceVO.getFYP().divide(BigDecimal.valueOf(teamPerformanceVO.getPremiumPersonNum()),2, RoundingMode.HALF_UP));
                teamPerformanceVO.setFYCPerPerson(teamPerformanceVO.getFYC().divide(BigDecimal.valueOf(teamPerformanceVO.getPremiumPersonNum()),2, RoundingMode.HALF_UP));
            }
            if (teamPerformanceVO.getDCPPersonNum()!= null && teamPerformanceVO.getDCPPersonNum()!= 0 ){
                teamPerformanceVO.setDCPPerPerson(teamPerformanceVO.getDCP().divide(BigDecimal.valueOf(teamPerformanceVO.getDCPPersonNum()),2, RoundingMode.HALF_UP));
            }
            if (!CollectionUtils.isEmpty(esPolicyNoList)){
                teamPerformanceVO.setCurrentDayPolicyNoList(esPolicyNoList);
            }
//            List<String> idList = new ArrayList<>();
//            allPerformanceVoList.forEach(o->{
//                String remark = o.getRemark();
//                if (!StringUtils.isEmpty(remark)){
//                    List<String> ids = Arrays.asList(remark.split(","));
//                    if (!CollectionUtils.isEmpty(ids)){
//                        idList.addAll(ids);
//                    }
//                }
//            });
//            if (!CollectionUtils.isEmpty(idList)){
//                teamPerformanceVO.setPolicyIdList(idList.stream().distinct().collect(Collectors.toList()));
//            }

        }

        //团队继续率
        dealCr13(saleTeamCode,performanceMonth,teamPerformanceVO);

    }

    private List<PersonalVO> getTeamPerformanceMonthEmpList(String saleTeamCode, String performanceMonth) {
        LocalDate lastDayOfMonth = LocalDate.parse(performanceMonth + "-01", df).with(TemporalAdjusters.lastDayOfMonth());
//        return  performanceDao.getTeamCurrentPersonQuitList(saleTeamCode);
        return performanceDao.getTeamMonthPersonQuitList(saleTeamCode,lastDayOfMonth);
    }

    private void dealCr13(String saleTeamCode,String performanceMonth, TeamPerformanceVO teamPerformanceVO) {
        List<TeamPerformanceVO> cr13InfoList = performanceDao.getTeamCrInfoByAgentCodeList(saleTeamCode,performanceMonth);
        if (CollectionUtils.isEmpty(cr13InfoList)){
            return;
        }
        BigDecimal m13AA = BigDecimal.ZERO;
        BigDecimal m13NA = BigDecimal.ZERO;
        for (TeamPerformanceVO cr : cr13InfoList) {
            String remark = cr.getIds();
            if (!org.apache.commons.lang3.StringUtils.isEmpty(remark)) {
                if (canJsonArray(remark)) {
                    List<ContinueRateInfo> continueRateInfoList = JSONArray.parseArray(remark, ContinueRateInfo.class);
                    if (!CollectionUtils.isEmpty(continueRateInfoList)){
                        List<ContinueRateInfo> r13List = continueRateInfoList.stream().filter(o -> !org.apache.commons.lang3.StringUtils.isEmpty(o.CONTINUE_RATE_CODE) && "R13".equals(o.CONTINUE_RATE_CODE)).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(r13List)){
                            ContinueRateInfo continueRateInfo = r13List.get(0);
                            m13AA = m13AA.add(new BigDecimal(com.alibaba.excel.util.StringUtils.isEmpty(continueRateInfo.NUMERATOR)?"0":continueRateInfo.NUMERATOR).setScale(0,BigDecimal.ROUND_DOWN));
                            m13NA = m13NA.add(new BigDecimal(com.alibaba.excel.util.StringUtils.isEmpty(continueRateInfo.DENOMINATOR)?"0":continueRateInfo.DENOMINATOR).setScale(0,BigDecimal.ROUND_DOWN));
                        }
                    }
                }
            }
        }
        teamPerformanceVO.setCr13Premium(m13NA);
        teamPerformanceVO.setCr13ActualPremium(m13AA);
        if (m13NA.compareTo(BigDecimal.ZERO)!=0){
            teamPerformanceVO.setCr13(m13AA.divide(m13NA,2,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")).setScale(2,BigDecimal.ROUND_HALF_UP));
        }else{
            teamPerformanceVO.setCr13(BigDecimal.ZERO);
        }

    }

    @Override
    public PerformancePolicyVO getTeamRangePerformancePolicyList(TeamPerformanceRequest request) {
        log.info("团队业绩保单明细查询参数,{}", JSON.toJSON(request));
        PerformancePolicyVO performancePolicyVOData = new PerformancePolicyVO();
        List<String> policyIdList = request.getPolicyIdList();
        List<String> currentDayPolicyNoList = request.getCurrentDayPolicyNoList();
        List<PerformancePolicyVO> performancePolicyVOList = new ArrayList<>();
        List<PerformancePolicyVO> esPolicyVOList = getEsCurrentPolicyByPolicyNo(currentDayPolicyNoList);
        List<PerformancePolicyVO> policyVOList = getPerformancePolicyListById(policyIdList);
        if (!CollectionUtils.isEmpty(esPolicyVOList)){
            performancePolicyVOList.addAll(esPolicyVOList);
        }
        if (!CollectionUtils.isEmpty(policyVOList)){
            performancePolicyVOList.addAll(policyVOList);
        }
        performancePolicyVOData.setPolicyVOList(performancePolicyVOList);
        return performancePolicyVOData;
//        performancePolicyVOData.setPolicyNoList(Arrays.asList("P992400039599","P442411025111","P992400039590","P442411020776","P442411030421"));
//        for (int i = 0; i < 4; i++) {
//            PerformancePolicyVO performancePolicyVO = new PerformancePolicyVO();
//            performancePolicyVO.setPolicyNo(UUID.randomUUID().toString());
//            performancePolicyVO.setPaymentYears("30年");
//            performancePolicyVO.setInsureYears("30年");
//            performancePolicyVO.setInsuranceAmount(BigDecimal.valueOf(Math.random() * 1000000).setScale(2, RoundingMode.HALF_UP));
//            performancePolicyVO.setPremium(BigDecimal.valueOf(Math.random() * 1000000).setScale(2, RoundingMode.HALF_UP));
//            performancePolicyVO.setPolicyState("有效");
//            performancePolicyVO.setAgentCode("代理人code");
//            performancePolicyVO.setAgentName("代理人名称");
//            performancePolicyVO.setTradeType("有效");
//            performancePolicyVO.setSettleDate(LocalDate.now());
//            performancePolicyVO.setPerformanceDate(LocalDate.now());
//            performancePolicyVO.setSettleFlag("已结算");
//            performancePolicyVO.setRiskPeriod(i<1?"M":"L");
//            performancePolicyVO.setRiskPeriodName(i<1?"短险":"长险");
//            performancePolicyVO.setQqdFlag("已续缴");
//            performancePolicyVO.setQqdDate(LocalDate.now());
//            performancePolicyVO.setRiskCode(UUID.randomUUID().toString());
//            performancePolicyVO.setRiskName("险种名称");
//            performancePolicyVO.setAppntName("投保人名称");
//            performancePolicyVO.setInsuredName("被保人名称");
//            performancePolicyVO.setAmount(BigDecimal.valueOf(Math.random() * 100).setScale(2, RoundingMode.HALF_UP));
//            performancePolicyVOList.add(performancePolicyVO);
//        }
//        performancePolicyVOData.setPolicyVOList(performancePolicyVOList);
//        return performancePolicyVOData;
    }

    private List<PerformancePolicyVO> getPerformancePolicyListById(List<String> policyIdList) {
        if (CollectionUtils.isEmpty(policyIdList)){
            return new ArrayList<>();
        }
        return performanceDao.getPerformancePolicyListById(policyIdList);
    }

    private List<PerformancePolicyVO> getEsCurrentPolicyByPolicyNo(List<String> currentDayPolicyNoList) {
        List<PolicyEsVo> esCurrentPolicyList = getEsCurrentPolicyList(null, null, currentDayPolicyNoList, null);
        return transEsVoTOPolicyVO(esCurrentPolicyList);
    }

    @Override
    public List<String> getPerformancePolicyNoList(String empCode, String queryType, String performanceMonth) {
        List<PerformancePolicyVO> policyVOList = new ArrayList<>();
        if (StringUtils.isEmpty(empCode)){
            return new ArrayList<>();
        }else if (!checkEmpAccess(empCode, performanceMonth)){
            log.info("查询失败,权限不足:{}",empCode);
            return new ArrayList<>();
        }
        log.info("个人当月收入业绩查询,{}", empCode);
        //判断是否当月
        boolean isCurrentMonth = performanceMonth.equals(new SimpleDateFormat("yyyy-MM").format(new Date()));
        List<PolicyEsVo> policyEsVoList = new ArrayList<>();
        List<String> wtPolNoList = new ArrayList<>();
        if (isCurrentMonth){
            if ("UW".equals(queryType)){
                policyEsVoList = getEsCurrentPolicyList(empCode, null, null, null);
                wtPolNoList = getEsCurrentDayWtPolNoList(empCode, performanceMonth);
            }
        }
        List<PerformancePolicyVO> performancePolicyInfoList = performanceDao.getPerformanceMonthPolicyInfo(empCode, performanceMonth, wtPolNoList, null, queryType);
        //转换添加ES今日承保数据
        performancePolicyInfoList.addAll(transEsVoTOPolicyVO(policyEsVoList));
        if (!CollectionUtils.isEmpty(performancePolicyInfoList)){
            return performancePolicyInfoList.stream().map(PerformancePolicyVO::getPolicyNo).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public List<PerformancePolicyVO> getPerformancePolicyList(String empCode, String qureyString) {
        log.info("个人当月收入业绩查询,{}", empCode);
        if (StringUtils.isEmpty(empCode)){
            empCode = getCurrentLoginEmpCode();
            if (StringUtils.isEmpty(empCode)){
                log.info("校验失败,未获取到当前登录人员信息");
                return new ArrayList<>();
            }
        }else if (!checkEmpAccess(empCode, null)){
            log.info("查询失败,权限不足:{}",empCode);
            return new ArrayList<>();
        }

        List<String> wtPolNoList = getEsCurrentDayWtPolNoList(null, null);
        List<PolicyEsVo> esCurrentPolicyList = new ArrayList<>();
        if (qureyString.matches(".*" + "[\\u4e00-\\u9fa5]" + ".*")){
            //含有汉字判断为名字
            esCurrentPolicyList = getEsCurrentPolicyList(empCode, null, null,qureyString);
        }else{
            esCurrentPolicyList = getEsCurrentPolicyList(empCode, null, Collections.singletonList(qureyString), null);
        }

        List<PerformancePolicyVO> performancePolicyList = performanceDao.getPerformancePolicyList(empCode, qureyString,wtPolNoList);
        performancePolicyList.addAll(transEsVoTOPolicyVO(esCurrentPolicyList));
        if (!CollectionUtils.isEmpty(performancePolicyList)){
            //按保单号以及主附加险排重
            performancePolicyList = transByPolicyNo(performancePolicyList);
//            //附加险转换
//            List<PerformancePolicyVO>  subPolicyList = performancePolicyList.stream().filter(o->"S".equals(o.getSubRiskFlag())).collect(Collectors.toList());
//            if (!CollectionUtils.isEmpty(subPolicyList)){
//                performancePolicyList = performancePolicyList.stream().filter(o->!"S".equals(o.getSubRiskFlag())).collect(Collectors.toList());
//                if (!CollectionUtils.isEmpty(performancePolicyList)){
//                    for (PerformancePolicyVO subPolicy : subPolicyList) {
//                        performancePolicyList.stream().filter(o -> o.getPolicyNo().equals(subPolicy.getPolicyNo()) && o.getSource().equals(subPolicy.getSource())).findFirst().ifPresent(o -> {
//                            o.setAmount(addBigDecimal(o.getAmount(), subPolicy.getAmount()));
//                            o.setPremium(addBigDecimal(o.getPremium(), subPolicy.getPremium()));
//                        });
//                    }
//                }
//            }
        }
        return performancePolicyList;
    }

    @Override
    public PolicyCommissionVO getPolicyCommissionList(String policyNo) {
        PolicyCommissionVO commissionVO = new PolicyCommissionVO();
        List<PolicyCommissionVO> list = new ArrayList<>();
        //获取数据
        List<PolicyCommissionVO> commissionVOList = performanceDao.getPolicyCommissionList(policyNo);
        if (CollectionUtils.isEmpty(commissionVOList)){
            return commissionVO;
        }
        commissionVOList.forEach(vo -> {
            if (StringUtils.isEmpty(vo.getCommissionType())){
                vo.setCommissionType(CommissionItemEnum.getLabelByValue(vo.getCommissionItem()));
            }
        });
        //排序
        List<PolicyCommissionVO> fycList = commissionVOList.stream().filter(vo -> "首期佣金".equals(vo.getCommissionType())).sorted(Comparator.comparing(PolicyCommissionVO::getSettleDate,Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
        List<PolicyCommissionVO> otherList = commissionVOList.stream().filter(vo -> !"首期佣金".equals(vo.getCommissionType())).sorted(Comparator.comparing(PolicyCommissionVO::getSettleDate,Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
        list.addAll(fycList);
        list.addAll(otherList);
        PolicyCommissionVO first = list.get(0);
        BeanUtils.copyProperties(first, commissionVO);
        commissionVO.setCommissionVOList(list);
        return commissionVO;
    }

    @Override
    public PerformancePolicyVO queryTeamRangePerformancePolicyList(TeamPerformanceRequest request) {
        log.info("团队业绩保单明细查询参数,{}", JSON.toJSON(request));
        PerformancePolicyVO performancePolicyVOData = new PerformancePolicyVO();
        List<PerformancePolicyVO> performancePolicyVOList = new ArrayList<>();

        //获取ES今日数据
        List<PerformancePolicyVO> esPolicyVOList = getEsCurrentPolicyByPolicyNo(request.getCurrentDayPolicyNoList());
        if (!CollectionUtils.isEmpty(esPolicyVOList)){
            performancePolicyVOList.addAll(esPolicyVOList);
        }

        //获取销管业绩数据
        List<PerformancePolicyVO> teamPerformancePolicyList = getTeamPerformancePolicyList(request);
        if (!CollectionUtils.isEmpty(teamPerformancePolicyList)){
            performancePolicyVOList.addAll(teamPerformancePolicyList);
        }
        //按保单号以及主附加险排重
        performancePolicyVOList = transByPolicyNo(performancePolicyVOList);
        performancePolicyVOData.setPolicyVOList(performancePolicyVOList);
        return performancePolicyVOData;
    }

    @Override
    public PerformanceVO getPersonalPerformance(String empCode,String loginEmpCode,String queryYear, String queryMonth)  {
        log.info("个人目标业绩查询,{},{},{},{}", empCode,loginEmpCode, queryYear, queryMonth);
        PerformanceVO targetPerformanceVO = new PerformanceVO();
        nullHandle(targetPerformanceVO);
        if (StringUtils.isEmpty(queryYear) && StringUtils.isEmpty(queryMonth)){
            return targetPerformanceVO;
        }
        //初始化数据
        List<String>  appntNoList = new ArrayList<>();
        List<PolicyEsVo>  policyEsVoList = new ArrayList<>();
        List<String> wtPolNoList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String currentMonth = formatter.format(LocalDate.now());
        String currentYear = currentMonth.substring(0, 4);
        //权限校验
        if (StringUtils.isEmpty(loginEmpCode)){
            if(checkEmpAccess(empCode,currentMonth.equals(queryMonth)?null:queryMonth)){
                log.info("查询失败,权限不足:{},{}",empCode,loginEmpCode);
                return targetPerformanceVO;
            }
        }else{
            if(!checkLoginEmpAccess(empCode,loginEmpCode,currentMonth.equals(queryMonth)?null:queryMonth)){
                log.info("查询失败,权限不足:{},{}",empCode,loginEmpCode);
                return targetPerformanceVO;
            }
        }

        //异步获取基本法类型
        String actualMonth ;
        if (!StringUtils.isEmpty(queryMonth)){
            actualMonth = queryMonth;
        }else{
            if (!currentYear.equals(queryYear)){
                actualMonth = queryYear + "-12";
            }else{
                actualMonth = currentMonth;
            }
        }
        if (!StringUtils.isEmpty(empCode) && empCode.startsWith("S")){
            return getSupervisorPerformanceVO(empCode,actualMonth);
        }
        CompletableFuture<String> versionTypeFuture = CompletableFuture.supplyAsync(() -> {
            return getBasicLawVersionType(empCode, null,actualMonth);
        });
        //异步获取增员人数量
        String finalQueryMonth = queryMonth;
        CompletableFuture<Integer> increaseNumFuture = CompletableFuture.supplyAsync(() -> {
            return getIncreaseNum(empCode, null, finalQueryMonth,queryYear);
        });

        //判断查询
        if (currentMonth.equals(queryMonth) || (StringUtils.isEmpty(queryMonth) && currentYear.equals(queryYear))){
            //查询当日ES保单数据
            policyEsVoList = getEsCurrentPolicyList(empCode, null, null, null);
            //获取今日承保人号
            appntNoList = getESAppntNoList(appntNoList, policyEsVoList);
            //查询当日犹退保单号
            wtPolNoList = getEsCurrentDayWtPolNoList(empCode,null);
        }
        if (!StringUtils.isEmpty(queryMonth)){
            //获取销管数据
            targetPerformanceVO = performanceDao.getMonthPersonalPerformance(empCode, queryMonth,wtPolNoList,appntNoList);
        }else {
            if (currentYear.equals(queryYear)){
                queryMonth = currentMonth;
            }else{
                queryMonth = queryYear + "-12";
            }
            //获取销管数据
            targetPerformanceVO = performanceDao.getYearPersonalPerformance(empCode, queryYear,wtPolNoList,appntNoList);
        }
        if (targetPerformanceVO == null){
            targetPerformanceVO = new PerformanceVO();
        }
        if (!StringUtils.isEmpty(queryYear) && !currentYear.equals(queryYear)){
            //月均FYC
            getTargetPerformanceMonthFyc(empCode,queryYear,targetPerformanceVO);
            //目前往年fyc使用同一字段
            targetPerformanceVO.setFycAmount(targetPerformanceVO.getAvgMonthFyc());
        }
        //组装ES与查询数据
        combinedESData(targetPerformanceVO,policyEsVoList);
        //查询13月继续率
        getCr13(targetPerformanceVO,empCode,queryMonth);
        //null处理
        nullHandle(targetPerformanceVO);
        //基本法类型处理
        targetPerformanceVO.setVersionType(versionTypeFuture.join());
        targetPerformanceVO.setIncreaseNum(increaseNumFuture.join());
        log.info("个人目标业绩查询结束,{}", JSON.toJSON(targetPerformanceVO));
        return targetPerformanceVO;
    }

    private PerformanceVO getSupervisorPerformanceVO(String empCode, String queryMonth) {
        PerformanceVO vo = new PerformanceVO();
        //获取督导账号信息
        SupervisorEmployee supervisorEmployee = supervisorEmployeeMapper.getSupervisorEmployeeByCode(empCode);
        if (ObjectUtils.isEmpty(supervisorEmployee) || StringUtils.isEmpty(supervisorEmployee.getTeamCode())){
            log.info("督导账号目前无团队信息");
            return vo;
        }
        String versionType = performanceDao.getTeamVersionType(supervisorEmployee.getTeamCode(),queryMonth);
        vo.setVersionType(versionType);
        nullHandle(vo);
        return vo;
    }

    private Integer getIncreaseNum(String empCode, String teamCode, String actualMonth, String queryYear) {
        if (StringUtils.isEmpty(actualMonth) && StringUtils.isEmpty(queryYear)){
            return 0;
        }
        if (!StringUtils.isEmpty(empCode)){
            return performanceDao.getAgentIncreaseNum(empCode,actualMonth,queryYear);
        }
        if (!StringUtils.isEmpty(teamCode)){
            return performanceDao.getTeamIncreaseNum(teamCode,actualMonth,queryYear);
        }
        return 0;
    }

    private void nullHandle(PerformanceVO targetPerformanceVO) {
        targetPerformanceVO.setFycAmount(targetPerformanceVO.getFycAmount() == null ? BigDecimal.ZERO : targetPerformanceVO.getFycAmount());
        targetPerformanceVO.setAvgMonthFyc(targetPerformanceVO.getAvgMonthFyc() == null ? BigDecimal.ZERO : targetPerformanceVO.getAvgMonthFyc());
        targetPerformanceVO.setPeriodPremium(targetPerformanceVO.getPeriodPremium() == null ? BigDecimal.ZERO : targetPerformanceVO.getPeriodPremium());
        targetPerformanceVO.setFycPremium(targetPerformanceVO.getFycPremium() == null ? BigDecimal.ZERO : targetPerformanceVO.getFycPremium());
        targetPerformanceVO.setAppntnoNum(targetPerformanceVO.getAppntnoNum() == null ? 0 : targetPerformanceVO.getAppntnoNum());
        targetPerformanceVO.setPeriodDCP(targetPerformanceVO.getPeriodDCP() == null ? BigDecimal.ZERO : targetPerformanceVO.getPeriodDCP());
        targetPerformanceVO.setCr13(targetPerformanceVO.getCr13() == null ? BigDecimal.ZERO : targetPerformanceVO.getCr13());
        targetPerformanceVO.setPeriodPolicyNum(targetPerformanceVO.getPeriodPolicyNum() == null ? 0 : targetPerformanceVO.getPeriodPolicyNum());
        targetPerformanceVO.setFycPolicyNum(targetPerformanceVO.getFycPolicyNum() == null ? 0 : targetPerformanceVO.getFycPolicyNum());
        targetPerformanceVO.setDcp(targetPerformanceVO.getDcp() == null ? BigDecimal.ZERO : targetPerformanceVO.getDcp());
        targetPerformanceVO.setIncreaseNum(targetPerformanceVO.getIncreaseNum() == null ? 0 : targetPerformanceVO.getIncreaseNum());
    }

    private boolean checkLoginEmpAccess(String empCode, String loginEmpCode,String performanceMonth) {
        if (!StringUtils.isEmpty(loginEmpCode) && !empCode.equals(loginEmpCode)) {
            //查询目标人信息
            EmployeeVO targetEmpInfo = saleTeamDao.getEmployeeInfo(empCode);
            if (!StringUtils.isEmpty(performanceMonth)){
                log.info("追溯历史团队,{},{}",empCode,performanceMonth);
                String saleTeamCode = saleTeamDao.getEmpTargetSaleteamCode(empCode,performanceMonth);
                if (!StringUtils.isEmpty(saleTeamCode)){
                    log.info("追溯到历史团队,{},{},{}",empCode,performanceMonth,saleTeamCode);
                    targetEmpInfo.setTeamCode(saleTeamCode);
                }
            }
            if (ObjectUtils.isEmpty(targetEmpInfo)) {
                log.info("校验失败,未获取到目标人员信息:{}", empCode);
                return false;
            }
            if (loginEmpCode.startsWith("S")) {
                if (StringUtils.isEmpty(targetEmpInfo.getOrgCode())) {
                    log.info("校验失败,未获取到目标人员机构信息:{}", empCode);
                    return false;
                }
                //督导账号查询机构权限
                List<String> orgList = getSupervisorOrgList(loginEmpCode);
                if (CollectionUtils.isEmpty(orgList)) {
                    log.info("校验失败,未获取到当前登录人员机构权限信息:{}", loginEmpCode);
                    return false;
                }
                if (orgList.contains(targetEmpInfo.getOrgCode())) {
                    return true;
                } else {
                    log.info("校验失败,督导账号机构权限匹配失败,当前登录人员机构权限信息:{},{},目标人员机构信息:{},{}", loginEmpCode, orgList, empCode, targetEmpInfo.getOrgCode());
                    return false;
                }
            } else {
                //查询团队
                EmployeeVO loginEmpInfo = saleTeamDao.getEmployeeInfo(loginEmpCode);
                if (ObjectUtils.isEmpty(loginEmpInfo)) {
                    log.info("校验失败,未获取到当前登录人员信息:{}", loginEmpCode);
                    return false;
                }
                if (StringUtils.isEmpty(loginEmpInfo.getEmpInCode()) || !loginEmpCode.equals(loginEmpInfo.getTeamLeaderCode())) {
                    log.info("校验失败,当前登录人员非团队主管:{},{}", loginEmpCode, loginEmpInfo.getTeamLeaderInCode());
                    return false;
                }
                //查询目标人团队
                if (!StringUtils.isEmpty(targetEmpInfo.getTeamCode()) && !StringUtils.isEmpty(targetEmpInfo.getTopCode()) && targetEmpInfo.getTeamCode().equals(loginEmpInfo.getTeamCode())) {
                    return true;
                } else {
                    log.info("校验失败,团队权限匹配失败,{},{},{},{}", loginEmpCode, loginEmpInfo.getTeamCode(), empCode, targetEmpInfo.getTeamCode());
                    return false;
                }
            }
        } else {
            return true;
        }
    }

    private void getTargetPerformanceMonthFyc(String empCode, String queryYear, PerformanceVO targetPerformanceVO) {
        if (ObjectUtils.isEmpty(targetPerformanceVO.getFycAmount())){
            return;
        }
        BigDecimal fycAmount = targetPerformanceVO.getFycAmount();
        EmployeeVO empInfo = teamService.getEmpInfo(empCode);
        if (!ObjectUtils.isEmpty(empInfo)){
            LocalDateTime entryTime = empInfo.getEntryTime();
            if (!ObjectUtils.isEmpty(entryTime)){
                LocalDate entryDate = entryTime.toLocalDate();
                int year = entryDate.getYear();
                int monthValue = entryDate.getMonthValue();
                if (year < Integer.parseInt(queryYear) ){
                    targetPerformanceVO.setAvgMonthFyc(fycAmount.divide(new BigDecimal(12), 2, BigDecimal.ROUND_HALF_UP));
                }else if (year == Integer.parseInt(queryYear)){
                    targetPerformanceVO.setAvgMonthFyc(fycAmount.divide(new BigDecimal(12 - monthValue + 1), 2, BigDecimal.ROUND_HALF_UP));
                }
            }
        }
    }


    private void getCr13(PerformanceVO targetPerformanceVO, String empCode, String queryMonth) {
        PersonalVO empCr = performanceDao.getEmpCr(empCode, queryMonth);
        if (!ObjectUtils.isEmpty(empCr)){
            String cr13 = empCr.getCr13();
            if (StringUtils.isEmpty(cr13)){
                return;
            }
            targetPerformanceVO.setCr13(new BigDecimal(cr13).multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP));
        }
    }

    @Nullable
    private List<String> getESAppntNoList(List<String> appntNoList, List<PolicyEsVo> policyEsVoList) {
        if (!CollectionUtils.isEmpty(policyEsVoList)){
            appntNoList = policyEsVoList.stream().map(o -> {
                if (!ObjectUtils.isEmpty(o.getAppntEsVo())) {
                    return o.getAppntEsVo().getCustomerNo();
                }
                return null;
            }).filter(o -> !ObjectUtils.isEmpty(o)).distinct().collect(Collectors.toList());
        }
        return appntNoList;
    }

    private void combinedESData(PerformanceVO targetPerformanceVO, List<PolicyEsVo> policyEsVoList) {
        if (CollectionUtils.isEmpty(policyEsVoList)){
            return;
        }
        if (ObjectUtils.isEmpty(targetPerformanceVO.getFycPremium())){
            targetPerformanceVO.setFycPremium(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(targetPerformanceVO.getFycPolicyNum())){
            targetPerformanceVO.setFycPolicyNum(0);
        }
        //保费
        BigDecimal bigDecimal = policyEsVoList.stream().map(PolicyEsVo::getPrem).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP);
        targetPerformanceVO.setFycPremium(targetPerformanceVO.getFycPremium().add(bigDecimal));
        //保单件数
        targetPerformanceVO.setFycPolicyNum(targetPerformanceVO.getFycPolicyNum()+policyEsVoList.size());
        //签单客户
//        List<String> appntNoList = policyEsVoList.stream().map(o -> {
//            if (!ObjectUtils.isEmpty(o.getAppntEsVo())) {
//                return o.getAppntEsVo().getCustomerNo();
//            }
//            return null;
//        }).filter(o -> !ObjectUtils.isEmpty(o)).distinct().collect(Collectors.toList());
//        if (!CollectionUtils.isEmpty(appntNoList)){
//            if (targetPerformanceVO.getAppntnoNum()==null){
//                targetPerformanceVO.setAppntnoNum(0);
//            }
//            targetPerformanceVO.setAppntnoNum(targetPerformanceVO.getAppntnoNum()+appntNoList.size());
//        }
        //筛选期交保单 交费方式不为【趸缴】/【一次性交清】and 长短险为【长险】的保单
        List<PolicyEsVo> periodPolicyList = policyEsVoList.stream().filter(o -> {
            List<RiskEsVo> riskVoList = o.getRiskVoList();
            if (!CollectionUtils.isEmpty(riskVoList)){
                return "L".equals(riskVoList.get(0).getRiskPeriod()) && !"0".equals(riskVoList.get(0).getPayIntv());
            }
            return false;
        }).collect(Collectors.toList());
        //期交保费
        if (!CollectionUtils.isEmpty(periodPolicyList)){
            if (targetPerformanceVO.getPeriodPremium()==null){
                targetPerformanceVO.setPeriodPremium(BigDecimal.ZERO);
            }
            if (targetPerformanceVO.getPeriodPolicyNum()==null){
                targetPerformanceVO.setPeriodPolicyNum(0);
            }
            targetPerformanceVO.setPeriodPremium(targetPerformanceVO.getPeriodPremium().add(periodPolicyList.stream().map(PolicyEsVo::getPrem).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP)));
            targetPerformanceVO.setPeriodPolicyNum(targetPerformanceVO.getPeriodPolicyNum()+periodPolicyList.size());
        }

    }



    private void initData(PerformanceVO targetPerformanceVO) {
        targetPerformanceVO.setFycAmount(new BigDecimal(Math.random()).multiply(new BigDecimal(10000)).setScale(2, BigDecimal.ROUND_HALF_UP));
        targetPerformanceVO.setPeriodPremium(new BigDecimal(Math.random()).multiply(new BigDecimal(10000)).setScale(2, BigDecimal.ROUND_HALF_UP));
        targetPerformanceVO.setFycPremium(new BigDecimal(Math.random()).multiply(new BigDecimal(10000)).setScale(2, BigDecimal.ROUND_HALF_UP));
        targetPerformanceVO.setAppntnoNum(new BigDecimal(Math.random()).multiply(new BigDecimal(10)).setScale(2, BigDecimal.ROUND_HALF_UP).intValue());
        targetPerformanceVO.setPeriodDCP(new BigDecimal(Math.random()).multiply(new BigDecimal(10000)).setScale(2, BigDecimal.ROUND_HALF_UP));
        targetPerformanceVO.setCr13(new BigDecimal(Math.random()).setScale(4, BigDecimal.ROUND_HALF_UP));
        targetPerformanceVO.setPeriodPolicyNum(new BigDecimal(Math.random()).multiply(new BigDecimal(10)).setScale(2, BigDecimal.ROUND_HALF_UP).intValue());
        targetPerformanceVO.setFycPolicyNum(new BigDecimal(Math.random()).multiply(new BigDecimal(10)).setScale(2, BigDecimal.ROUND_HALF_UP).intValue());
    }

    @Override
    public PerformanceVO getTeamTargetPerformance(String saleTeamCode,String loginEmpCode, String queryYear, String queryMonth) throws ExecutionException, InterruptedException {
        log.info("团队目标业绩查询,{},{},{},{}", saleTeamCode,loginEmpCode, queryYear, queryMonth);
        PerformanceVO targetPerformanceVO = new PerformanceVO();
        nullHandle(targetPerformanceVO);
        if (StringUtils.isEmpty(queryYear)&& StringUtils.isEmpty(queryMonth)){
            return targetPerformanceVO;
        }
        targetPerformanceVO.setFycAmount(null);
        targetPerformanceVO.setAppntnoNum(null);
        TeamManageVO teamManageVO = new TeamManageVO();
        if (StringUtils.isEmpty(loginEmpCode)){
            if (!checkTeamAccess(saleTeamCode,teamManageVO)){
                log.info("当前登录代理人无权限访问团队");
                return targetPerformanceVO;
            }
        }else{
            if (!checkLoginTeamAccess(saleTeamCode,loginEmpCode,teamManageVO)){
                log.info("当前登录代理人无权限访问团队");
                return targetPerformanceVO;
            }
        }
        //团队转换
        if (!StringUtils.isEmpty(loginEmpCode)){
            if (loginEmpCode.startsWith("S")){
                Tbsaleteam saleTeam = saleTeamDao.getTeamSaleTeam(saleTeamCode);
                if (ObjectUtils.isEmpty(saleTeam)){
                    log.info("督导账号查询组级团队失败,{}",saleTeamCode);
                    return targetPerformanceVO;
                }
                saleTeamCode = saleTeam.getSaleteamcode();
            }
        }else
        if (getCurrentLoginEmpCode().startsWith("S")){
            Tbsaleteam saleTeam = saleTeamDao.getTeamSaleTeam(saleTeamCode);
            if (ObjectUtils.isEmpty(saleTeam)){
                log.info("督导账号查询组级团队失败,{}",saleTeamCode);
                return targetPerformanceVO;
            }
            saleTeamCode = saleTeam.getSaleteamcode();
        }
        //初始化数据
        List<String>  appntNoList = new ArrayList<>();
        List<PolicyEsVo>  policyEsVoList = new ArrayList<>();
        List<String> wtPolNoList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String currentMonth = formatter.format(LocalDate.now());
        String currentYear = currentMonth.substring(0, 4);
        Integer month = 0;


        //异步获取基本法类型
        String actualMonth ;
        if (!StringUtils.isEmpty(queryMonth)){
            actualMonth = queryMonth;
        }else{
            if (!currentYear.equals(queryYear)){
                actualMonth = queryYear + "-12";
            }else{
                actualMonth = currentMonth;
            }
        }
        String finalSaleTeamCode = saleTeamCode;
        CompletableFuture<String> versionTypeFuture = CompletableFuture.supplyAsync(() -> {
            return getBasicLawVersionType(null, finalSaleTeamCode,actualMonth);
        });
        //异步获取增员人数量
        String finalQueryMonth = queryMonth;
        CompletableFuture<Integer> increaseNumFuture = CompletableFuture.supplyAsync(() -> {
            return getIncreaseNum(null, finalSaleTeamCode, finalQueryMonth,queryYear);
        });

        //查询数据
        List<PersonalVO> empInfoList = new ArrayList<>();
        if (currentMonth.equals(queryMonth) || (StringUtils.isEmpty(queryMonth) && currentYear.equals(queryYear))){
            //查询本月在职和离职人员
            empInfoList = performanceDao.getTeamCurrentPersonQuitList(saleTeamCode);
        }else{
            //查询目前在职人员
//            empInfoList = performanceDao.getTeamCurrentPersonQuitList(saleTeamCode);
            empInfoList = performanceDao.getTeamCurrentPersonList(saleTeamCode);
        }
        if (CollectionUtils.isEmpty(empInfoList)){
            log.info("查询团队成员为空,{}",saleTeamCode);
            targetPerformanceVO.setVersionType(versionTypeFuture.join());
            return targetPerformanceVO;
        }
        List<String> agentCodeList = empInfoList.stream().map(PersonalVO::getAgentCode).distinct().collect(Collectors.toList());
        if (currentMonth.equals(queryMonth) || (StringUtils.isEmpty(queryMonth) && currentYear.equals(queryYear))){
            //查询当日ES保单数据
            policyEsVoList = getEsCurrentPolicyList(null, agentCodeList, null, null);
            //获取今日承保人号
            appntNoList = getESAppntNoList(appntNoList, policyEsVoList);
            //查询当日犹退保单号
            wtPolNoList = getEsCurrentDayWtPolNoList(null,null);
        }
        if (!StringUtils.isEmpty(queryMonth)){
            //获取销管数据
            targetPerformanceVO = performanceDao.getMonthTeamPerformance(agentCodeList, queryMonth,wtPolNoList,appntNoList);
        }else {
            if (currentYear.equals(queryYear)){
                queryMonth = currentMonth;
            }else{
                queryMonth = queryYear + "-12";
            }
            //获取销管数据
            targetPerformanceVO = performanceDao.getYearTeamPerformance(agentCodeList, queryYear,wtPolNoList,appntNoList);
        }
        if (targetPerformanceVO == null){
            targetPerformanceVO = new PerformanceVO();
        }
        //组装ES与查询数据
        combinedESData(targetPerformanceVO,policyEsVoList);
        targetPerformanceVO.setAppntnoNum(null);
        //查询13月继续率
        getTeamCr13(targetPerformanceVO,agentCodeList,queryMonth);
        //基本法类型处理
        targetPerformanceVO.setVersionType(versionTypeFuture.get());
        targetPerformanceVO.setIncreaseNum(increaseNumFuture.get());
        //null处理
        nullHandle(targetPerformanceVO);
        log.info("团队目标业绩查询结束,{}", JSON.toJSON(targetPerformanceVO));
        return targetPerformanceVO;
//        if ("month".equals(queryType)){
//            if ("1".equals(yearFlag)){
//                initData(targetPerformanceVO);
//                targetPerformanceVO.setFycAmount(null);
//                targetPerformanceVO.setAppntnoNum(null);
//            }else{
//                initData(targetPerformanceVO);
//                targetPerformanceVO.setFycAmount(null);
//                targetPerformanceVO.setAppntnoNum(null);
//            }
//        }else if ("year".equals(queryType)){
//            if ("1".equals(yearFlag)){
//                initData(targetPerformanceVO);
//                targetPerformanceVO.setFycAmount(null);
//            }else{
//                initData(targetPerformanceVO);
//                targetPerformanceVO.setFycAmount(null);
//                targetPerformanceVO.setAppntnoNum(null);
//            }
//        }
    }

    @Override
    public List<String> getBasicLawVersionTypeList(String employeeCode) {
        log.info("查询业务员基础法版本类型,{}", employeeCode);
        List<String> versionTypeList = new ArrayList<>();
        List<String> showList = new ArrayList<>();
        List<String> list = new ArrayList<>();
        if (!StringUtils.isEmpty(lotusOrgList)){
            list = Arrays.asList(lotusOrgList.split(","));
        }

        try {
            if (employeeCode.startsWith("S")){
                List<String> orgList = getSupervisorOrgList(employeeCode);
                log.info("获取督导账号权限机构,{},{}", employeeCode, JSON.toJSON(orgList));
                //查询机构
                versionTypeList = performanceDao.getOrgListVersionTypeList(orgList);
                if (!CollectionUtils.isEmpty(orgList)){
                    if (!StringUtils.isEmpty(lotusOrgList)){
                        if (!CollectionUtils.isEmpty(list)){
                            //若list与orgList有交叉
                            if (orgList.stream().anyMatch(list::contains)){
                                versionTypeList.add("SELF_LOTUS");
                            }
                        }
                    }
                }
            }else{
                //查询按团队按机构配置基本法
                versionTypeList = performanceDao.getVersionTypeListByEmpTeam(employeeCode,list);
            }

            //目前只展示金莲花与2024
            if (!CollectionUtils.isEmpty(versionTypeList)){
                //默认展示金莲花 切换2024
                if (versionTypeList.contains("SELF_LOTUS")){
                    showList.add("SELF_LOTUS");
                }
                if (versionTypeList.contains("SELF_2024")){
                    showList.add("SELF_2024");
                }
            }
        }catch (Exception e){
            log.info("查询业务员基础法版本类型异常,{}", employeeCode, e);
        }

        log.info("业务员基础法版本类型列表,{}", JSON.toJSON(showList));
        return showList;
    }

    @Override
    public String getBasicLawVersionType(String employeeCode, String teamCode, String commissionMonth) {
        log.info("追溯基本法类型,{},{},{}", employeeCode, teamCode, commissionMonth);
        String versionType = "";
        if (StringUtils.isEmpty(employeeCode) && StringUtils.isEmpty(teamCode)){
            return versionType;
        }
        //初始化月份
        if (StringUtils.isEmpty(commissionMonth)){
            commissionMonth = DateUtils.format(new Date()).substring(0,7);
        }
        //督导账号初始化团队
        if (!StringUtils.isEmpty(employeeCode)){
            if (employeeCode.startsWith("S")){
                SupervisorEmployee supervisorEmployee = supervisorEmployeeMapper.getSupervisorEmployeeByCode(employeeCode);
                if (!ObjectUtils.isEmpty(supervisorEmployee)){
                    teamCode = supervisorEmployee.getTeamCode();
                }
            }else{
                teamCode =  performanceDao.getAgentSaleTeamCode(employeeCode,commissionMonth);
            }
            if (StringUtils.isEmpty(teamCode)){
                log.info("查询失败,未追溯到团队信息");
                return versionType;
            }
        }
        log.info("追溯团队基本法类型,{},{}", teamCode, commissionMonth );
        versionType = performanceDao.getTeamVersionType(teamCode,commissionMonth);
        if (StringUtils.isEmpty(versionType)){
            log.info("追溯团队基本法类型为空,{}", teamCode);
            return "SELF_2024";
        }else{
            return versionType;
        }
    }

    private boolean checkLoginTeamAccess(String saleTeamCode, String loginEmpCode, TeamManageVO teamManageVO) {
        if (StringUtils.isEmpty(loginEmpCode)) {
            return true;
        }
        //查询目标人信息
        Tbsaleteam targetSaleTeam = saleTeamDao.getPerformanceSaleTeamByCode(saleTeamCode);
        if (ObjectUtils.isEmpty(targetSaleTeam) ){
            log.info("校验失败,未获取到当前团队信息");
            return false;
        }
        if (!ObjectUtils.isEmpty(teamManageVO)){
            teamManageVO.setTeamCode(targetSaleTeam.getSaleteamcode());
            teamManageVO.setTeamName(targetSaleTeam.getSaleteamname());
            teamManageVO.setSaleTeamInCode(targetSaleTeam.getSaleteamincode());
            teamManageVO.setEmpCode(targetSaleTeam.getEmpincode());
        }
        if (loginEmpCode.startsWith("S")) {
            if (StringUtils.isEmpty(targetSaleTeam.getInstCode())){
                log.info("校验失败,未获取到目标团队机构信息:{}", saleTeamCode);
                return false;
            }
            //督导账号查询机构权限
            List<String> orgList = getSupervisorOrgList(loginEmpCode);
            if (CollectionUtils.isEmpty(orgList)) {
                log.info("校验失败,未获取到当前登录人员机构权限信息:{}", loginEmpCode);
                return false;
            }
            if (orgList.contains(targetSaleTeam.getInstCode())) {
                return true;
            } else {
                log.info("校验失败,督导账号机构权限匹配失败,当前登录人员机构权限信息:{},{},目标人员机构信息:{},{}", loginEmpCode, orgList, loginEmpCode, targetSaleTeam.getInstCode());
                return false;
            }
        }else{
            if (ObjectUtils.isEmpty(targetSaleTeam.getEmpincode()) ){
                log.info("校验失败,未获取到当前团队主管信息");
                return false;
            }
            if (targetSaleTeam.getEmpincode().equals(loginEmpCode)){
                return true;
            }else{
                log.info("校验失败,当前登录人员不是该团队主管,无查看权限:{},{}", loginEmpCode, targetSaleTeam.getEmpincode());
                return false;
            }
        }
    }

    private void getTeamCr13(PerformanceVO targetPerformanceVO, List<String> agentCodeList, String performanceMonth) {
        List<TeamPerformanceVO> cr13InfoList = performanceDao.getTeamCr13(agentCodeList,performanceMonth);
        if (CollectionUtils.isEmpty(cr13InfoList)){
            return;
        }
        BigDecimal m13AA = BigDecimal.ZERO;
        BigDecimal m13NA = BigDecimal.ZERO;
        for (TeamPerformanceVO cr : cr13InfoList) {
            String remark = cr.getIds();
            if (!org.apache.commons.lang3.StringUtils.isEmpty(remark)) {
                if (canJsonArray(remark)) {
                    List<ContinueRateInfo> continueRateInfoList = JSONArray.parseArray(remark, ContinueRateInfo.class);
                    if (!CollectionUtils.isEmpty(continueRateInfoList)){
                        List<ContinueRateInfo> r13List = continueRateInfoList.stream().filter(o -> !org.apache.commons.lang3.StringUtils.isEmpty(o.CONTINUE_RATE_CODE) && "R13".equals(o.CONTINUE_RATE_CODE)).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(r13List)){
                            ContinueRateInfo continueRateInfo = r13List.get(0);
                            m13AA = m13AA.add(new BigDecimal(StringUtils.isEmpty(continueRateInfo.NUMERATOR)?"0":continueRateInfo.NUMERATOR).setScale(0,BigDecimal.ROUND_DOWN));
                            m13NA = m13NA.add(new BigDecimal(StringUtils.isEmpty(continueRateInfo.DENOMINATOR)?"0":continueRateInfo.DENOMINATOR).setScale(0,BigDecimal.ROUND_DOWN));
                        }
                    }
                }
            }
        }
        if (m13NA.compareTo(BigDecimal.ZERO)!=0){
            targetPerformanceVO.setCr13(m13AA.divide(m13NA,2,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")).setScale(2,BigDecimal.ROUND_HALF_UP));
        }else{
            targetPerformanceVO.setCr13(BigDecimal.ZERO);
        }
    }

    private List<PerformancePolicyVO> getTeamPerformancePolicyList(TeamPerformanceRequest request) {
        String type = request.getType();
        String saleTeamCode = request.getSaleTeamCode();
        if (getCurrentLoginEmpCode().startsWith("S")){
            Tbsaleteam saleTeam = saleTeamDao.getTeamSaleTeam(saleTeamCode);
            if (ObjectUtils.isEmpty(saleTeam)){
                log.info("督导账号查询组级团队失败,{}",saleTeamCode);
                return new ArrayList<>();
            }
            saleTeamCode = saleTeam.getSaleteamcode();
        }
        String performanceMonth = request.getPerformanceMonth();
        Date startDate = request.getStartDate();
        Date endDate = request.getEndDate();
        String paymentYears = request.getPaymentYears();
        String agentCode = request.getAgentCode();
        String riskCode = request.getRiskCode();
        String rankSeqCode = request.getRankSeqCode();
        if ("team".equals(type) && !StringUtils.isEmpty(performanceMonth)){
            List<PersonalVO> empInfoList = getTeamPerformanceMonthEmpList(saleTeamCode,performanceMonth);
            if (CollectionUtils.isEmpty(empInfoList)){
                return new ArrayList<>();
            }
            List<String> agentCodeList = empInfoList.stream().map(PersonalVO::getAgentCode).distinct().collect(Collectors.toList());
            List<String> wtPolNoList = new ArrayList<>();
            boolean currentMonth = performanceMonth.equals(getCommissionMonth(new Date()));
            if (currentMonth){
                //查询核心犹退数据
                wtPolNoList = getEsCurrentDayWtPolNoList(null, null);
            }
            return performanceDao.getTeamPerformancePolicyList(agentCodeList,performanceMonth,wtPolNoList);
        }else {
            List<PersonalVO> empInfoList = performanceDao.getTeamCurrentPersonQuitList(saleTeamCode);
            List<String> agentCodeList = empInfoList.stream().map(PersonalVO::getAgentCode).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(empInfoList)) {
                return new ArrayList<>();
            }
            boolean currentDay = new SimpleDateFormat("yyyy-MM-dd").format(new Date()).compareTo(new SimpleDateFormat("yyyy-MM-dd").format(endDate)) <= 0;
            List<String> wtPolNoList = new ArrayList<>();
            if (currentDay){
                wtPolNoList = getEsCurrentDayWtPolNoList(null, null);
            }
            if ("personal".equals(type) && !StringUtils.isEmpty(agentCode)){
                return performanceDao.getTeamPersonalPerformancePolicyList(agentCode,startDate,endDate,wtPolNoList,paymentYears);
            }else if ("product".equals(type) && !StringUtils.isEmpty(riskCode)){
                return performanceDao.getTeamProductPerformancePolicyList(agentCodeList,riskCode,startDate,endDate,wtPolNoList,paymentYears);
            }else if ("rankSeqCode".equals(type) && !StringUtils.isEmpty(rankSeqCode)) {
                return performanceDao.getTeamRankSeqPerformancePolicyList(agentCodeList,rankSeqCode,startDate,endDate,wtPolNoList,paymentYears);
            }
        }
        return new ArrayList<>();
    }

    private boolean checkEmpAccess(String empCode, String performanceMonth) {
        if (!StringUtils.isEmpty(empCode)) {
            String loginEmpCode = getCurrentLoginEmpCode();
            if (StringUtils.isEmpty(loginEmpCode)) {
                log.info("校验失败,未获取到当前登录人员信息");
                return false;
            }
            //查询目标人信息
            EmployeeVO targetEmpInfo = saleTeamDao.getEmployeeInfo(empCode);
            if (!StringUtils.isEmpty(performanceMonth)){
                log.info("追溯历史团队,{},{}",empCode,performanceMonth);
                String saleTeamCode = saleTeamDao.getEmpTargetSaleteamCode(empCode,performanceMonth);
                if (!StringUtils.isEmpty(saleTeamCode)){
                    log.info("追溯到历史团队,{},{},{}",empCode,performanceMonth,saleTeamCode);
                    targetEmpInfo.setTeamCode(saleTeamCode);
                }
            }
            if (ObjectUtils.isEmpty(targetEmpInfo)) {
                log.info("校验失败,未获取到目标人员信息:{}", empCode);
                return false;
            }
            if (loginEmpCode.startsWith("S")) {
                if (StringUtils.isEmpty(targetEmpInfo.getOrgCode())) {
                    log.info("校验失败,未获取到目标人员机构信息:{}", empCode);
                    return false;
                }
                //督导账号查询机构权限
                List<String> orgList = getSupervisorOrgList(loginEmpCode);
                if (CollectionUtils.isEmpty(orgList)) {
                    log.info("校验失败,未获取到当前登录人员机构权限信息:{}", loginEmpCode);
                    return false;
                }
                if (orgList.contains(targetEmpInfo.getOrgCode())) {
                    return true;
                } else {
                    log.info("校验失败,督导账号机构权限匹配失败,当前登录人员机构权限信息:{},{},目标人员机构信息:{},{}", loginEmpCode, orgList, empCode, targetEmpInfo.getOrgCode());
                    return false;
                }
            } else {
                //查询团队
                EmployeeVO loginEmpInfo = saleTeamDao.getEmployeeInfo(loginEmpCode);
                if (ObjectUtils.isEmpty(loginEmpInfo)) {
                    log.info("校验失败,未获取到当前登录人员信息:{}", loginEmpCode);
                    return false;
                }
                if (StringUtils.isEmpty(loginEmpInfo.getEmpInCode()) || !loginEmpCode.equals(loginEmpInfo.getTeamLeaderCode())) {
                    log.info("校验失败,当前登录人员非团队主管:{},{}", loginEmpCode, loginEmpInfo.getTeamLeaderInCode());
                    return false;
                }
                //查询目标人团队
                if (!StringUtils.isEmpty(targetEmpInfo.getTeamCode()) && !StringUtils.isEmpty(targetEmpInfo.getTopCode()) && targetEmpInfo.getTeamCode().equals(loginEmpInfo.getTeamCode())) {
                    return true;
                } else {
                    log.info("校验失败,团队权限匹配失败,{},{},{},{}", loginEmpCode, loginEmpInfo.getTeamCode(), empCode, targetEmpInfo.getTeamCode());
                    return false;
                }
            }
        } else {
            return false;
        }
    }
    @Override
    public List<String> getSupervisorOrgList(String loginEmpCode) {
        SupervisorEmployee supervisorEmployee = supervisorEmployeeMapper.getSupervisorEmployeeByCode(loginEmpCode);
        if (ObjectUtils.isEmpty(supervisorEmployee)){
            return new ArrayList<>();
        }
        List<String> orgList = new ArrayList<>();
        orgList.add(supervisorEmployee.getOrgCode());
        try {
            //查询已配置合伙人机构权限
            if (!StringUtils.isEmpty(supervisorEmployee.getOrgCodeList())){
                orgList.addAll(JsonUtil.toList(supervisorEmployee.getOrgCodeList(), List.class, String.class));
            }
            //查询合伙人权限下的合伙人机构
            if (!StringUtils.isEmpty(supervisorEmployee.getTopCodeList())){
                List<String> topCodeList = JsonUtil.toList(supervisorEmployee.getTopCodeList(), List.class, String.class);
                Map<String,Object> map = new HashMap();
                map.put("topCodeList",topCodeList);
                List<String> instList = tbepartnerMapper.selectInstListByCompanyCode(map);
                if (!CollectionUtils.isEmpty(instList)){
                    orgList.addAll(instList);
                }
            }
        }catch (Exception e){
            log.info("督导账号,机构权限列表获取失败,{},{}",loginEmpCode,supervisorEmployee.getOrgCodeList());
        }
        return orgList.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<SimpleNodeVO> getSelfPolicyList() {
        return performanceDao.getSelfPolicyList();
    }

    @Override
    public List<MarkDetailVO> getSupervisorCrList(List<String> instCodeList, String teamCode, String commissionMonth) {
        return performanceDao.getSupervisorCrList(instCodeList,teamCode,commissionMonth);
    }

    @Override
    public List<SupervisorPerformanceDetailNumberVO> getSupervisorIncreaseVOList(Map map) {
        return performanceDao.getSupervisorIncreaseVOList(map);
    }


    public BigDecimal getEmpPerformance(String empCode, String performanceMonth) {
        //查询ES当日犹退保单号
        List<String> esPolicyNoList = new ArrayList<>();
        boolean isCurrentMonth = performanceMonth.equals(new SimpleDateFormat("yyyy-MM").format(new Date()));
        if (isCurrentMonth){
            esPolicyNoList = getEsCurrentDayWtPolNoList(empCode, performanceMonth);
        }
        //查询当月FYC
        return performanceDao.getEmpPerformanceFycAmount(empCode,performanceMonth, esPolicyNoList);
    }

    private List<String> getEsCurrentDayWtPolNoList(String empCode, String performanceMonth) {
        String format = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        log.info("查询ES当日犹豫退保单,{}",getCurrentLoginEmpCode());
        List<SecurityResponse> wtPolicyList = new ArrayList<>();
        try {
            wtPolicyList = policyQueryApi.querySecurity(format, format, "WT");
        }catch (Exception e){
            log.info("查询当日犹豫退保单错误");
        }
        if (CollectionUtils.isEmpty(wtPolicyList)){
            return new ArrayList<>();
        }
        if (wtPolicyList.size()<=100){
            log.info("查询ES当日犹豫退保单,{},{}",getCurrentLoginEmpCode(), JSON.toJSON(wtPolicyList));
        }else{
            log.info("查询ES当日犹豫退保单,{},{}",getCurrentLoginEmpCode(), wtPolicyList.size());
        }
        return wtPolicyList.stream().map(SecurityResponse::getPolNo).filter(polNo -> !StringUtils.isEmpty(polNo)).distinct().collect(Collectors.toList());
    }

    public String calculateWorkDuration(Date entryDate) {
        LocalDate entryLocalDate = entryDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate today = LocalDate.now();
        long months = ChronoUnit.MONTHS.between(entryLocalDate.withDayOfMonth(1), today.withDayOfMonth(1));
        if (entryLocalDate.getDayOfMonth() <= 15) {
            months += 1;
        }
        long years = months / 12;
        long remainingMonths = months % 12;
        StringBuilder duration = new StringBuilder();
        if (years > 0) {
            duration.append(years).append("年");
        }
        if (remainingMonths > 0) {
            duration.append(remainingMonths).append("个月");
        }
        if (duration.length() == 0) {
            duration.append("0个月");
        }
        return duration.toString();
    }
}

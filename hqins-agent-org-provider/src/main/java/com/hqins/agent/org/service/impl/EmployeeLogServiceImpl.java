package com.hqins.agent.org.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.dao.entity.org.SupervisorEmployee;
import com.hqins.agent.org.dao.mapper.exms.TbempMapper;
import com.hqins.agent.org.dao.mapper.exms.TbsaleteamMapper;
import com.hqins.agent.org.dao.mapper.org.SupervisorEmployeeMapper;
import com.hqins.agent.org.model.enums.SupervisorType;
import com.hqins.agent.org.model.vo.EmployeeLogHierarchyLevelVO;
import com.hqins.agent.org.model.vo.EmployeeLogResultVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.service.EmployeeLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> MXH
 * @create 2025/6/3 15:26
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class EmployeeLogServiceImpl implements EmployeeLogService {

    private final CacheService cacheService;

    private final SupervisorEmployeeMapper supervisorEmployeeMapper;

    private final TbsaleteamMapper tbsaleteamMapper;

    private final TbempMapper tbempMapper;

    @Override
    public EmployeeLogResultVO employeeLogQuery(String employeeCode) {

        if(StrUtil.isEmpty(employeeCode)){
            throw new RuntimeException("传入的代理人工号不能为空");
        }

        //从缓存中获取所有人员
        Map<String, Tbemp> allTbempMap = cacheService.getAllTbempMap();
        if(CollUtil.isEmpty(allTbempMap)){
            throw new RuntimeException("从缓存中获取人员信息失败");
        }

        //从缓存中获取所有人员
        Map<String, Tbsaleteam> allTbsaleteamsMap = cacheService.getAllTbsaleteamsMap();
        if(CollUtil.isEmpty(allTbsaleteamsMap)){
            throw new RuntimeException("从缓存中获取团队信息失败");
        }

        //判断传入的请求参数是 代理人/督导账号
        if(employeeCode.startsWith("S")){
            Map<String, BaseInst> allBaseInstsMap = cacheService.getAllBaseInstsMap();
            if(CollUtil.isEmpty(allBaseInstsMap)){
                throw new RuntimeException("从缓存中获取机构信息失败");
            }
            //督导账号(即为机构内勤) 判断是否为总部/机构督导
            SupervisorEmployee supervisorEmployee = supervisorEmployeeMapper.getSupervisorEmployeeByCode(employeeCode);
            if(supervisorEmployee == null){
                throw new RuntimeException("未获取到相关机构内勤人员的信息");
            }

            if(SupervisorType.ZongBu.name().equals(supervisorEmployee.getRoleType()) || SupervisorType.JiGou.name().equals(supervisorEmployee.getRoleType())){
                String orgCodeList = supervisorEmployee.getOrgCodeList();

                //存放机构层级
                List<EmployeeLogHierarchyLevelVO> employeeLogHierarchyLevelVOList = new ArrayList<>();

                if(StrUtil.isNotBlank(orgCodeList) && !"[]".equals(orgCodeList)){
                    List<String> strings = convertToList(orgCodeList);
                    if(CollUtil.isNotEmpty(strings)){
                        List<Tbsaleteam> saleTeams = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                                .eq(Tbsaleteam::getSaleteamstatus, "00")
                                .eq(Tbsaleteam::getCompanycode, "P00001")
                                .in(Tbsaleteam::getInstCode, strings));
                        //根据instCode进行分组
                        Map<String, List<Tbsaleteam>> saleTeamMap = saleTeams.stream().collect(Collectors.groupingBy(Tbsaleteam::getInstCode));

                        strings.forEach(instCode -> {
                            BaseInst baseInst = allBaseInstsMap.get(instCode);

                            EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                                    .code(baseInst.getInstCode())
                                    .name(baseInst.getInstName())
                                    .level("机构")
                                    .build();
                            List<EmployeeLogHierarchyLevelVO> subLevels = new ArrayList<>();

                            List<Tbsaleteam> groupSaleTeams = saleTeamMap.get(instCode);

                            if(CollUtil.isNotEmpty(groupSaleTeams)){
                                groupSaleTeams.forEach(saleTeam -> {
                                    log.info("督导账号为机构督导,下级团队信息为:{}", JSONObject.toJSONString(saleTeam));
                                    List<Tbsaleteam> highestLevelTeams = groupSaleTeams.stream()
                                            .filter(team -> StrUtil.isBlank(team.getSupersaleteamcode()) || team.getSupersaleteamcode().equals(team.getSaleteamcode()) || "03".equals(team.getTeamlevel()))
                                            .collect(Collectors.toList());
                                    if(CollUtil.isNotEmpty(highestLevelTeams)){
                                        highestLevelTeams.forEach(team -> {
                                            if("01".equals(team.getTeamlevel())){
                                                EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO1 = EmployeeLogHierarchyLevelVO.builder()
                                                        .code(team.getSaleteamcode())
                                                        .name(team.getSaleteamname())
                                                        .level("团队")
                                                        .build();
                                                //查询该团队人员
                                                List<EmployeeVO> employeeVOS = new ArrayList<>();
                                                extracted(team, employeeVOS);
                                                if(CollUtil.isNotEmpty(employeeVOS)){
                                                    employeeLogHierarchyLevelVO1.setEmployeeList(employeeVOS);
                                                    subLevels.add(employeeLogHierarchyLevelVO1);
                                                }
                                            }
                                            if("02".equals(team.getTeamlevel())){
                                                EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO1 = EmployeeLogHierarchyLevelVO.builder()
                                                        .code(team.getSaleteamcode())
                                                        .name(team.getSaleteamname())
                                                        .level("团队")
                                                        .build();

                                                List<Tbsaleteam> childrenSaleTeams = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                                                        .eq(Tbsaleteam::getSaleteamstatus, "00")
                                                        .eq(Tbsaleteam::getSupersaleteamcode, team.getSaleteamcode()).orderByAsc(Tbsaleteam::getSaleteamincode));
                                                List<EmployeeLogHierarchyLevelVO> childrenSubLevels = new ArrayList<>();
                                                if(CollUtil.isNotEmpty(childrenSaleTeams)){
                                                    childrenSaleTeams.forEach(children -> {
                                                        EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO2 = EmployeeLogHierarchyLevelVO.builder()
                                                                .code(children.getSaleteamcode())
                                                                .name(children.getSaleteamname())
                                                                .level("团队")
                                                                .build();
                                                        List<EmployeeVO> employeeVOS = new ArrayList<>();
                                                        extracted(children, employeeVOS);
                                                        if(CollUtil.isNotEmpty(employeeVOS)){
                                                            employeeLogHierarchyLevelVO2.setEmployeeList(employeeVOS);
                                                            childrenSubLevels.add(employeeLogHierarchyLevelVO2);
                                                        }
                                                    });
                                                    if(CollUtil.isNotEmpty(childrenSubLevels)){
                                                        employeeLogHierarchyLevelVO1.setSubLevels(childrenSubLevels);
                                                    }
                                                }
                                                subLevels.add(employeeLogHierarchyLevelVO1);
                                            }
                                            if("03".equals(team.getTeamlevel())){
                                                EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO1 = EmployeeLogHierarchyLevelVO.builder()
                                                        .code(team.getSaleteamcode())
                                                        .name(team.getSaleteamname())
                                                        .level("团队")
                                                        .build();

                                                List<Tbsaleteam> childrenSaleTeams = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                                                        .eq(Tbsaleteam::getSaleteamstatus, "00")
                                                        .eq(Tbsaleteam::getSupersaleteamcode, team.getSaleteamcode()).orderByAsc(Tbsaleteam::getSaleteamincode));
                                                if(CollUtil.isNotEmpty(childrenSaleTeams)){
                                                    List<EmployeeLogHierarchyLevelVO> childrenSubLevels = new ArrayList<>();
                                                    childrenSaleTeams.forEach(children -> {
                                                        EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO2 = EmployeeLogHierarchyLevelVO.builder()
                                                                .code(children.getSaleteamcode())
                                                                .name(children.getSaleteamname())
                                                                .level("团队")
                                                                .build();

                                                        List<Tbsaleteam> childrenSubSaleTeams = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                                                                .eq(Tbsaleteam::getSaleteamstatus, "00")
                                                                .eq(Tbsaleteam::getSupersaleteamcode, team.getSaleteamcode()).orderByAsc(Tbsaleteam::getSaleteamincode));

                                                        List<EmployeeLogHierarchyLevelVO> tmpChildrenSubLevels = new ArrayList<>();
                                                        if(CollUtil.isNotEmpty(childrenSubSaleTeams)){
                                                            childrenSubSaleTeams.forEach(subChildren -> {
                                                                EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO3 = EmployeeLogHierarchyLevelVO.builder()
                                                                        .code(subChildren.getSaleteamcode())
                                                                        .name(subChildren.getSaleteamname())
                                                                        .level("团队")
                                                                        .build();
                                                                List<EmployeeVO> employeeVOS = new ArrayList<>();
                                                                extracted(subChildren, employeeVOS);
                                                                if (CollUtil.isNotEmpty(employeeVOS)) {
                                                                    employeeLogHierarchyLevelVO3.setEmployeeList(employeeVOS);
                                                                    tmpChildrenSubLevels.add(employeeLogHierarchyLevelVO3);
                                                                }
                                                            });
                                                            if(CollUtil.isNotEmpty(tmpChildrenSubLevels)){
                                                                employeeLogHierarchyLevelVO2.setSubLevels(tmpChildrenSubLevels);
                                                            }
                                                        }
                                                    });
                                                    if(CollUtil.isNotEmpty(childrenSubLevels)){
                                                        employeeLogHierarchyLevelVO1.setSubLevels(childrenSubLevels);
                                                    }
                                                }
                                                subLevels.add(employeeLogHierarchyLevelVO1);
                                            }
                                        });
                                        employeeLogHierarchyLevelVOList.add(employeeLogHierarchyLevelVO);
                                    }
                                });
                            }
                        });
                    }
                }
            }
        }else {
            if(!allTbempMap.containsKey(employeeCode)){
                throw new RuntimeException("传入的代理人工号在系统中不存在");
            }
            Tbemp tbemp = allTbempMap.get(employeeCode);

            //判断该代理人是否为团队直属领导
            List<Tbsaleteam> manageTeamList = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                    .eq(Tbsaleteam::getSaleteamstatus, "00")
                    .eq(Tbsaleteam::getEmpincode, tbemp.getEmpincode())
                    .eq(Tbsaleteam::getCompanycode, "P00001").orderByDesc(Tbsaleteam::getTeamlevel));

            if(CollUtil.isNotEmpty(manageTeamList)){

                Tbsaleteam team = manageTeamList.get(0);

                List<EmployeeLogHierarchyLevelVO> subLevels = new ArrayList<>();

                if("01".equals(team.getTeamlevel())){
                    EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO1 = EmployeeLogHierarchyLevelVO.builder()
                            .code(team.getSaleteamcode())
                            .name(team.getSaleteamname())
                            .level("团队")
                            .build();
                    //查询该团队人员
                    List<EmployeeVO> employeeVOS = new ArrayList<>();
                    extracted(team, employeeVOS);
                    if(CollUtil.isNotEmpty(employeeVOS)){
                        employeeLogHierarchyLevelVO1.setEmployeeList(employeeVOS);
                        subLevels.add(employeeLogHierarchyLevelVO1);
                    }
                }

                if("02".equals(team.getTeamlevel())){
                    EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO1 = EmployeeLogHierarchyLevelVO.builder()
                            .code(team.getSaleteamcode())
                            .name(team.getSaleteamname())
                            .level("团队")
                            .build();

                    List<Tbsaleteam> childrenSaleTeams = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                            .eq(Tbsaleteam::getSaleteamstatus, "00")
                            .eq(Tbsaleteam::getSupersaleteamcode, team.getSaleteamcode()).orderByAsc(Tbsaleteam::getSaleteamincode));
                    List<EmployeeLogHierarchyLevelVO> childrenSubLevels = new ArrayList<>();
                    if(CollUtil.isNotEmpty(childrenSaleTeams)){
                        childrenSaleTeams.forEach(children -> {
                            EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO2 = EmployeeLogHierarchyLevelVO.builder()
                                    .code(children.getSaleteamcode())
                                    .name(children.getSaleteamname())
                                    .level("团队")
                                    .build();
                            List<EmployeeVO> employeeVOS = new ArrayList<>();
                            extracted(children, employeeVOS);
                            if(CollUtil.isNotEmpty(employeeVOS)){
                                employeeLogHierarchyLevelVO2.setEmployeeList(employeeVOS);
                                childrenSubLevels.add(employeeLogHierarchyLevelVO2);
                            }
                        });
                        if(CollUtil.isNotEmpty(childrenSubLevels)){
                            employeeLogHierarchyLevelVO1.setSubLevels(childrenSubLevels);
                        }
                    }
                    subLevels.add(employeeLogHierarchyLevelVO1);
                }
                if("03".equals(team.getTeamlevel())){
                    EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO1 = EmployeeLogHierarchyLevelVO.builder()
                            .code(team.getSaleteamcode())
                            .name(team.getSaleteamname())
                            .level("团队")
                            .build();

                    List<Tbsaleteam> childrenSaleTeams = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                            .eq(Tbsaleteam::getSaleteamstatus, "00")
                            .eq(Tbsaleteam::getSupersaleteamcode, team.getSaleteamcode()).orderByAsc(Tbsaleteam::getSaleteamincode));
                    if(CollUtil.isNotEmpty(childrenSaleTeams)){
                        List<EmployeeLogHierarchyLevelVO> childrenSubLevels = new ArrayList<>();
                        childrenSaleTeams.forEach(children -> {
                            EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO2 = EmployeeLogHierarchyLevelVO.builder()
                                    .code(children.getSaleteamcode())
                                    .name(children.getSaleteamname())
                                    .level("团队")
                                    .build();

                            List<Tbsaleteam> childrenSubSaleTeams = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                                    .eq(Tbsaleteam::getSaleteamstatus, "00")
                                    .eq(Tbsaleteam::getSupersaleteamcode, team.getSaleteamcode()).orderByAsc(Tbsaleteam::getSaleteamincode));

                            List<EmployeeLogHierarchyLevelVO> tmpChildrenSubLevels = new ArrayList<>();
                            if(CollUtil.isNotEmpty(childrenSubSaleTeams)){
                                childrenSubSaleTeams.forEach(subChildren -> {
                                    EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO3 = EmployeeLogHierarchyLevelVO.builder()
                                            .code(subChildren.getSaleteamcode())
                                            .name(subChildren.getSaleteamname())
                                            .level("团队")
                                            .build();
                                    List<EmployeeVO> employeeVOS = new ArrayList<>();
                                    extracted(subChildren, employeeVOS);
                                    if (CollUtil.isNotEmpty(employeeVOS)) {
                                        employeeLogHierarchyLevelVO3.setEmployeeList(employeeVOS);
                                        tmpChildrenSubLevels.add(employeeLogHierarchyLevelVO3);
                                    }
                                });
                                if(CollUtil.isNotEmpty(tmpChildrenSubLevels)){
                                    employeeLogHierarchyLevelVO2.setSubLevels(tmpChildrenSubLevels);
                                }
                            }
                        });
                        if(CollUtil.isNotEmpty(childrenSubLevels)){
                            employeeLogHierarchyLevelVO1.setSubLevels(childrenSubLevels);
                        }
                    }
                    subLevels.add(employeeLogHierarchyLevelVO1);
                }

                if(CollUtil.isNotEmpty(subLevels)){
                    return EmployeeLogResultVO.builder()
                            .employeeCode(employeeCode)
                            .employeeRole("代理人")
                            .hierarchyList(subLevels)
                            .build();
                }

            }else {
                log.info("查询自己的工作日志,代理人为:{}", employeeCode);

                List<EmployeeLogHierarchyLevelVO> employeeLogHierarchyLevelVOList = new ArrayList<>();
                EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                        .code(employeeCode)
                        .name(tbemp.getEmpname())
                        .level("团队")
                        .build();
                employeeLogHierarchyLevelVOList.add(employeeLogHierarchyLevelVO);

                return EmployeeLogResultVO.builder()
                        .employeeCode(employeeCode)
                        .employeeRole("代理人")
                        .hierarchyList(employeeLogHierarchyLevelVOList)
                        .build();
            }
        }

        return null;
    }

    @Override
    public EmployeeLogResultVO employeeLogAudit(String employeeCode) {

        if(StrUtil.isEmpty(employeeCode)){
            throw new RuntimeException("传入的代理人工号不能为空");
        }

        //从缓存中获取所有人员
        Map<String, Tbemp> allTbempMap = cacheService.getAllTbempMap();
        if(CollUtil.isEmpty(allTbempMap)){
            throw new RuntimeException("从缓存中获取人员信息失败");
        }

        //判断传入的请求参数是 代理人/督导账号
        if(employeeCode.startsWith("S")){

            Map<String, BaseInst> allBaseInstsMap = cacheService.getAllBaseInstsMap();
            if(CollUtil.isEmpty(allBaseInstsMap)){
                throw new RuntimeException("从缓存中获取机构信息失败");
            }

            //督导账号(即为机构内勤) 判断是否为总部/机构督导
            SupervisorEmployee supervisorEmployee = supervisorEmployeeMapper.getSupervisorEmployeeByCode(employeeCode);
            if(supervisorEmployee == null){
                throw new RuntimeException("未获取到相关机构内勤人员的信息");
            }
            if(SupervisorType.ZongBu.name().equals(supervisorEmployee.getRoleType()) || SupervisorType.JiGou.name().equals(supervisorEmployee.getRoleType())){
                String orgCodeList = supervisorEmployee.getOrgCodeList();

                if(StrUtil.isNotBlank(orgCodeList) && !"[]".equals(orgCodeList)){

                    //存放机构层级
                    List<EmployeeLogHierarchyLevelVO> employeeLogHierarchyLevelVOList = new ArrayList<>();

                    List<String> strings = convertToList(orgCodeList);
                    if(CollUtil.isNotEmpty(strings)){
                        //根据渠道商机构查询对应的团队
                        List<Tbsaleteam> saleTeams = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                                .eq(Tbsaleteam::getSaleteamstatus, "00")
                                .eq(Tbsaleteam::getCompanycode, "P00001")
                                .in(Tbsaleteam::getInstCode, strings));

                        //根据instCode进行分组
                        Map<String, List<Tbsaleteam>> saleTeamMap = saleTeams.stream().collect(Collectors.groupingBy(Tbsaleteam::getInstCode));

                        strings.forEach( instCode -> {
                            log.info("机构内勤的机构编码为:{}", instCode);

                            BaseInst baseInst = allBaseInstsMap.get(instCode);

                            EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                                    .code(baseInst.getInstCode())
                                    .name(baseInst.getInstName())
                                    .level("机构")
                                    .build();

                            List<EmployeeVO> employeeVOS = new ArrayList<>();

                            List<Tbsaleteam> groupSaleTeams = saleTeamMap.get(instCode);

                            if(CollUtil.isNotEmpty(groupSaleTeams)){
                                //查询上级团队为空的或者上级团队编码和本身的团队编码相同或者团队等级为03
                                List<Tbsaleteam> highestLevelTeams = groupSaleTeams.stream()
                                        .filter(team -> StrUtil.isBlank(team.getSupersaleteamcode()) || team.getSupersaleteamcode().equals(team.getSaleteamcode()) || "03".equals(team.getTeamlevel()))
                                        .collect(Collectors.toList());
                                if(CollUtil.isNotEmpty(highestLevelTeams)){
                                    highestLevelTeams.forEach(highestLevelTeam -> {
                                        if("03".equals(highestLevelTeam.getTeamlevel())) {
                                            if(StrUtil.isNotEmpty(highestLevelTeam.getEmpincode())){
                                                EmployeeVO employeeVO = EmployeeVO.builder()
                                                        .code(highestLevelTeam.getEmpincode().substring(4))
                                                        .name(allTbempMap.get(highestLevelTeam.getEmpincode().substring(4)).getEmpname())
                                                        .teamLevel(highestLevelTeam.getTeamlevel())
                                                        .build();
                                                employeeVOS.add(employeeVO);
                                            }else {
                                                log.info("顶层团队为区,机构内勤批阅区总监为空,区团队信息为:{}", JSONObject.toJSONString(highestLevelTeam));
                                                //查询下级部团队
                                                List<Tbsaleteam> saleTeams1 = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                                                        .eq(Tbsaleteam::getSaleteamstatus, "00")
                                                        .eq(Tbsaleteam::getSupersaleteamcode, highestLevelTeam.getSaleteamcode()).orderByAsc(Tbsaleteam::getSaleteamincode));
                                                if(CollUtil.isNotEmpty(saleTeams1)){
                                                    saleTeams1.forEach(children -> {
                                                        if(StrUtil.isNotBlank(children.getEmpincode())){
                                                            EmployeeVO employeeVO = EmployeeVO.builder()
                                                                    .code(children.getEmpincode().substring(4))
                                                                    .name(allTbempMap.get(children.getEmpincode().substring(4)).getEmpname())
                                                                    .teamLevel(children.getTeamlevel())
                                                                    .build();
                                                            employeeVOS.add(employeeVO);
                                                        }else {
                                                            log.info("顶层团队为区,机构内勤批阅部经理为空,部团队信息为:{}", JSONObject.toJSONString(children));
                                                            //查询下级部团队
                                                            List<Tbsaleteam> saleTeams2 = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                                                                    .eq(Tbsaleteam::getSaleteamstatus, "00")
                                                                    .eq(Tbsaleteam::getSupersaleteamcode, children.getSaleteamcode()).orderByAsc(Tbsaleteam::getSaleteamincode));
                                                            if (CollUtil.isNotEmpty(saleTeams2)) {
                                                                saleTeams2.forEach(team -> {
                                                                    if (StrUtil.isNotBlank(team.getEmpincode())) {
                                                                        EmployeeVO employeeVO = EmployeeVO.builder()
                                                                                .code(team.getEmpincode().substring(4))
                                                                                .name(allTbempMap.get(team.getEmpincode().substring(4)).getEmpname())
                                                                                .teamLevel(team.getTeamlevel())
                                                                                .build();
                                                                        employeeVOS.add(employeeVO);
                                                                    } else {
                                                                        extracted(children, employeeVOS);
                                                                    }
                                                                });
                                                            }
                                                        }
                                                    });
                                                }
                                            }
                                        }
                                        if("02".equals(highestLevelTeam.getTeamlevel())){
                                            if(StrUtil.isNotEmpty(highestLevelTeam.getEmpincode())){
                                                EmployeeVO employeeVO = EmployeeVO.builder()
                                                        .code(highestLevelTeam.getEmpincode().substring(4))
                                                        .name(allTbempMap.get(highestLevelTeam.getEmpincode().substring(4)).getEmpname())
                                                        .teamLevel(highestLevelTeam.getTeamlevel())
                                                        .build();
                                                employeeVOS.add(employeeVO);
                                            }else {
                                                log.info("顶层团队为部,机构内勤批阅部经理为空,部团队信息为:{}", JSONObject.toJSONString(highestLevelTeam));
                                                //查询下级团队
                                                List<Tbsaleteam> saleTeams1 = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                                                        .eq(Tbsaleteam::getSaleteamstatus, "00")
                                                        .eq(Tbsaleteam::getSupersaleteamcode, highestLevelTeam.getSaleteamcode()).orderByAsc(Tbsaleteam::getSaleteamincode));
                                                if(CollUtil.isNotEmpty(saleTeams1)){
                                                    saleTeams1.forEach(children -> {
                                                        if(StrUtil.isNotBlank(children.getEmpincode())){
                                                            EmployeeVO employeeVO = EmployeeVO.builder()
                                                                    .code(children.getEmpincode().substring(4))
                                                                    .name(allTbempMap.get(children.getEmpincode().substring(4)).getEmpname())
                                                                    .teamLevel(children.getTeamlevel())
                                                                    .build();
                                                            employeeVOS.add(employeeVO);
                                                        }else {
                                                            extracted(highestLevelTeam, employeeVOS);
                                                        }
                                                    });
                                                }
                                            }
                                        }
                                        if("01".equals(highestLevelTeam.getTeamlevel())){
                                            if(StrUtil.isNotEmpty(highestLevelTeam.getEmpincode())){
                                                EmployeeVO employeeVO = EmployeeVO.builder()
                                                        .code(highestLevelTeam.getEmpincode().substring(4))
                                                        .name(allTbempMap.get(highestLevelTeam.getEmpincode().substring(4)).getEmpname())
                                                        .teamLevel(highestLevelTeam.getTeamlevel())
                                                        .build();
                                                employeeVOS.add(employeeVO);
                                            }else {
                                                log.info("顶层团队为组,机构内勤批阅小组主管为空,小组团队信息为:{}", JSONObject.toJSONString(highestLevelTeam));
                                                //查寻小组人员
                                                extracted(highestLevelTeam, employeeVOS);
                                            }
                                        }
                                    });
                                }
                            }

                            if(CollUtil.isNotEmpty(employeeVOS)){
                                employeeLogHierarchyLevelVO.setEmployeeList(employeeVOS);
                                employeeLogHierarchyLevelVOList.add(employeeLogHierarchyLevelVO);
                            }
                        });
                    }

                    if(CollUtil.isNotEmpty(employeeLogHierarchyLevelVOList)){
                        return EmployeeLogResultVO.builder()
                                .employeeCode(employeeCode)
                                .employeeRole("机构内勤")
                                .hierarchyList(employeeLogHierarchyLevelVOList)
                                .build();
                    }
                }
            }
        }else {
            //获取指定代理人信息
            if(!allTbempMap.containsKey(employeeCode)){
                throw new RuntimeException("该代理人工号在系统中不存在");
            }
            Tbemp tbemp = allTbempMap.get(employeeCode);

            //判断该代理人是否为团队直属领导
            List<Tbsaleteam> manageTeamList = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                    .eq(Tbsaleteam::getSaleteamstatus, "00")
                    .eq(Tbsaleteam::getEmpincode, tbemp.getEmpincode())
                    .eq(Tbsaleteam::getCompanycode, "P00001").orderByDesc(Tbsaleteam::getTeamlevel));

            if(CollUtil.isNotEmpty(manageTeamList)){

                Tbsaleteam highestLevelTeam = manageTeamList.get(0);

                //组装该代理人层级信息
                List<EmployeeLogHierarchyLevelVO> employeeLogHierarchyLevelVOList = new ArrayList<>();

                if(StrUtil.isBlank(highestLevelTeam.getTeamlevel())){
                    log.info("团队等级为空,团队信息为:{}", JSONObject.toJSONString(highestLevelTeam));
                    throw new RuntimeException("团队等级为空");
                }

                if("01".equals(highestLevelTeam.getTeamlevel())){
                    //该代理人为小组主管 获取组内人员信息
                    List<Tbemp> empList = tbempMapper.selectList(new LambdaQueryWrapper<Tbemp>()
                            .eq(Tbemp::getSaleteamincode, highestLevelTeam.getSaleteamincode())
                            .eq(Tbemp::getCompanycode, "P00001")
                            .eq(Tbemp::getEmpstatus, "01")
                            .eq(Tbemp::getIsvirtualemp, "N"));

                    if(!CollUtil.isEmpty(empList)){
                        //组装人员信息数据
                        List<EmployeeVO> employeeVOS = new ArrayList<>();
                        empList.forEach(each -> {
                            EmployeeVO employeeVO = EmployeeVO.builder()
                                    .code(each.getEmpcode())
                                    .name(each.getEmpname())
                                    .teamLevel("01")
                                    .build();
                            employeeVOS.add(employeeVO);
                        });

                        //组装人员工作日志查询结果层级列表
                        EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder().
                                code(highestLevelTeam.getSaleteamcode())
                                .name(highestLevelTeam.getSaleteamname())
                                .level("团队")
                                .employeeList(employeeVOS)
                                .build();
                        employeeLogHierarchyLevelVOList.add(employeeLogHierarchyLevelVO);
                    }
                }

                if("02".equals(highestLevelTeam.getTeamlevel())){
                    //查询下级团队
                    List<Tbsaleteam> saleTeams = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                            .eq(Tbsaleteam::getSaleteamstatus, "00")
                            .eq(Tbsaleteam::getSupersaleteamcode, highestLevelTeam.getSaleteamcode()).orderByAsc(Tbsaleteam::getSaleteamincode));
                    if(!CollUtil.isEmpty(saleTeams)) {
                        saleTeams.forEach(each -> {
                            EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                                    .code(each.getSaleteamcode())
                                    .name(each.getSaleteamname())
                                    .level("团队")
                                    .build();
                            log.info("代理人为部经理,下级团队信息为:{}", JSONObject.toJSONString(each));
                            Tbemp team = allTbempMap.get(each.getEmpincode().substring(4));
                            if(StrUtil.isNotBlank(each.getEmpincode()) && !team.getEmpcode().equals(employeeCode)){
                                List<EmployeeVO> employeeVOS = new ArrayList<>();
                                //返回小组主管信息
                                EmployeeVO employeeVO = EmployeeVO.builder()
                                        .code(team.getEmpcode())
                                        .name(team.getEmpname())
                                        .teamLevel("01")
                                        .build();
                                employeeVOS.add(employeeVO);
                                employeeLogHierarchyLevelVO.setEmployeeList(employeeVOS);
                                employeeLogHierarchyLevelVOList.add(employeeLogHierarchyLevelVO);
                            }else {
                                List<EmployeeVO> employeeVOS = new ArrayList<>();
                                //该代理人为小组主管 获取组内人员信息
                                List<Tbemp> empList = tbempMapper.selectList(new LambdaQueryWrapper<Tbemp>()
                                        .eq(Tbemp::getSaleteamincode, highestLevelTeam.getSaleteamincode())
                                        .eq(Tbemp::getCompanycode, "P00001")
                                        .eq(Tbemp::getEmpstatus, "01")
                                        .eq(Tbemp::getIsvirtualemp, "N"));
                                if(!CollUtil.isEmpty(empList)){
                                    empList.forEach(emp -> {
                                        EmployeeVO employeeVO = EmployeeVO.builder()
                                                .code(emp.getEmpcode())
                                                .name(emp.getEmpname())
                                                .teamLevel("01")
                                                .build();
                                        employeeVOS.add(employeeVO);
                                    });
                                    employeeLogHierarchyLevelVO.setEmployeeList(employeeVOS);
                                    employeeLogHierarchyLevelVOList.add(employeeLogHierarchyLevelVO);
                                }
                            }
                        });
                    }
                }

                if("03".equals(highestLevelTeam.getTeamlevel())){
                    //查询下级部团队
                    List<Tbsaleteam> saleTeams = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                            .eq(Tbsaleteam::getSaleteamstatus, "00")
                            .eq(Tbsaleteam::getSupersaleteamcode, highestLevelTeam.getSaleteamcode()).orderByAsc(Tbsaleteam::getSaleteamincode));
                    if(!CollUtil.isEmpty(saleTeams)) {
                        //部的孩子层级结构信息
                        List<EmployeeLogHierarchyLevelVO> subLevels = new ArrayList<>();
                        saleTeams.forEach(each -> {
                            //存放部层级信息
                            EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO = EmployeeLogHierarchyLevelVO.builder()
                                    .code(highestLevelTeam.getSaleteamcode())
                                    .name(highestLevelTeam.getSaleteamname())
                                    .level("团队")
                                    .build();
                            log.info("代理人为区总监,下级部团队信息为:{}", JSONObject.toJSONString(each));
                            if(StrUtil.isNotBlank(each.getEmpincode()) && !each.getEmpincode().substring(4).equals(employeeCode)){
                                Tbemp partManager = allTbempMap.get(each.getEmpincode().substring(4));
                                List<EmployeeVO> employeeVOS = new ArrayList<>();
                                //返回小组主管信息
                                EmployeeVO employeeVO = EmployeeVO.builder()
                                        .code(partManager.getEmpcode())
                                        .name(partManager.getEmpname())
                                        .teamLevel("02")
                                        .build();
                                employeeVOS.add(employeeVO);
                                employeeLogHierarchyLevelVO.setEmployeeList(employeeVOS);
                                employeeLogHierarchyLevelVOList.add(employeeLogHierarchyLevelVO);
                            }else {
                                //存放组层级信息
                                EmployeeLogHierarchyLevelVO employeeLogHierarchyLevelVO1 = EmployeeLogHierarchyLevelVO.builder()
                                        .code(each.getSaleteamcode())
                                        .name(each.getSaleteamname())
                                        .level("团队")
                                        .build();
                                //查询部下级团队
                                List<Tbsaleteam> childrenSaleTeams = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                                        .eq(Tbsaleteam::getSaleteamstatus, "00")
                                        .eq(Tbsaleteam::getSupersaleteamcode, highestLevelTeam.getSaleteamcode()).orderByAsc(Tbsaleteam::getSaleteamincode));
                                if(CollUtil.isEmpty(childrenSaleTeams)){
                                    childrenSaleTeams.forEach(children -> {
                                        if(StrUtil.isNotBlank(children.getEmpincode()) && !children.getEmpincode().substring(4).equals(employeeCode)){
                                            Tbemp emp = allTbempMap.get(children.getEmpincode().substring(4));
                                            List<EmployeeVO> employeeVOS = new ArrayList<>();
                                            //返回小组主管信息
                                            EmployeeVO employeeVO = EmployeeVO.builder()
                                                    .code(emp.getEmpcode())
                                                    .name(emp.getEmpname())
                                                    .teamLevel("01")
                                                    .build();
                                            employeeVOS.add(employeeVO);
                                            employeeLogHierarchyLevelVO1.setEmployeeList(employeeVOS);
                                            subLevels.add(employeeLogHierarchyLevelVO1);
                                        }else {
                                            //该代理人为小组主管 获取组内人员信息
                                            List<Tbemp> empList = tbempMapper.selectList(new LambdaQueryWrapper<Tbemp>()
                                                    .eq(Tbemp::getSaleteamincode, highestLevelTeam.getSaleteamincode())
                                                    .eq(Tbemp::getCompanycode, "P00001")
                                                    .eq(Tbemp::getEmpstatus, "01")
                                                    .eq(Tbemp::getIsvirtualemp, "N"));
                                            if(CollUtil.isNotEmpty(empList)){
                                                List<EmployeeVO> employeeVOS = new ArrayList<>();
                                                empList.forEach(emp1 -> {
                                                    EmployeeVO employeeVO = EmployeeVO.builder()
                                                            .code(emp1.getEmpcode())
                                                            .name(emp1.getEmpname())
                                                            .teamLevel("01")
                                                            .build();
                                                    employeeVOS.add(employeeVO);
                                                });
                                                employeeLogHierarchyLevelVO1.setEmployeeList(employeeVOS);
                                                subLevels.add(employeeLogHierarchyLevelVO1);
                                            }
                                        }
                                    });
                                    if(CollUtil.isNotEmpty(subLevels)){
                                        employeeLogHierarchyLevelVO.setSubLevels(subLevels);
                                    }
                                }
                            }
                            employeeLogHierarchyLevelVOList.add(employeeLogHierarchyLevelVO);
                        });
                    }
                }

                if(CollUtil.isNotEmpty(employeeLogHierarchyLevelVOList)){
                    return EmployeeLogResultVO.builder()
                            .employeeCode(employeeCode)
                            .employeeRole("代理人")
                            .hierarchyList(employeeLogHierarchyLevelVOList)
                            .build();
                }
            }
        }
        return null;
    }

    /**
     * 查询小组人员
     * @param highestLevelTeam  团队
     * @param employeeVOS       人员集合
     */
    private void extracted(Tbsaleteam highestLevelTeam, List<EmployeeVO> employeeVOS) {
        //查寻小组人员
        List<Tbemp> empList = tbempMapper.selectList(new LambdaQueryWrapper<Tbemp>()
                .eq(Tbemp::getSaleteamincode, highestLevelTeam.getSaleteamincode())
                .eq(Tbemp::getCompanycode, "P00001")
                .eq(Tbemp::getEmpstatus, "01")
                .eq(Tbemp::getIsvirtualemp, "N"));
        if(CollUtil.isNotEmpty(empList)){
            empList.forEach(emp -> {
                EmployeeVO employeeVO = EmployeeVO.builder()
                        .code(emp.getEmpcode())
                        .name(emp.getEmpname())
                        .teamLevel(highestLevelTeam.getTeamlevel())
                        .build();
                employeeVOS.add(employeeVO);
            });
        }
    }

    /**
     * 根据团队等级获取最高等级的团队
     * 团队等级：01-营业组，02-营业部，03-营业区
     * 数字越大，等级越高
     *
     * @param teamList 团队列表
     * @return 最高等级的团队，如果没有有效团队则返回null
     */
    private Tbsaleteam getHighestLevelTeam(List<Tbsaleteam> teamList) {
        if (CollUtil.isEmpty(teamList)) {
            return null;
        }

        return teamList.stream()
                .filter(team -> team.getTeamlevel() != null && !team.getTeamlevel().trim().isEmpty())
                .max((team1, team2) -> team1.getTeamlevel().compareTo(team2.getTeamlevel()))
                .orElse(null);
    }

    /**
     * 将字符串转化为List<String>
     * @param str   字符串
     * @return      集合
     */
    public List<String> convertToList(String str) {
        return Arrays.stream(str.replaceAll("[\\[\\]\"]", "").split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }

}

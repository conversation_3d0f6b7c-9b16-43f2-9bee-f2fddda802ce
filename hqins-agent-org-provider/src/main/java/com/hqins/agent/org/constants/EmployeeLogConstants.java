package com.hqins.agent.org.constants;

/**
 * 员工日志相关常量定义
 *
 * <AUTHOR> MXH
 * @create 2025/1/15
 */
public class EmployeeLogConstants {

    /**
     * 督导账号前缀
     */
    public static final String SUPERVISOR_PREFIX = "S";

    /**
     * 公司编码
     */
    public static final String COMPANY_CODE = "P00001";

    /**
     * 团队状态 - 有效
     */
    public static final String TEAM_STATUS_ACTIVE = "00";

    /**
     * 员工状态 - 有效
     */
    public static final String EMP_STATUS_ACTIVE = "01";

    /**
     * 团队等级 - 营业组
     */
    public static final String TEAM_LEVEL_GROUP = "01";

    /**
     * 团队等级 - 营业部
     */
    public static final String TEAM_LEVEL_DEPARTMENT = "02";

    /**
     * 团队等级 - 营业区
     */
    public static final String TEAM_LEVEL_REGION = "03";

    /**
     * 虚拟员工标志 - 否
     */
    public static final String IS_VIRTUAL_EMP_NO = "N";

    /**
     * 内部员工标志 - 是
     */
    public static final String IS_INSIDE_FLAG_YES = "1";

    /**
     * 空列表字符串
     */
    public static final String EMPTY_LIST_STRING = "[]";

    /**
     * 层级名称定义
     */
    public static class LevelNames {
        public static final String INSTITUTION = "机构";
        public static final String REGION = "区";
        public static final String DEPARTMENT = "部";
        public static final String TEAM = "团队";
    }

    /**
     * 员工角色定义
     */
    public static class EmployeeRoles {
        public static final String SUPERVISOR = "机构内勤";
        public static final String AGENT = "代理人";
    }

    /**
     * 错误消息定义
     */
    public static class ErrorMessages {
        public static final String EMPLOYEE_CODE_EMPTY = "传入的代理人工号不能为空";
        public static final String EMPLOYEE_INFO_LOAD_FAILED = "从缓存中获取人员信息失败";
        public static final String TEAM_INFO_LOAD_FAILED = "从缓存中获取团队信息失败";
        public static final String INSTITUTION_INFO_LOAD_FAILED = "从缓存中获取机构信息失败";
        public static final String SUPERVISOR_NOT_FOUND = "未获取到相关机构内勤人员的信息";
        public static final String EMPLOYEE_NOT_EXISTS = "传入的代理人工号在系统中不存在";
        public static final String TEAM_LEVEL_EMPTY = "团队等级为空";
    }

    /**
     * 团队等级名称映射
     *
     * @param teamLevel 团队等级编码
     * @return 团队等级名称
     */
    public static String getTeamLevelName(String teamLevel) {
        if (teamLevel == null) {
            return "";
        }
        switch (teamLevel) {
            case TEAM_LEVEL_GROUP:
                return "组";
            case TEAM_LEVEL_DEPARTMENT:
                return "部";
            case TEAM_LEVEL_REGION:
                return "区";
            default:
                return teamLevel;
        }
    }
} 
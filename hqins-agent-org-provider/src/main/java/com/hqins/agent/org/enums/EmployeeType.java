package com.hqins.agent.org.enums;

import com.hqins.agent.org.constants.EmployeeLogConstants;

/**
 * 员工类型枚举
 *
 * <AUTHOR> MXH
 * @create 2025/1/15
 */
public enum EmployeeType {
    
    /**
     * 督导（机构内勤）
     */
    SUPERVISOR(EmployeeLogConstants.EmployeeRoles.SUPERVISOR),
    
    /**
     * 代理人
     */
    AGENT(EmployeeLogConstants.EmployeeRoles.AGENT);
    
    private final String roleName;
    
    EmployeeType(String roleName) {
        this.roleName = roleName;
    }
    
    public String getRoleName() {
        return roleName;
    }
    
    /**
     * 根据员工编码判断员工类型
     *
     * @param employeeCode 员工编码
     * @return 员工类型
     */
    public static EmployeeType fromEmployeeCode(String employeeCode) {
        if (employeeCode != null && employeeCode.startsWith(EmployeeLogConstants.SUPERVISOR_PREFIX)) {
            return SUPERVISOR;
        }
        return AGENT;
    }
} 
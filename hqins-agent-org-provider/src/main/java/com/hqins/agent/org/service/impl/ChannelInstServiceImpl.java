package com.hqins.agent.org.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.dao.mapper.iips.BaseInstMapper;
import com.hqins.agent.org.model.enums.ChannelInstStatusEnum;
import com.hqins.agent.org.model.request.ChannelInstOutletsQueryRequest;
import com.hqins.agent.org.model.vo.ChannelInstOutletsVO;
import com.hqins.agent.org.service.ChannelInstService;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.PageUtil;
import com.hqins.common.utils.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 渠道商机构管理实现.
 *
 * <AUTHOR> MXH
 * @create 2025/6/30 9:47
 */
@Service
@RequiredArgsConstructor
public class ChannelInstServiceImpl implements ChannelInstService {

    private final BaseInstMapper baseInstMapper;

    @Override
    public PageInfo<ChannelInstOutletsVO> listInstitutionalOutlets(ChannelInstOutletsQueryRequest queryRequest) {

        LambdaQueryWrapper<BaseInst> queryWrapper = new LambdaQueryWrapper<BaseInst>()
                .eq(BaseInst::getInstLevel, "04")
                .orderByDesc(BaseInst::getInstCode);

        if(StringUtil.isNotBlank(queryRequest.getChannelInstCode())){
            queryWrapper.like(BaseInst::getInstCode, "%" + queryRequest.getChannelInstCode() + "%");
        }
        if(StringUtil.isNotBlank(queryRequest.getChannelInstName())){
            queryWrapper.like(BaseInst::getInstName, "%" + queryRequest.getChannelInstName() + "%");
        }

        Page<BaseInst> baseInstPage = baseInstMapper.selectPage(new Page<>(queryRequest.getCurrent(), queryRequest.getSize()), queryWrapper);

        if (baseInstPage.getTotal() > 0) {
            return PageUtil.convert(baseInstPage, this::convertInstToChannelInstOutletsVO);
        }

        return new PageInfo<>(queryRequest.getCurrent(), queryRequest.getSize());
    }

    private ChannelInstOutletsVO convertInstToChannelInstOutletsVO(BaseInst baseInst) {
        ChannelInstOutletsVO outletsVO = ChannelInstOutletsVO.builder()
                .instCode(baseInst.getInstCode())
                .instName(baseInst.getInstName())
                .instStatus(baseInst.getInstStatus())
                .build();
        outletsVO.setInstStatusName(ChannelInstStatusEnum.getLabelByCode(baseInst.getInstStatus()));
        return outletsVO;
    }

}

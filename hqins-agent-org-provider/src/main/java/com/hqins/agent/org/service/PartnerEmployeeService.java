package com.hqins.agent.org.service;

import com.hqins.agent.org.model.request.EmployeeQueryRequest;
import com.hqins.agent.org.model.request.PartnerEmployeeRequest;
import com.hqins.agent.org.model.vo.*;
import com.hqins.common.base.page.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/7
 * @Description
 */
public interface PartnerEmployeeService {

    PageInfo<PartnerEmployeeVO> listMy(PartnerEmployeeRequest queryRequest);

    PageInfo<AccountVO> listMyAccountVO(PartnerEmployeeRequest queryRequest);

    EmployeeVO getEmployeeVO(String employeeCode);

    EmployeeVO getEmployeeVOByMobile(String mobile);

    MyEmployeeVO getMyEmployeeVO(String employeeCode);

    List<SimpleEmployeeVO> listEmployeeSimple(EmployeeQueryRequest queryRequest);

    String changeMobile(String message);

    /**
     * 合伙人下代理人导出
     *
     * @param queryRequest
     * @return
     */
    String exportData(PartnerEmployeeRequest queryRequest);

    /**
     * 根据代理人工号、状态查询代理人信息
     *
     * @param employeeCode
     * @param status
     * @return
     */
    EmployeeVO getEmployeeVO(String employeeCode, String status);

    /**
     * 根据代理人工号集合查询
     *
     * @param employeeCodeList
     * @return
     */
    List<EmployeeVO> getByEmployeeCodeList(List<String> employeeCodeList);

    /**
     * 根据工号，查询机构区部组组长信息，查询合伙人机构下销售组信息
     * 督导账号需求用，先返回有值的区 ，再是部，再是组
     */
    List<SaleTeamVO> listSaleTeamByOrgCode(String orgCode,Boolean ifpFlag);

    /**
     * 根据代理人工号查询代理人区代码
     * @param empCode
     * @return
     */
    EmployeeVO getEmpAreaCode(String empCode);

    /**
     * 是否是团队长
     */

    EmployeeVO isTeamLeader(EmployeeVO employeeVO);

    List<PartnerEmployeeVO> listMyApi(PartnerEmployeeRequest queryRequest);
}

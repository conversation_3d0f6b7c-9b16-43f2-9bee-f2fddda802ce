package com.hqins.agent.org.exception;

/**
 * 员工日志业务异常
 *
 * <AUTHOR> MXH
 * @create 2025/1/15
 */
public class EmployeeLogException extends RuntimeException {

    public EmployeeLogException(String message) {
        super(message);
    }

    public EmployeeLogException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * 员工未找到异常
 */
class EmployeeNotFoundException extends EmployeeLogException {
    
    public EmployeeNotFoundException(String employeeCode) {
        super("员工未找到: " + employeeCode);
    }
}

/**
 * 数据加载失败异常
 */
class DataLoadException extends EmployeeLogException {
    
    public DataLoadException(String message) {
        super("数据加载失败: " + message);
    }
} 
package com.hqins.agent.org.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hqins.agent.org.constants.EmployeeLogConstants;
import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.dao.entity.org.SupervisorEmployee;
import com.hqins.agent.org.dao.mapper.exms.TbempMapper;
import com.hqins.agent.org.dao.mapper.exms.TbsaleteamMapper;
import com.hqins.agent.org.dao.mapper.iips.BaseInstMapper;
import com.hqins.agent.org.dao.mapper.org.SupervisorEmployeeMapper;
import com.hqins.agent.org.model.vo.EmployeeVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 批量员工日志数据服务
 * 提供高性能的批量数据查询能力
 *
 * <AUTHOR> MXH
 * @create 2025/1/15
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BatchEmployeeLogDataService {

    private final TbempMapper tbempMapper;
    private final TbsaleteamMapper tbsaleteamMapper;
    private final BaseInstMapper baseInstMapper;
    private final SupervisorEmployeeMapper supervisorEmployeeMapper;

    /**
     * 批量查询多个机构的所有团队信息
     * 性能优化：从N次查询优化为1次查询
     *
     * @param institutionCodes 机构编码列表
     * @return 按机构编码分组的团队信息
     */
    public Map<String, List<Tbsaleteam>> getTeamsByInstitutionsBatch(List<String> institutionCodes) {
        if (CollUtil.isEmpty(institutionCodes)) {
            return new HashMap<>();
        }

        log.info("批量查询机构团队信息，机构数量: {}", institutionCodes.size());

        List<Tbsaleteam> allTeams = tbsaleteamMapper.selectList(new LambdaQueryWrapper<Tbsaleteam>()
                .eq(Tbsaleteam::getSaleteamstatus, EmployeeLogConstants.TEAM_STATUS_ACTIVE)
                .eq(Tbsaleteam::getCompanycode, EmployeeLogConstants.COMPANY_CODE)
                .in(Tbsaleteam::getInstCode, institutionCodes)
                .orderByAsc(Tbsaleteam::getTeamlevel)
                .orderByAsc(Tbsaleteam::getSaleteamincode));

        log.info("批量查询到团队数量: {}", allTeams.size());

        return allTeams.stream()
                .collect(Collectors.groupingBy(Tbsaleteam::getInstCode));
    }

    /**
     * 批量查询多个团队的所有员工信息
     * 性能优化：从M次查询优化为1次查询
     *
     * @param teamCodes 团队编码列表
     * @return 按团队编码分组的员工信息
     */
    public Map<String, List<Tbemp>> getEmployeesByTeamsBatch(List<String> teamCodes) {
        if (CollUtil.isEmpty(teamCodes)) {
            return new HashMap<>();
        }

        log.info("批量查询团队员工信息，团队数量: {}", teamCodes.size());

        List<Tbemp> allEmployees = tbempMapper.selectList(new LambdaQueryWrapper<Tbemp>()
                .eq(Tbemp::getCompanycode, EmployeeLogConstants.COMPANY_CODE)
                .eq(Tbemp::getEmpstatus, EmployeeLogConstants.EMP_STATUS_ACTIVE)
                .eq(Tbemp::getIsvirtualemp, EmployeeLogConstants.IS_VIRTUAL_EMP_NO)
                .eq(Tbemp::getIsinsideflag, EmployeeLogConstants.IS_INSIDE_FLAG_YES)
                .in(Tbemp::getSaleteamincode, teamCodes)
                .orderByAsc(Tbemp::getEmpname));

        log.info("批量查询到员工数量: {}", allEmployees.size());

        return allEmployees.stream()
                .collect(Collectors.groupingBy(Tbemp::getSaleteamincode));
    }

    /**
     * 批量查询多个团队的员工信息并转换为VO
     * 支持查询和审核两种模式
     *
     * @param teamCodes           团队编码列表
     * @param currentEmployeeCode 当前员工编码
     * @param includeCurrentEmployee 是否包含当前员工
     * @return 按团队编码分组的员工VO
     */
    public Map<String, List<EmployeeVO>> getEmployeeVOsByTeamsBatch(List<String> teamCodes, 
                                                                   String currentEmployeeCode,
                                                                   boolean includeCurrentEmployee) {
        Map<String, List<Tbemp>> employeesByTeam = getEmployeesByTeamsBatch(teamCodes);

        return employeesByTeam.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> convertToEmployeeVOs(entry.getValue(), currentEmployeeCode, includeCurrentEmployee)
                ));
    }

    /**
     * 批量构建团队层级关系映射
     * 性能优化：使用HashMap提供O(1)查找效率
     *
     * @param allTeams 所有团队列表
     * @return 父团队编码到子团队列表的映射
     */
    public Map<String, List<Tbsaleteam>> buildTeamHierarchyBatch(List<Tbsaleteam> allTeams) {
        if (CollUtil.isEmpty(allTeams)) {
            return new HashMap<>();
        }

        return allTeams.stream()
                .filter(team -> StrUtil.isNotBlank(team.getSupersaleteamcode()) && 
                               !team.getSupersaleteamcode().equals(team.getSaleteamcode()))
                .collect(Collectors.groupingBy(Tbsaleteam::getSupersaleteamcode));
    }

    /**
     * 获取顶级团队列表
     * 顶级团队定义：无上级团队或上级团队编码等于自身编码或团队等级为03
     *
     * @param allTeams 所有团队列表
     * @return 顶级团队列表
     */
    public List<Tbsaleteam> getTopLevelTeams(List<Tbsaleteam> allTeams) {
        if (CollUtil.isEmpty(allTeams)) {
            return new ArrayList<>();
        }

        return allTeams.stream()
                .filter(team -> StrUtil.isBlank(team.getSupersaleteamcode()) || 
                               team.getSupersaleteamcode().equals(team.getSaleteamcode()) ||
                               EmployeeLogConstants.TEAM_LEVEL_REGION.equals(team.getTeamlevel()))
                .collect(Collectors.toList());
    }

    /**
     * 按团队等级分组团队
     *
     * @param teams 团队列表
     * @return 按等级分组的团队映射
     */
    public Map<String, List<Tbsaleteam>> groupTeamsByLevel(List<Tbsaleteam> teams) {
        if (CollUtil.isEmpty(teams)) {
            return new HashMap<>();
        }

        return teams.stream()
                .filter(team -> StrUtil.isNotBlank(team.getTeamlevel()))
                .collect(Collectors.groupingBy(Tbsaleteam::getTeamlevel));
    }

    /**
     * 批量查询督导员工信息
     *
     * @param employeeCodes 员工编码列表
     * @return 督导员工信息映射
     */
    public Map<String, SupervisorEmployee> getSupervisorEmployeesBatch(List<String> employeeCodes) {
        if (CollUtil.isEmpty(employeeCodes)) {
            return new HashMap<>();
        }

        List<SupervisorEmployee> supervisors = supervisorEmployeeMapper.selectList(
                new LambdaQueryWrapper<SupervisorEmployee>()
                        .in(SupervisorEmployee::getEmployeeCode, employeeCodes));

        return supervisors.stream()
                .collect(Collectors.toMap(SupervisorEmployee::getEmployeeCode, supervisor -> supervisor));
    }

    /**
     * 批量查询机构信息
     *
     * @param institutionCodes 机构编码列表
     * @return 机构信息映射
     */
    public Map<String, BaseInst> getInstitutionsBatch(List<String> institutionCodes) {
        if (CollUtil.isEmpty(institutionCodes)) {
            return new HashMap<>();
        }

        List<BaseInst> institutions = baseInstMapper.selectList(
                new LambdaQueryWrapper<BaseInst>()
                        .in(BaseInst::getInstCode, institutionCodes));

        return institutions.stream()
                .collect(Collectors.toMap(BaseInst::getInstCode, institution -> institution));
    }

    /**
     * 转换员工实体为VO
     *
     * @param employees              员工列表
     * @param currentEmployeeCode    当前员工编码
     * @param includeCurrentEmployee 是否包含当前员工
     * @return 员工VO列表
     */
    private List<EmployeeVO> convertToEmployeeVOs(List<Tbemp> employees, 
                                                  String currentEmployeeCode,
                                                  boolean includeCurrentEmployee) {
        if (CollUtil.isEmpty(employees)) {
            return new ArrayList<>();
        }

        return employees.stream()
                .filter(emp -> includeCurrentEmployee || !emp.getEmpcode().equals(currentEmployeeCode))
                .map(emp -> EmployeeVO.builder()
                        .code(emp.getEmpcode())
                        .name(emp.getEmpname())
                        .teamLevel(EmployeeLogConstants.TEAM_LEVEL_GROUP)
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 转换员工实体为VO（用于督导审核场景）
     * 注意：为与原始版本extracted方法保持一致，此方法处理的员工不包含isinsideflag筛选
     *
     * @param employees              员工列表
     * @param currentEmployeeCode    当前员工编码
     * @param includeCurrentEmployee 是否包含当前员工
     * @return 员工VO列表
     */
    public List<EmployeeVO> convertToEmployeeVOsForAudit(List<Tbemp> employees, 
                                                        String currentEmployeeCode,
                                                        boolean includeCurrentEmployee) {
        if (CollUtil.isEmpty(employees)) {
            return new ArrayList<>();
        }

        return employees.stream()
                .filter(emp -> includeCurrentEmployee || !emp.getEmpcode().equals(currentEmployeeCode))
                .map(emp -> EmployeeVO.builder()
                        .code(emp.getEmpcode())
                        .name(emp.getEmpname())
                        .teamLevel(EmployeeLogConstants.TEAM_LEVEL_GROUP)
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * 统计性能指标
     *
     * @param institutionCodes 机构编码列表
     * @return 性能统计信息
     */
    public Map<String, Object> getPerformanceMetrics(List<String> institutionCodes) {
        Map<String, Object> metrics = new HashMap<>();
        
        long startTime = System.currentTimeMillis();
        
        // 统计机构数量
        metrics.put("institutionCount", institutionCodes.size());
        
        // 统计团队数量
        Map<String, List<Tbsaleteam>> teamsByInstitution = getTeamsByInstitutionsBatch(institutionCodes);
        int totalTeams = teamsByInstitution.values().stream()
                .mapToInt(List::size)
                .sum();
        metrics.put("totalTeams", totalTeams);
        
        // 统计员工数量
        List<String> allTeamCodes = teamsByInstitution.values().stream()
                .flatMap(List::stream)
                .map(Tbsaleteam::getSaleteamincode)
                .collect(Collectors.toList());
        
        Map<String, List<Tbemp>> employeesByTeam = getEmployeesByTeamsBatch(allTeamCodes);
        int totalEmployees = employeesByTeam.values().stream()
                .mapToInt(List::size)
                .sum();
        metrics.put("totalEmployees", totalEmployees);
        
        // 统计查询耗时
        long endTime = System.currentTimeMillis();
        metrics.put("queryTimeMs", endTime - startTime);
        
        log.info("性能统计 - 机构数: {}, 团队数: {}, 员工数: {}, 查询耗时: {}ms", 
                institutionCodes.size(), totalTeams, totalEmployees, endTime - startTime);
        
        return metrics;
    }

    /**
     * 批量查询多个团队的员工信息（督导审核专用）
     * 注意：为与原始版本extracted方法保持一致，此方法不包含isinsideflag查询条件
     *
     * @param teamCodes 团队编码列表
     * @return 按团队编码分组的员工信息
     */
    public Map<String, List<Tbemp>> getEmployeesByTeamsBatchForAudit(List<String> teamCodes) {
        if (CollUtil.isEmpty(teamCodes)) {
            return new HashMap<>();
        }

        log.info("批量查询团队员工信息（督导审核专用），团队数量: {}", teamCodes.size());

        List<Tbemp> allEmployees = tbempMapper.selectList(new LambdaQueryWrapper<Tbemp>()
                .eq(Tbemp::getCompanycode, EmployeeLogConstants.COMPANY_CODE)
                .eq(Tbemp::getEmpstatus, EmployeeLogConstants.EMP_STATUS_ACTIVE)
                .eq(Tbemp::getIsvirtualemp, EmployeeLogConstants.IS_VIRTUAL_EMP_NO)
                // 注意：此处不包含.eq(Tbemp::getIsinsideflag, "1")以与原始版本extracted方法保持一致
                .in(Tbemp::getSaleteamincode, teamCodes)
                .orderByAsc(Tbemp::getEmpname));

        log.info("批量查询到员工数量（督导审核专用）: {}", allEmployees.size());

        return allEmployees.stream()
                .collect(Collectors.groupingBy(Tbemp::getSaleteamincode));
    }

    /**
     * 批量查询多个团队的员工信息并转换为VO（督导审核专用）
     * 支持督导审核模式，与原始版本extracted方法保持一致
     *
     * @param teamCodes           团队编码列表
     * @param currentEmployeeCode 当前员工编码
     * @param includeCurrentEmployee 是否包含当前员工
     * @return 按团队编码分组的员工VO
     */
    public Map<String, List<EmployeeVO>> getEmployeeVOsByTeamsBatchForAudit(List<String> teamCodes, 
                                                                           String currentEmployeeCode,
                                                                           boolean includeCurrentEmployee) {
        Map<String, List<Tbemp>> employeesByTeam = getEmployeesByTeamsBatchForAudit(teamCodes);

        return employeesByTeam.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> convertToEmployeeVOsForAudit(entry.getValue(), currentEmployeeCode, includeCurrentEmployee)
                ));
    }
} 
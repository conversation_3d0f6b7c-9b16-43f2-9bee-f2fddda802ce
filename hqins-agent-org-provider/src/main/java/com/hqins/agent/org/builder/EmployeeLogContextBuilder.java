package com.hqins.agent.org.builder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.hqins.agent.org.constants.EmployeeLogConstants;
import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.dao.entity.org.SupervisorEmployee;
import com.hqins.agent.org.dao.mapper.iips.BaseInstMapper;
import com.hqins.agent.org.dao.mapper.org.SupervisorEmployeeMapper;
import com.hqins.agent.org.enums.EmployeeType;
import com.hqins.agent.org.enums.OperationType;
import com.hqins.agent.org.exception.EmployeeLogException;
import com.hqins.agent.org.model.context.EmployeeLogContext;
import com.hqins.agent.org.model.enums.SupervisorType;
import com.hqins.agent.org.service.impl.EmployeeLogDataService;
import lombok.RequiredArgsConstructor;
import org.mockito.internal.util.collections.Sets;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 员工日志上下文构建器
 *
 * <AUTHOR> MXH
 * @create 2025/1/15
 */
@Component
@RequiredArgsConstructor
public class EmployeeLogContextBuilder {

    private final EmployeeLogDataService dataService;
    private final SupervisorEmployeeMapper supervisorEmployeeMapper;
    private final BaseInstMapper baseInstMapper;

    /**
     * 构建执行上下文
     *
     * @param employeeCode  员工编码
     * @param operationType 操作类型
     * @return 执行上下文
     */
    public EmployeeLogContext build(String employeeCode, OperationType operationType) {
        // 基础验证
        validateEmployeeCode(employeeCode);

        // 获取基础数据
        Map<String, Tbemp> allEmployees = loadEmployeesMap();
        Map<String, Tbsaleteam> allTeams = loadTeamsMap();
        Map<String, BaseInst> allInstitutions = loadInstitutionsMap();

        // 确定员工类型
        EmployeeType employeeType = EmployeeType.fromEmployeeCode(employeeCode);

        // 构建上下文
        EmployeeLogContext.EmployeeLogContextBuilder contextBuilder = EmployeeLogContext.builder()
                .employeeCode(employeeCode)
                .employeeType(employeeType)
                .operationType(operationType)
                .allEmployees(allEmployees)
                .allTeams(allTeams)
                .allInstitutions(allInstitutions);

        // 根据员工类型构建特定数据
        if (employeeType == EmployeeType.SUPERVISOR) {
            buildSupervisorContext(contextBuilder, employeeCode, allInstitutions);
        } else {
            buildAgentContext(contextBuilder, employeeCode, allEmployees);
        }

        return contextBuilder.build();
    }

    /**
     * 验证员工编码
     *
     * @param employeeCode 员工编码
     */
    private void validateEmployeeCode(String employeeCode) {
        if (StrUtil.isEmpty(employeeCode)) {
            throw new EmployeeLogException(EmployeeLogConstants.ErrorMessages.EMPLOYEE_CODE_EMPTY);
        }
    }

    /**
     * 加载员工信息映射
     *
     * @return 员工信息映射
     */
    private Map<String, Tbemp> loadEmployeesMap() {
        Map<String, Tbemp> employeesMap = dataService.getAllEmployeesMap();
        if (CollUtil.isEmpty(employeesMap)) {
            throw new EmployeeLogException(EmployeeLogConstants.ErrorMessages.EMPLOYEE_INFO_LOAD_FAILED);
        }
        return employeesMap;
    }

    /**
     * 加载团队信息映射
     *
     * @return 团队信息映射
     */
    private Map<String, Tbsaleteam> loadTeamsMap() {
        Map<String, Tbsaleteam> teamsMap = dataService.getAllTeamsMap();
        if (CollUtil.isEmpty(teamsMap)) {
            throw new EmployeeLogException(EmployeeLogConstants.ErrorMessages.TEAM_INFO_LOAD_FAILED);
        }
        return teamsMap;
    }

    /**
     * 加载机构信息映射
     *
     * @return 机构信息映射
     */
    private Map<String, BaseInst> loadInstitutionsMap() {
        Map<String, BaseInst> institutionsMap = dataService.getAllInstitutionsMap();
        if (CollUtil.isEmpty(institutionsMap)) {
            throw new EmployeeLogException(EmployeeLogConstants.ErrorMessages.INSTITUTION_INFO_LOAD_FAILED);
        }
        return institutionsMap;
    }

    /**
     * 构建督导上下文
     *
     * @param contextBuilder  上下文构建器
     * @param employeeCode    员工编码
     * @param allInstitutions 所有机构信息
     */
    private void buildSupervisorContext(EmployeeLogContext.EmployeeLogContextBuilder contextBuilder,
                                        String employeeCode,
                                        Map<String, BaseInst> allInstitutions) {
        // 查询督导员工信息
        SupervisorEmployee supervisorEmployee = supervisorEmployeeMapper.getSupervisorEmployeeByCode(employeeCode);
        if (supervisorEmployee == null) {
            throw new EmployeeLogException(EmployeeLogConstants.ErrorMessages.SUPERVISOR_NOT_FOUND);
        }

        // 验证督导类型
        if (!isValidSupervisorType(supervisorEmployee.getRoleType())) {
            throw new EmployeeLogException("无效的督导类型: " + supervisorEmployee.getRoleType());
        }

        // 获取机构编码列表
        List<String> institutionCodes = getInstitutionCodes(supervisorEmployee);
        if (CollUtil.isEmpty(institutionCodes)) {
            throw new EmployeeLogException("未找到督导权限范围内的机构");
        }

        // 获取按机构分组的团队信息
        Map<String, List<Tbsaleteam>> teamsByInstitution = dataService.getTeamsByInstitutions(institutionCodes);

        contextBuilder
                .supervisorEmployee(supervisorEmployee)
                .institutionCodes(institutionCodes)
                .teamsByInstitution(teamsByInstitution);
    }

    /**
     * 构建代理人上下文
     *
     * @param contextBuilder 上下文构建器
     * @param employeeCode   员工编码
     * @param allEmployees   所有员工信息
     */
    private void buildAgentContext(EmployeeLogContext.EmployeeLogContextBuilder contextBuilder,
                                   String employeeCode,
                                   Map<String, Tbemp> allEmployees) {
        // 验证员工是否存在
        if (!allEmployees.containsKey(employeeCode)) {
            throw new EmployeeLogException(EmployeeLogConstants.ErrorMessages.EMPLOYEE_NOT_EXISTS);
        }

        Tbemp currentEmployee = allEmployees.get(employeeCode);

        // 查询管理的团队
        List<Tbsaleteam> managedTeams = dataService.getManagedTeams(currentEmployee.getEmpincode());

        contextBuilder
                .currentEmployee(currentEmployee)
                .managedTeams(managedTeams);
    }

    /**
     * 验证督导类型是否有效
     *
     * @param roleType 角色类型
     * @return 是否有效
     */
    private boolean isValidSupervisorType(String roleType) {
        return SupervisorType.ZongBu.name().equals(roleType) ||
               SupervisorType.JiGou.name().equals(roleType);
    }

    /**
     * 获取机构编码列表
     *
     * @param supervisorEmployee 督导员工信息
     * @return 机构编码列表
     */
    private List<String> getInstitutionCodes(SupervisorEmployee supervisorEmployee) {
        if (SupervisorType.ZongBu.name().equals(supervisorEmployee.getRoleType())) {
            return getZongBuInstitutionCodes(supervisorEmployee.getTopCodeList());
        } else {
            return dataService.convertToList(supervisorEmployee.getOrgCodeList());
        }
    }

    /**
     * 获取总部督导的机构编码列表
     *
     * @param topCodeList 顶级编码列表
     * @return 机构编码列表
     */
    private List<String> getZongBuInstitutionCodes(String topCodeList) {
        if (StrUtil.isBlank(topCodeList) || !topCodeList.contains(EmployeeLogConstants.COMPANY_CODE)) {
            return new ArrayList<>();
        }

        List<Map<String, Object>> topCodes = baseInstMapper.listByTopCodes(Sets.newSet(EmployeeLogConstants.COMPANY_CODE));
        if (CollUtil.isEmpty(topCodes)) {
            return new ArrayList<>();
        }

        return topCodes.stream()
                .map(map -> map.get("code").toString())
                .collect(Collectors.toList());
    }
} 
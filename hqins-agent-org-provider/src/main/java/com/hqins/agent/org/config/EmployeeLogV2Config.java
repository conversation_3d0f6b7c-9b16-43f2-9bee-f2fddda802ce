package com.hqins.agent.org.config;

import com.hqins.agent.org.command.EmployeeLogCommand;
import com.hqins.agent.org.strategy.EmployeeHierarchyStrategy;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * 员工日志 V2 配置类
 *
 * <AUTHOR> MXH
 * @create 2025/1/15
 */
@Configuration
@RequiredArgsConstructor
public class EmployeeLogV2Config {

    private final ApplicationContext applicationContext;

    /**
     * 注册命令映射
     *
     * @return 命令映射
     */
    @Bean("employeeLogCommands")
    public Map<String, EmployeeLogCommand> employeeLogCommands() {
        Map<String, EmployeeLogCommand> commands = new HashMap<>();
        
        // 获取所有命令实现
        Map<String, EmployeeLogCommand> commandBeans = applicationContext.getBeansOfType(EmployeeLogCommand.class);
        
        commandBeans.forEach((beanName, command) -> {
            commands.put(beanName, command);
        });
        
        return commands;
    }

    /**
     * 注册策略映射
     *
     * @return 策略映射
     */
    @Bean("employeeHierarchyStrategies")
    public Map<String, EmployeeHierarchyStrategy> employeeHierarchyStrategies() {
        Map<String, EmployeeHierarchyStrategy> strategies = new HashMap<>();
        
        // 获取所有策略实现
        Map<String, EmployeeHierarchyStrategy> strategyBeans = applicationContext.getBeansOfType(EmployeeHierarchyStrategy.class);
        
        strategyBeans.forEach((beanName, strategy) -> {
            strategies.put(beanName, strategy);
        });
        
        return strategies;
    }
} 
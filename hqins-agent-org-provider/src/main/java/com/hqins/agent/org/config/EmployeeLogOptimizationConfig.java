package com.hqins.agent.org.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 员工日志优化配置类
 * 提供异步执行和性能优化相关配置
 *
 * <AUTHOR> MXH
 * @create 2025/1/15
 */
@Configuration
@EnableAsync
@Slf4j
public class EmployeeLogOptimizationConfig {

    @Value("${employee.log.async.core-pool-size:10}")
    private int corePoolSize;

    @Value("${employee.log.async.max-pool-size:50}")
    private int maxPoolSize;

    @Value("${employee.log.async.queue-capacity:100}")
    private int queueCapacity;

    @Value("${employee.log.async.keep-alive-seconds:60}")
    private int keepAliveSeconds;

    @Value("${employee.log.async.thread-name-prefix:EmployeeLog-}")
    private String threadNamePrefix;

    @Value("${employee.log.optimization.enabled:false}")
    private boolean optimizationEnabled;

    @Value("${employee.log.batch.max-institutions:30}")
    private int maxInstitutions;

    @Value("${employee.log.performance.monitoring.enabled:true}")
    private boolean performanceMonitoringEnabled;

    /**
     * 员工日志专用异步执行器
     * 
     * @return 异步执行器
     */
    @Bean("employeeLogExecutor")
    public Executor employeeLogExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 线程池配置
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setThreadNamePrefix(threadNamePrefix);
        
        // 拒绝策略：调用者执行，确保任务不会丢失
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 允许核心线程超时
        executor.setAllowCoreThreadTimeOut(true);
        
        // 优雅关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        
        log.info("初始化员工日志异步执行器 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}", 
                corePoolSize, maxPoolSize, queueCapacity);
        
        return executor;
    }

    /**
     * 是否启用优化版本
     * 
     * @return 是否启用
     */
    public boolean isOptimizationEnabled() {
        return optimizationEnabled;
    }

    /**
     * 获取最大机构处理数量
     * 用于控制批量处理的规模，避免内存溢出
     * 
     * @return 最大机构数量
     */
    public int getMaxInstitutions() {
        return maxInstitutions;
    }

    /**
     * 是否启用性能监控
     * 
     * @return 是否启用
     */
    public boolean isPerformanceMonitoringEnabled() {
        return performanceMonitoringEnabled;
    }

    /**
     * 获取异步执行器配置信息
     * 
     * @return 配置信息
     */
    public String getExecutorConfigInfo() {
        return String.format("异步执行器配置 - 核心线程: %d, 最大线程: %d, 队列容量: %d, 线程前缀: %s",
                corePoolSize, maxPoolSize, queueCapacity, threadNamePrefix);
    }

    /**
     * 获取优化配置摘要
     * 
     * @return 配置摘要
     */
    public String getOptimizationSummary() {
        return String.format("优化配置 - 启用状态: %s, 最大机构数: %d, 性能监控: %s",
                optimizationEnabled ? "启用" : "禁用",
                maxInstitutions,
                performanceMonitoringEnabled ? "启用" : "禁用");
    }
} 
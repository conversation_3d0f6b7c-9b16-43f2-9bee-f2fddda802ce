package com.hqins.agent.org.service.impl;

import com.hqins.agent.org.builder.EmployeeLogContextBuilder;
import com.hqins.agent.org.command.EmployeeLogCommand;
import com.hqins.agent.org.enums.OperationType;
import com.hqins.agent.org.model.context.EmployeeLogContext;
import com.hqins.agent.org.model.vo.EmployeeLogResultVO;
import com.hqins.agent.org.service.EmployeeLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 员工日志服务实现 V2 - 建造者模式 + 命令模式
 *
 * <AUTHOR> MXH
 * @create 2025/1/15
 */
@Service("employeeLogServiceV2")
@Slf4j
public class EmployeeLogServiceV2Impl implements EmployeeLogService {

    private final EmployeeLogContextBuilder contextBuilder;
    private final Map<String, EmployeeLogCommand> commands;

    public EmployeeLogServiceV2Impl(EmployeeLogContextBuilder contextBuilder,
                                   @Qualifier("employeeLogCommands") Map<String, EmployeeLogCommand> commands) {
        this.contextBuilder = contextBuilder;
        this.commands = commands;
    }

    @Override
    public EmployeeLogResultVO employeeLogQuery(String employeeCode) {
        log.info("员工日志查询开始, employeeCode: {}", employeeCode);
        
        try {
            // 构建执行上下文
            EmployeeLogContext context = contextBuilder.build(employeeCode, OperationType.QUERY);
            
            // 获取查询命令并执行
            EmployeeLogCommand queryCommand = commands.get("queryCommand");
            if (queryCommand == null) {
                throw new IllegalStateException("未找到queryCommand命令实现");
            }
            EmployeeLogResultVO result = queryCommand.execute(context);
            
            log.info("员工日志查询完成, employeeCode: {}, resultSize: {}", 
                    employeeCode, result != null ? result.getHierarchyList().size() : 0);
            
            return result;
        } catch (Exception e) {
            log.error("员工日志查询失败, employeeCode: {}, error: {}", employeeCode, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public EmployeeLogResultVO employeeLogAudit(String employeeCode) {
        log.info("员工日志审核查询开始, employeeCode: {}", employeeCode);
        
        try {
            // 构建执行上下文
            EmployeeLogContext context = contextBuilder.build(employeeCode, OperationType.AUDIT);
            
            // 获取审核命令并执行
            EmployeeLogCommand auditCommand = commands.get("auditCommand");
            if (auditCommand == null) {
                throw new IllegalStateException("未找到auditCommand命令实现");
            }
            EmployeeLogResultVO result = auditCommand.execute(context);
            
            log.info("员工日志审核查询完成, employeeCode: {}, resultSize: {}", 
                    employeeCode, result != null ? result.getHierarchyList().size() : 0);
            
            return result;
        } catch (Exception e) {
            log.error("员工日志审核查询失败, employeeCode: {}, error: {}", employeeCode, e.getMessage(), e);
            throw e;
        }
    }
} 
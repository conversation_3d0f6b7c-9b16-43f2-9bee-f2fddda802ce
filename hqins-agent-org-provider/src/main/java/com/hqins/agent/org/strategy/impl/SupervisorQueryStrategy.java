package com.hqins.agent.org.strategy.impl;

import cn.hutool.core.collection.CollUtil;
import com.hqins.agent.org.builder.EmployeeLogHierarchyBuilder;
import com.hqins.agent.org.constants.EmployeeLogConstants;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.model.context.EmployeeLogContext;
import com.hqins.agent.org.model.vo.EmployeeLogHierarchyLevelVO;
import com.hqins.agent.org.model.vo.EmployeeLogResultVO;
import com.hqins.agent.org.model.vo.EmployeeVO;
import com.hqins.agent.org.service.impl.EmployeeLogDataService;
import com.hqins.agent.org.strategy.EmployeeHierarchyStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 督导查询策略实现
 *
 * <AUTHOR> MXH
 * @create 2025/1/15
 */
@Component("SUPERVISOR_QUERY_STRATEGY")
@Slf4j
@RequiredArgsConstructor
public class SupervisorQueryStrategy implements EmployeeHierarchyStrategy {

    private final EmployeeLogDataService dataService;

    @Override
    public EmployeeLogResultVO buildHierarchy(EmployeeLogContext context, EmployeeLogHierarchyBuilder builder) {
        log.info("开始构建督导查询层级结构, employeeCode: {}", context.getEmployeeCode());

        List<String> institutionCodes = context.getInstitutionCodes();
        Map<String, BaseInst> allInstitutions = context.getAllInstitutions();
        Map<String, List<Tbsaleteam>> teamsByInstitution = context.getTeamsByInstitution();

        // 为每个机构构建层级结构
        for (String institutionCode : institutionCodes) {
            BaseInst institution = allInstitutions.get(institutionCode);
            if (institution == null) {
                log.warn("机构信息不存在: {}", institutionCode);
                continue;
            }

            // 构建区级子层级
            List<EmployeeLogHierarchyLevelVO> regionLevels = buildRegionLevels(
                    teamsByInstitution.get(institutionCode), context);

            // 修复：只有当存在有效的区级层级时才创建并添加机构层级
            if (CollUtil.isNotEmpty(regionLevels)) {
                // 创建机构层级
                EmployeeLogHierarchyLevelVO institutionLevel = EmployeeLogHierarchyLevelVO.builder()
                        .code(institution.getInstCode())
                        .name(institution.getInstName())
                        .level(EmployeeLogConstants.LevelNames.INSTITUTION)
                        .subLevels(regionLevels)
                        .build();

                builder.addLevel(institutionLevel);
            } else {
                log.debug("机构无有效数据，跳过: {}", institutionCode);
            }
        }

        return builder.build();
    }

    /**
     * 构建区级层级
     *
     * @param teams   团队列表
     * @param context 执行上下文
     * @return 区级层级列表
     */
    private List<EmployeeLogHierarchyLevelVO> buildRegionLevels(List<Tbsaleteam> teams, EmployeeLogContext context) {
        if (CollUtil.isEmpty(teams)) {
            return new ArrayList<>();
        }

        List<EmployeeLogHierarchyLevelVO> regionLevels = new ArrayList<>();

        // 筛选区级团队
        List<Tbsaleteam> regionTeams = dataService.filterTeamsByLevel(teams, EmployeeLogConstants.TEAM_LEVEL_DEPARTMENT);

        for (Tbsaleteam regionTeam : regionTeams) {
            EmployeeLogHierarchyLevelVO regionLevel = EmployeeLogHierarchyLevelVO.builder()
                    .code(regionTeam.getSaleteamcode())
                    .name(regionTeam.getSaleteamname())
                    .level(EmployeeLogConstants.LevelNames.REGION)
                    .build();

            // 构建部级子层级
            List<EmployeeLogHierarchyLevelVO> departmentLevels = buildDepartmentLevels(regionTeam, context);

            if (CollUtil.isNotEmpty(departmentLevels)) {
                regionLevel.setSubLevels(departmentLevels);
                regionLevels.add(regionLevel);
            }
        }

        return regionLevels;
    }

    /**
     * 构建部级层级
     *
     * @param regionTeam 区级团队
     * @param context    执行上下文
     * @return 部级层级列表
     */
    private List<EmployeeLogHierarchyLevelVO> buildDepartmentLevels(Tbsaleteam regionTeam, EmployeeLogContext context) {
        List<EmployeeLogHierarchyLevelVO> departmentLevels = new ArrayList<>();

        // 查询区下的部级团队
        List<Tbsaleteam> departmentTeams = dataService.getSubTeams(regionTeam.getSaleteamcode());

        for (Tbsaleteam departmentTeam : departmentTeams) {
            EmployeeLogHierarchyLevelVO departmentLevel = EmployeeLogHierarchyLevelVO.builder()
                    .code(departmentTeam.getSaleteamcode())
                    .name(departmentTeam.getSaleteamname())
                    .level(EmployeeLogConstants.LevelNames.DEPARTMENT)
                    .build();

            // 获取部级团队的员工（查阅模式：包含当前员工）
            List<EmployeeVO> employees = dataService.getTeamMembers(
                    departmentTeam.getSaleteamincode(), context.getEmployeeCode(), true);

            if (CollUtil.isNotEmpty(employees)) {
                departmentLevel.setEmployeeList(employees);
                departmentLevels.add(departmentLevel);
            }
        }

        return departmentLevels;
    }
} 
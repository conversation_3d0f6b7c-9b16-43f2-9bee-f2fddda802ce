package com.hqins.agent.org.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.configuration.SupervisorPerformanceBasicLawStrategyFactory;
import com.hqins.agent.org.constants.AppConsts;
import com.hqins.agent.org.dao.IFP2024AssessmentDao;
import com.hqins.agent.org.dao.converter.PartnerConverter;
import com.hqins.agent.org.dao.entity.exms.Tbsaleteam;
import com.hqins.agent.org.dao.entity.iips.BaseInst;
import com.hqins.agent.org.dao.entity.org.SupervisorEmployee;
import com.hqins.agent.org.dao.mapper.org.SupervisorEmployeeMapper;
import com.hqins.agent.org.excel.*;
import com.hqins.agent.org.model.enums.BasicLawType;
import com.hqins.agent.org.model.enums.SupervisorType;
import com.hqins.agent.org.model.request.OrgQueryRequest;
import com.hqins.agent.org.model.request.SupervisorPerformanceRequest;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.service.*;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.JsonUtil;
import com.hqins.common.web.RequestContextHolder;
import com.hqins.file.service.api.FileApi;
import com.hqins.file.service.model.request.FileBase64Request;
import com.hqins.file.service.model.vo.FileGetUrlsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class SupervisorPerformanceServiceImpl implements SupervisorPerformanceService {


    @Autowired
    private SupervisorEmployeeMapper supervisorEmployeeMapper;
    @Autowired
    private SupervisorEmployeeService supervisorEmployeeService;
    @Autowired
    private PerformanceService performanceService;
    @Autowired
    private DaDetailService daDetailService;
    @Resource
    private PartnerOrgService partnerOrgService;
    @Resource
    private CacheService cacheService;

    private final PerformanceInstExcelAssembler performanceInstExcelAssembler;

    private final SupervisorMarkSelfPolicyExcelAssembler supervisorMarkSelfPolicyExcelAssembler;

    private final FileApi fileApi;

    private final IFP2024AssessmentDao assessmentDao;

    private final SupervisorPerformanceBasicLawStrategyFactory factory;

    private final List<String> selfCompanyCodeList = Arrays.asList("P00003","P00004");

    @Override
    public String getSelfPolicyExport(SupervisorPerformanceRequest request) {
        SupervisorMarkVO vo = getSelfPolicy(request);
        if (ObjectUtils.isEmpty(vo) || CollectionUtils.isEmpty(vo.getSelfPolicyList())){
            return "";
        }
        List<MarkDetailVO> selfPolicyList = vo.getSelfPolicyList();

        List<SupervisorMarkSelfPolicyExcel> resultList = new ArrayList<>();
        resultList =  selfPolicyList.stream()
                .map(supervisorMarkSelfPolicyExcelAssembler::assemblerSelfPolicy)
                .collect(Collectors.toList());


        byte[] bytes = null;
        //查询数据 生成报表
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
                EasyExcel.write(bos, SupervisorMarkSelfPolicyExcel.class)
                        .registerWriteHandler(new CellWriteWidthConfig())
                        .sheet("指标预警自保件互保件明细")
                        .doWrite(resultList);
            bytes = bos.toByteArray();
        } catch (IOException e) {
            log.error("export.error:{}", e.getMessage(), e);
        }
        // 上传阿里云
        FileGetUrlsVO fileGetUrlsVO = fileApi.upload4Base64(FileBase64Request
                .builder()
                .fileName("指标预警自保件互保件明细清单"+ LocalDateTime.now()+ ".xlsx")
                .source("admin")
                .fileBase64String(Base64.encode(bytes))
                .build());
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(fileGetUrlsVO.getFileUrlList())) {
            return fileGetUrlsVO.getFileUrlList().get(0).getOuterUrl();
        }
        return "";
    }

    @Override
    public String getDisCompleteCr13Export(SupervisorPerformanceRequest request) {
        SupervisorMarkVO vo = getDisCompleteCr13(request);
        if (ObjectUtils.isEmpty(vo) || CollectionUtils.isEmpty(vo.getCr13List())){
            return "";
        }
        List<MarkDetailVO> selfPolicyList = vo.getCr13List();

        List<SupervisorMarkCr13PolicyExcel> resultList = new ArrayList<>();
        resultList =  selfPolicyList.stream()
                .map(supervisorMarkSelfPolicyExcelAssembler::assemblerCr13Policy)
                .collect(Collectors.toList());


        byte[] bytes = null;
        //查询数据 生成报表
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            EasyExcel.write(bos, SupervisorMarkCr13PolicyExcel.class)
                    .registerWriteHandler(new CellWriteWidthConfig())
                    .sheet("指标预警13个月继续率明细")
                    .doWrite(resultList);
            bytes = bos.toByteArray();
        } catch (IOException e) {
            log.error("export.error:{}", e.getMessage(), e);
        }
        // 上传阿里云
        FileGetUrlsVO fileGetUrlsVO = fileApi.upload4Base64(FileBase64Request
                .builder()
                .fileName("指标预警13个月继续率明细清单"+ LocalDateTime.now()+ ".xlsx")
                .source("admin")
                .fileBase64String(Base64.encode(bytes))
                .build());
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(fileGetUrlsVO.getFileUrlList())) {
            return fileGetUrlsVO.getFileUrlList().get(0).getOuterUrl();
        }
        return "";
    }

    @Override
    public String getCtPolicyExport(SupervisorPerformanceRequest request) {
        SupervisorMarkVO vo = getCtPolicy(request);
        if (ObjectUtils.isEmpty(vo) || CollectionUtils.isEmpty(vo.getCtPolicyList())){
            return "";
        }
        List<MarkDetailVO> selfPolicyList = vo.getCtPolicyList();

        List<SupervisorMarkCtPolicyExcel> resultList = new ArrayList<>();
        resultList =  selfPolicyList.stream()
                .map(supervisorMarkSelfPolicyExcelAssembler::assemblerCtPolicy)
                .collect(Collectors.toList());


        byte[] bytes = null;
        //查询数据 生成报表
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            EasyExcel.write(bos, SupervisorMarkCtPolicyExcel.class)
                    .registerWriteHandler(new CellWriteWidthConfig())
                    .sheet("指标预警犹豫期退保明细")
                    .doWrite(resultList);
            bytes = bos.toByteArray();
        } catch (IOException e) {
            log.error("export.error:{}", e.getMessage(), e);
        }
        // 上传阿里云
        FileGetUrlsVO fileGetUrlsVO = fileApi.upload4Base64(FileBase64Request
                .builder()
                .fileName("指标预警犹豫期退保明细清单"+ LocalDateTime.now()+ ".xlsx")
                .source("admin")
                .fileBase64String(Base64.encode(bytes))
                .build());
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(fileGetUrlsVO.getFileUrlList())) {
            return fileGetUrlsVO.getFileUrlList().get(0).getOuterUrl();
        }
        return "";
    }

    @Override
    public String getCheckInoListExport(SupervisorPerformanceRequest request) {
        log.info("supervisorPerformance getCheckInoListExport request:{}",request);
        //校验请求参数
        if(!CollectionUtils.isEmpty(request.getInstCodeList())){
            if(request.getInstCodeList().size() > 1){
                if(!StrUtil.isEmpty(request.getTeamCode())){
                    throw new RuntimeException("团队应该为空");
                }
            }
        }else {
            throw new RuntimeException("机构不能为空");
        }
        if(!StrUtil.isEmpty(request.getTeamCode())){
            Map<String, Tbsaleteam> allTbsaleteamsMap = cacheService.getAllTbsaleteamsMap();
            if (allTbsaleteamsMap.containsKey(request.getTeamCode())) {
                log.info("supervisorPerformance getCheckInoListExport saleTeamInCode:{}",allTbsaleteamsMap.get(request.getTeamCode()).getSaleteamincode());
                request.setTeamCode(allTbsaleteamsMap.get(request.getTeamCode()).getSaleteamincode());
            }else {
                throw new RuntimeException("团队获取失败");
            }
        }
        List<BasicLawInfoVO> basicLawInfoVOS = new ArrayList<>();
        // 遍历所有的基本法类型并生成相应的基本信息
        for (BasicLawType basicLawType : BasicLawType.values()) {
            SupervisorPerformanceBasicLawStrategy strategy = factory.getStrategy(basicLawType.name());
            if (strategy != null) {
                List<BasicLawInfoVO> basicLawInfoVOs = strategy.generateBasicLawInfo(request);
                basicLawInfoVOS.addAll(basicLawInfoVOs);
            } else {
                log.info("getCheckInoListExport Unsupported BasicLawType: {}", basicLawType);
            }
        }
        if(!CollectionUtils.isEmpty(basicLawInfoVOS)){
            List<FamilyRiskManagerCheckInfo> riskManagerCheckInfos = new ArrayList<>();
            List<PartnerCheckInfo> partnerCheckInfoList = new ArrayList<>();
            for (BasicLawInfoVO basicLawInfoVO : basicLawInfoVOS) {
                if(basicLawInfoVO.getFamilyRiskManagerInfoVO() != null){
                    if(CollUtil.isNotEmpty(basicLawInfoVO.getFamilyRiskManagerInfoVO().getFamilyRiskManagerCheckInfos())){
                        riskManagerCheckInfos.addAll(basicLawInfoVO.getFamilyRiskManagerInfoVO().getFamilyRiskManagerCheckInfos());
                    }
                }
                if(basicLawInfoVO.getPartnerInfoVO() != null){
                    if(CollUtil.isNotEmpty(basicLawInfoVO.getPartnerInfoVO().getPartnerCheckInfoList())){
                        partnerCheckInfoList.addAll(basicLawInfoVO.getPartnerInfoVO().getPartnerCheckInfoList());
                    }
                }
            }
            List<PerformanceCheckRiskMangerExcel> riskMangerExcels = new ArrayList<>();
            List<PerformanceCheckExcel> performanceCheckExcelList = new ArrayList<>();
            if(CollUtil.isNotEmpty(riskManagerCheckInfos)){
                riskManagerCheckInfos.forEach(riskManagerCheckInfo -> {
                    PerformanceCheckRiskMangerExcel riskMangerExcel = new PerformanceCheckRiskMangerExcel();
                    BeanUtils.copyProperties(riskManagerCheckInfo, riskMangerExcel);
                    riskMangerExcels.add(riskMangerExcel);
                });
            }
            if(CollUtil.isNotEmpty(partnerCheckInfoList)){
                partnerCheckInfoList.forEach(partnerCheckInfo -> {
                    PerformanceCheckExcel performanceCheckExcel = new PerformanceCheckExcel();
                    BeanUtils.copyProperties(partnerCheckInfo, performanceCheckExcel);
                    performanceCheckExcelList.add(performanceCheckExcel);
                });
            }
            if(CollUtil.isNotEmpty(performanceCheckExcelList) || CollUtil.isNotEmpty(riskMangerExcels)){
                byte[] bytes = null;
                try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
                    // 创建 ExcelWriter 对象
                    ExcelWriter excelWriter = EasyExcel.write(bos)
                            .build();

                    // 创建第一个 Sheet（家庭风险管理师）
                    WriteSheet riskManagerSheet = EasyExcel.writerSheet(0, "家庭风险管理师")
                            .head(PerformanceCheckRiskMangerExcel.class)
                            .build();

                    // 创建第二个 Sheet（合伙人）
                    WriteSheet partnerSheet = EasyExcel.writerSheet(1, "合伙人")
                            .head(PerformanceCheckExcel.class)
                            .build();

                    // 写入数据到第一个 Sheet
                    if (CollUtil.isNotEmpty(riskMangerExcels)) {
                        excelWriter.write(riskMangerExcels, riskManagerSheet);
                    }

                    // 写入数据到第二个 Sheet
                    if (CollUtil.isNotEmpty(performanceCheckExcelList)) {
                        excelWriter.write(performanceCheckExcelList, partnerSheet);
                    }

                    // 关闭 ExcelWriter，完成写入
                    excelWriter.finish();

                    bytes = bos.toByteArray();
                } catch (IOException e) {
                    log.error("getCheckInoListExport-error:{}", e.getMessage(), e);
                }
                // 上传阿里云
                FileGetUrlsVO fileGetUrlsVO = fileApi.upload4Base64(FileBase64Request
                        .builder()
                        .fileName("考核汇总及明细数据"+ LocalDateTime.now()+ ".xlsx")
                        .source("admin")
                        .fileBase64String(Base64.encode(bytes))
                        .build());
                if(CollUtil.isNotEmpty(fileGetUrlsVO.getFileUrlList())){
                    return fileGetUrlsVO.getFileUrlList().get(0).getOuterUrl();
                }
            }
        }
        return "";
    }

    @Override
    public String getPerformanceListExport(SupervisorPerformanceRequest request) {
        SupervisorPerformanceVO vo = getPerformanceList(request);

        // 检查vo是否为空或其列表是否为空
        if (ObjectUtils.isEmpty(vo) ||
                (CollectionUtils.isEmpty(vo.getDetailVOList()) && CollectionUtils.isEmpty(vo.getAccVoList()))) {
            return "";
        }

        // 合并detailVOList和accVoList到allDetailVOList
        List<SupervisorPerformanceDetailVO> allDetailVOList = new ArrayList<>();
        allDetailVOList.addAll(CollectionUtils.isEmpty(vo.getDetailVOList()) ? Collections.emptyList() : vo.getDetailVOList());
        allDetailVOList.addAll(CollectionUtils.isEmpty(vo.getAccVoList()) ? Collections.emptyList() : vo.getAccVoList());

        // 使用Stream API简化转换过程
        List<PerformanceInstExcel> resultList = new ArrayList<>();
        List<PerformanceTeamExcel> teamList = new ArrayList<>();
        List<PerformanceAgentExcel> agentList = new ArrayList<>();
        if ("inst".equals(request.getGroupType())){
            resultList =  allDetailVOList.stream()
                    .map(performanceInstExcelAssembler::assemblerInst)
                    .collect(Collectors.toList());
        } else if ("team".equals(request.getGroupType())){
            teamList =  allDetailVOList.stream()
                    .map(performanceInstExcelAssembler::assemblerTeam)
                    .collect(Collectors.toList());
        }else if ("agent".equals(request.getGroupType())){
            agentList =  allDetailVOList.stream()
                    .map(performanceInstExcelAssembler::assemblerAgent)
                    .collect(Collectors.toList());
        }
        byte[] bytes = null;
        //查询数据 生成报表
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            if ("inst".equals(request.getGroupType())){
                EasyExcel.write(bos, PerformanceInstExcel.class)
                        .registerWriteHandler(new CellWriteWidthConfig())
                        .sheet("机构业绩汇总及数据明细")
                        .doWrite(resultList);
            }else if ("team".equals(request.getGroupType())){
                EasyExcel.write(bos, PerformanceTeamExcel.class)
                        .registerWriteHandler(new CellWriteWidthConfig())
                        .sheet("团队业绩汇总及数据明细")
                        .doWrite(teamList);
            }else if ("agent".equals(request.getGroupType())){
                EasyExcel.write(bos, PerformanceAgentExcel.class)
                        .registerWriteHandler(new CellWriteWidthConfig())
                        .sheet("个人业绩汇总及数据明细")
                        .doWrite(agentList);
            }
            bytes = bos.toByteArray();
        } catch (IOException e) {
            log.error("export.error:{}", e.getMessage(), e);
        }
        // 上传阿里云
        FileGetUrlsVO fileGetUrlsVO = fileApi.upload4Base64(FileBase64Request
                .builder()
                .fileName("业绩汇总及数据明细清单"+ LocalDateTime.now()+ ".xlsx")
                .source("admin")
                .fileBase64String(Base64.encode(bytes))
                .build());
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(fileGetUrlsVO.getFileUrlList())) {
            return fileGetUrlsVO.getFileUrlList().get(0).getOuterUrl();
        }
        return "";
    }
    @Override
    public List<TreeNodeVO> getSupervisorPartnerInstList() {
        String empCode = getCurrentLoginEmpCode();
        SupervisorEmployee supervisorEmployee = supervisorEmployeeMapper.getSupervisorEmployeeByCode(empCode);
        if (ObjectUtils.isEmpty(supervisorEmployee)){
            log.info("督导查询失败,未查到督导账号信息");
            return new ArrayList<>();
        }
        return getSelfSupervisorEmployeeInstList(supervisorEmployee);

    }

    private List<TreeNodeVO> getSelfSupervisorEmployeeInstList(SupervisorEmployee supervisorEmployee) {
        List<TreeNodeVO> voList = new ArrayList<>();
        OrgQueryRequest queryRequest = OrgQueryRequest.builder().current(1L).size(999).build();
        if (SupervisorType.ZongBu.name().equals(supervisorEmployee.getRoleType())){
            List<String>  topCodeList = JsonUtil.toList(supervisorEmployee.getTopCodeList(), List.class,String.class);
            if (!CollectionUtils.isEmpty(selfCompanyCodeList) && !CollectionUtils.isEmpty(topCodeList)){
                topCodeList = topCodeList.stream().filter(selfCompanyCodeList::contains).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(topCodeList)){
                return new ArrayList<>();
            }
            List<TreeNodeVO> treeNodeVOS = new ArrayList<>();
            for (String s : topCodeList) {
                RequestContextHolder.setAdminAppIdLocal(1L);
                RequestContextHolder.setStaffIdLocal(1L);
                queryRequest.setTopCode(s);
                treeNodeVOS.addAll(partnerOrgService.listAllSimple(queryRequest));
                RequestContextHolder.setAdminAppIdLocal(null);
                RequestContextHolder.setStaffIdLocal(null);
            }
            log.info("查询到总部督导权限:{}", treeNodeVOS);
            if (!CollectionUtils.isEmpty(treeNodeVOS)){
                for (TreeNodeVO vo : treeNodeVOS) {
                    List<TreeNodeVO> children = vo.getChildren();
                    if (!CollectionUtils.isEmpty(children)){
                        voList.addAll(children);
                    }
                }
            }
            return voList;
        }else if (SupervisorType.JiGou.name().equals(supervisorEmployee.getRoleType())){
            if (!CollectionUtils.isEmpty(selfCompanyCodeList)){
                if (!selfCompanyCodeList.contains(supervisorEmployee.getTopCode())){
                    log.info("非个团督导账号");
                    return voList;
                }
            }
            List<String>  orgCodeList = JsonUtil.toList(supervisorEmployee.getOrgCodeList(), List.class,String.class);
            if (CollectionUtils.isEmpty(orgCodeList)){
                return new ArrayList<>();
            }else{
                Set<String> set = new HashSet<>(orgCodeList);
                Page<BaseInst> baseInstPage = cacheService.selectBaseInstPage(AppConsts.CPTYPE_PARTNER, queryRequest, set);
                List<TreeNodeVO> treeNodeVOS = BeanCopier.copyList(baseInstPage.getRecords(), PartnerConverter::instToTreeNodeVo);
                if (!CollectionUtils.isEmpty(treeNodeVOS) && !CollectionUtils.isEmpty(selfCompanyCodeList)){
                    treeNodeVOS = treeNodeVOS.stream().filter(treeNodeVO -> selfCompanyCodeList.contains(treeNodeVO.getParentCode())).collect(Collectors.toList());
                }
                if (!CollectionUtils.isEmpty(treeNodeVOS)){
                    voList.addAll(treeNodeVOS);
                }
            }
        }else if (SupervisorType.TuanDui.name().equals(supervisorEmployee.getRoleType())){
            if (!CollectionUtils.isEmpty(selfCompanyCodeList)){
                if (!selfCompanyCodeList.contains(supervisorEmployee.getTopCode())){
                    log.info("非个团督导账号");
                    return voList;
                }
            }
            String orgCode = supervisorEmployee.getOrgCode();
            Set<String> set = new HashSet<>(Collections.singletonList(orgCode));
            Page<BaseInst> baseInstPage = cacheService.selectBaseInstPage(AppConsts.CPTYPE_PARTNER, queryRequest, set);
            List<TreeNodeVO> treeNodeVOS = BeanCopier.copyList(baseInstPage.getRecords(), PartnerConverter::instToTreeNodeVo);
            if (!CollectionUtils.isEmpty(treeNodeVOS)){
                voList.addAll(treeNodeVOS);
            }
        }
        if (!CollectionUtils.isEmpty(voList)){
            //按code排序
            voList.sort(Comparator.comparing(TreeNodeVO::getCode));
        }
        return voList;
    }

    @Override
    public List<TreeNodeVO> getSupervisorTeamList(String partnerInstCode) {
        String empCode = getCurrentLoginEmpCode();
        SupervisorEmployee supervisorEmployee = supervisorEmployeeMapper.getSupervisorEmployeeByCode(empCode);
        if (ObjectUtils.isEmpty(supervisorEmployee)){
            log.info("督导查询失败,未查到督导账号信息");
            return new ArrayList<>();
        }
        List<TreeNodeVO> teamList = new ArrayList<>();
        if (SupervisorType.TuanDui.name().equals(supervisorEmployee.getRoleType())){
            if (!StringUtils.isEmpty(partnerInstCode)){
                if (!partnerInstCode.equals(supervisorEmployee.getOrgCode())){
                    return teamList;
                }
            }
            if (!StringUtils.isEmpty(supervisorEmployee.getTeamCode())){
                List<Tbsaleteam> saleteams = cacheService.getAllTbsaleteams();
                saleteams.stream().filter(s -> s.getSaleteamcode().equals(supervisorEmployee.getTeamCode())).findFirst().ifPresent(o->{
                    TreeNodeVO treeNodeVO = new TreeNodeVO();
                    treeNodeVO.setCode(o.getSaleteamcode());
                    treeNodeVO.setName(o.getSaleteamname());
                    teamList.add(treeNodeVO);
                });
            }
        }else{
            //查询机构对应的团队数据
            List<Tbsaleteam> saleteams = cacheService.getAllTbsaleteams();
            saleteams.stream().filter(s -> s.getInstCode().equals(partnerInstCode) && "01".equals(s.getTeamlevel())).forEach(o->{
                TreeNodeVO treeNodeVO = new TreeNodeVO();
                treeNodeVO.setCode(o.getSaleteamcode());
                treeNodeVO.setName(o.getSaleteamname());
                teamList.add(treeNodeVO);
            });
        }
        if (!CollectionUtils.isEmpty(teamList)){
            //按code排序
            teamList.sort(Comparator.comparing(TreeNodeVO::getCode));
        }
        return teamList;
    }

    @Override
    public PerformanceVO getMonthPerformance() {
        String empCode = getCurrentLoginEmpCode();
        if (StringUtils.isEmpty(empCode) || !empCode.startsWith("S")){
            log.info("督导查询失败,不是督导账号");
            return null;
        }
        SupervisorEmployee supervisorEmployee = supervisorEmployeeMapper.getSupervisorEmployeeByCode(empCode);
        if (ObjectUtils.isEmpty(supervisorEmployee)){
            log.info("督导查询失败,未查到督导账号信息");
            return null;
        }
        PerformanceVO performanceVO = new PerformanceVO();
//        List<String> policyNoList = getSelfPolicyNoList();
        //判断账号类型
        if (SupervisorType.ZongBu.name().equals(supervisorEmployee.getRoleType()) || SupervisorType.JiGou.name().equals(supervisorEmployee.getRoleType())){
            List<TreeNodeVO> nodeVOList = getSelfSupervisorEmployeeInstList(supervisorEmployee);
            if (CollectionUtils.isEmpty(nodeVOList)){
                log.info("查询到督导机构权限为空");
                return performanceVO;
            }
            List<String> codeList = nodeVOList.stream().map(o -> o.getCode()).collect(Collectors.toList());
            performanceVO = daDetailService.selectPerformanceAcc(codeList,null);
        }else if (SupervisorType.TuanDui.name().equals(supervisorEmployee.getRoleType())){
            String teamCode = supervisorEmployee.getTeamCode();
            if (StringUtils.isEmpty(teamCode)){
                log.info("查询到督导团队权限为空");
                return performanceVO;
            }
            performanceVO = daDetailService.selectPerformanceAcc(null,teamCode);
        }
        if (ObjectUtils.isEmpty(performanceVO)){
            performanceVO = new PerformanceVO();
        }
        zeroIfNull(performanceVO);
        return performanceVO;
    }

    private List<String> getSelfPolicyNoList() {
        List<SimpleNodeVO> voList = performanceService.getSelfPolicyList();
        if (CollectionUtils.isEmpty(voList)){
            return new ArrayList<>();
        }
        return voList.stream().map(o -> o.getCode()).collect(Collectors.toList());
    }


    @Override
    public SupervisorPerformanceVO getPerformanceList(String partnerInstCode, String teamCode, Date startDate, Date endDate, String accType, String groupType) {

        SupervisorPerformanceVO performanceVO = new SupervisorPerformanceVO();
        List<SupervisorPerformanceDetailVO> accList = new ArrayList<>();
        List<SupervisorPerformanceDetailVO> list = new ArrayList<>();
        SupervisorPerformanceDetailVO accVo = new SupervisorPerformanceDetailVO();
        accVo.setInstCode("汇总");
        accVo.setInstName("汇总");

        //测试数据
        accList.add(accVo);
        SupervisorPerformanceDetailVO vo1  = new SupervisorPerformanceDetailVO();
        vo1.setInstCode("code1");
        vo1.setInstName("机构1");
        list.add(vo1);
        SupervisorPerformanceDetailVO vo2  = new SupervisorPerformanceDetailVO();
        vo2.setInstCode("code2");
        vo2.setInstName("机构2");
        list.add(vo2);
        if ("team".equals(groupType)){
            vo1.setTeamCode("code1");
            vo1.setTeamName("团队1");
            vo2.setTeamCode("code2");
            vo2.setTeamName("团队2");
        }else if ("agent".equals(groupType)){
            vo1.setTeamCode("code1");
            vo1.setTeamName("团队1");
            vo2.setTeamCode("code2");
            vo2.setTeamName("团队2");
            vo1.setAgentCode("code1");
            vo1.setAgentName("name1");
            vo2.setAgentCode("code2");
            vo2.setAgentName("name2");
        }
        for (SupervisorPerformanceDetailVO vo : list) {
            zeroIfNull(vo);
//            accData(accVo, vo);
        }
        performanceVO.setAccVoList(accList);
        performanceVO.setDetailVOList(list);

        return performanceVO;
    }

    @Override
    public SupervisorMarkVO getSelfPolicy(SupervisorPerformanceRequest request) {
        String empCode = getCurrentLoginEmpCode();
        if (StringUtils.isEmpty(empCode) || !empCode.startsWith("S")){
            log.info("督导查询失败,不是督导账号");
            return null;
        }
        SupervisorEmployee supervisorEmployee = supervisorEmployeeMapper.getSupervisorEmployeeByCode(empCode);
        if (ObjectUtils.isEmpty(supervisorEmployee)){
            log.info("督导查询失败,未查到督导账号信息");
            return null;
        }
        SupervisorMarkVO markVO = new SupervisorMarkVO();
        List<MarkDetailVO>  selfPolicyList = getHologresSelfPolicy(supervisorEmployee,request.getInstCodeList(),request.getTeamCode());
        markVO.setSelfPolicyCount(selfPolicyList.size());
        markVO.setSelfPolicyList(selfPolicyList);
        return markVO;
    }

    @Override
    public SupervisorMarkVO getDisCompleteCr13(SupervisorPerformanceRequest request) {
        String empCode = getCurrentLoginEmpCode();
        if (StringUtils.isEmpty(empCode) || !empCode.startsWith("S")){
            log.info("督导查询失败,不是督导账号");
            return null;
        }
        SupervisorEmployee supervisorEmployee = supervisorEmployeeMapper.getSupervisorEmployeeByCode(empCode);
        if (ObjectUtils.isEmpty(supervisorEmployee)){
            log.info("督导查询失败,未查到督导账号信息");
            return null;
        }
        SupervisorMarkVO markVO = new SupervisorMarkVO();
        List<MarkDetailVO> crList = getSupervisorCrList(supervisorEmployee,request.getInstCodeList(),request.getTeamCode());
        markVO.setDisCompleteCr13Count(crList.size());
        markVO.setCr13List(crList);
        return markVO;
    }

    @Override
    public SupervisorMarkVO getCtPolicy(SupervisorPerformanceRequest request) {
        String empCode = getCurrentLoginEmpCode();
        if (StringUtils.isEmpty(empCode) || !empCode.startsWith("S")){
            log.info("督导查询失败,不是督导账号");
            return null;
        }
        SupervisorEmployee supervisorEmployee = supervisorEmployeeMapper.getSupervisorEmployeeByCode(empCode);
        if (ObjectUtils.isEmpty(supervisorEmployee)){
            log.info("督导查询失败,未查到督导账号信息");
            return null;
        }
        SupervisorMarkVO markVO = new SupervisorMarkVO();
        List<MarkDetailVO> ctList = getSupervisorCtList(supervisorEmployee,request.getInstCodeList(),request.getTeamCode());
        markVO.setCtPolicyCount(ctList.size());
        markVO.setCtPolicyList(ctList);
        return markVO;
    }

    @Override
    public SupervisorPerformanceVO getPerformanceList(SupervisorPerformanceRequest request) {
        SupervisorPerformanceVO performanceVO = new SupervisorPerformanceVO();
        List<SupervisorPerformanceDetailNumberVO> accList = new ArrayList<>();
        List<SupervisorPerformanceDetailNumberVO> list = new ArrayList<>();
//        LinkedHashMap<String,String> headList = new LinkedHashMap<>();
        List<HeadVO> headList = new ArrayList<>();

        SupervisorPerformanceDetailNumberVO accVo = new SupervisorPerformanceDetailNumberVO();
        accVo.setInstCode("汇总");
        accVo.setInstName("汇总");
        accVo.setTeamName("-");
        accVo.setAgentName("-");
        if (CollectionUtils.isEmpty(request.getInstCodeList()) && StringUtils.isEmpty(request.getTeamCode())){
            List<TreeNodeVO> supervisorPartnerInstList = getSupervisorPartnerInstList();
            if (CollectionUtils.isEmpty(supervisorPartnerInstList)){
                return performanceVO;
            }else{
                request.setInstCodeList(supervisorPartnerInstList.stream().map(TreeNodeVO::getCode).collect(Collectors.toList()));
            }
        }
        headList.add(new HeadVO("机构","instName"));
        accList.add(accVo);
        list = getSupervisorPerformanceVOList(request,null);
//        List<SupervisorPerformanceDetailNumberVO> appNumList = getSupervisorPerformanceVOList(request,"appNum");
        List<SupervisorPerformanceDetailNumberVO> increaseList = getSupervisorIncreaseVOList(request);
        combineList(list,increaseList,request.getGroupType());
        String groupType = request.getGroupType();
        if ("team".equals(groupType)){
            headList.add(new HeadVO("团队","teamName"));
        }else if ("agent".equals(groupType)){
            headList.add(new HeadVO("团队","teamName"));
            headList.add(new HeadVO("代理人","agentName"));
        }
        addHead(headList);
        for (SupervisorPerformanceDetailNumberVO vo : list) {
            zeroIfNull(vo);
            accData(accVo, vo);
        }
//        if (!CollectionUtils.isEmpty(appNumList) && !CollectionUtils.isEmpty(list)){
//            accVo.setFycAppntNoNum(0);
//            accVo.setPeriodAppntNoNum(0);
//            SupervisorPerformanceDetailNumberVO numberVO = appNumList.get(0);
//            zeroIfNull(numberVO);
//            accVo.setFycAppntNoNum(numberVO.getFycAppntNoNum());
//            accVo.setPeriodAppntNoNum(numberVO.getPeriodAppntNoNum());
//        }
        performanceVO.setHeadList(headList);
        performanceVO.setAccVoList(transData(accList));
        performanceVO.setDetailVOList(transData(list));

        return performanceVO;
    }

    private void combineList(List<SupervisorPerformanceDetailNumberVO> list, List<SupervisorPerformanceDetailNumberVO> increaseList, String groupType) {
        if (CollectionUtils.isEmpty(increaseList)){
            return ;
        }
        if (CollectionUtils.isEmpty(list)){
            list.addAll(increaseList);
        }
        List<SupervisorPerformanceDetailNumberVO> tempList = new ArrayList<>();
        if ("inst".equals(groupType)){
            for (SupervisorPerformanceDetailNumberVO increaseVo : increaseList) {
                Optional<SupervisorPerformanceDetailNumberVO> first = list.stream().filter(vo -> Objects.equals(vo.getInstCode(), increaseVo.getInstCode())).findFirst();
                if (first.isPresent()){
                    first.get().setIncreaseNum(increaseVo.getIncreaseNum());
                }else{
                    tempList.add(increaseVo);
                }
            }

        }else if ("team".equals(groupType)){
            for (SupervisorPerformanceDetailNumberVO increaseVo : increaseList) {
                Optional<SupervisorPerformanceDetailNumberVO> first = list.stream().filter(vo -> Objects.equals(vo.getTeamCode(), increaseVo.getTeamCode())).findFirst();
                if (first.isPresent()){
                    first.get().setIncreaseNum(increaseVo.getIncreaseNum());
                }else{
                    tempList.add(increaseVo);
                }
            }
        }else if ("agent".equals(groupType)){
            for (SupervisorPerformanceDetailNumberVO increaseVo : increaseList) {
                Optional<SupervisorPerformanceDetailNumberVO> first = list.stream().filter(vo -> Objects.equals(vo.getAgentCode(), increaseVo.getAgentCode())).findFirst();
                if (first.isPresent()){
                    first.get().setIncreaseNum(increaseVo.getIncreaseNum());
                }else{
                    tempList.add(increaseVo);
                }
            }
        }
        list.addAll(tempList);
        //按instCode升序,teamCode升序,fycPremium降序 重新排序 且排序字段为空时放在最后
        if ("inst".equals(groupType)){
            list.sort(Comparator
                    .comparing(SupervisorPerformanceDetailNumberVO::getFycPremium,
                            Comparator.nullsLast(Comparator.reverseOrder())));

        }else if ("team".equals(groupType)){
            list.sort(Comparator
                    .comparing(SupervisorPerformanceDetailNumberVO::getInstCode,
                            Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing(SupervisorPerformanceDetailNumberVO::getFycPremium,
                            Comparator.nullsLast(Comparator.reverseOrder())));
        }else if ("agent".equals(groupType)){
            list.sort(Comparator
                    .comparing(SupervisorPerformanceDetailNumberVO::getInstCode,
                            Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing(SupervisorPerformanceDetailNumberVO::getTeamCode,
                            Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing(SupervisorPerformanceDetailNumberVO::getFycPremium,
                            Comparator.nullsLast(Comparator.reverseOrder())));
        }
    }

    private List<SupervisorPerformanceDetailNumberVO> getSupervisorIncreaseVOList(SupervisorPerformanceRequest request) {
        List<String> instCodeList = request.getInstCodeList();
        String teamCode = request.getTeamCode();
        String accType = request.getAccType();
        String groupType = request.getGroupType();
        LocalDate startDate = request.getStartDate();
        LocalDate endDate = request.getEndDate();
        if (ObjectUtils.isEmpty(request.getStartDate()) || StringUtils.isEmpty(accType) || StringUtils.isEmpty(groupType)){
            return new ArrayList<>();
        }
        if (ObjectUtils.isEmpty(request.getEndDate())){
            endDate = LocalDate.now();
        }

        Map map = new HashMap();
        if (!CollectionUtils.isEmpty(instCodeList)){
            map.put("performanceGroupList",instCodeList);
        }
        if (!StringUtils.isEmpty(teamCode)) {
            map.put("teamCode",teamCode);
        }
        if (!StringUtils.isEmpty(accType)){
            map.put("accType",accType);
        }
        if (!StringUtils.isEmpty(groupType)){
            map.put("groupType",groupType);
        }
        if ("time".equals(request.getType())){
            map.put("type","time");
        }
        map.put("startDate",startDate);
        map.put("endDate",endDate);
        return performanceService.getSupervisorIncreaseVOList(map);
    }

    private void addHead(List<HeadVO> headList) {
        headList.add(new HeadVO("首年保费(元)","fycPremium"));
        headList.add(new HeadVO("首年标保(元)","dcp"));
        headList.add(new HeadVO("首年保单件数(件)","fycPolicyNum"));
        headList.add(new HeadVO("期交保费(元)","periodPremium"));
        headList.add(new HeadVO("期交标保(元)","periodDCP"));
        headList.add(new HeadVO("期交件数(件)","periodPolicyNum"));
        headList.add(new HeadVO("新单短险保费(元)","fycMRiskPremium"));
        headList.add(new HeadVO("新单短险件数(件)","fycMRiskPolicyNum"));
        headList.add(new HeadVO("新单客户数(人)","fycAppntNoNum"));
        headList.add(new HeadVO("期交客户数(人)","periodAppntNoNum"));
        headList.add(new HeadVO("增员人数(人)","increaseNum"));
    }

    private List<SupervisorPerformanceDetailVO> transData(List<SupervisorPerformanceDetailNumberVO> numberList) {
        List<SupervisorPerformanceDetailVO> voList = new ArrayList<>();
        if (CollectionUtils.isEmpty(numberList)){
            return voList;
        }
        for (SupervisorPerformanceDetailNumberVO numberVO : numberList) {
            SupervisorPerformanceDetailVO vo = new SupervisorPerformanceDetailVO();
            voList.add(vo);
            for (Field field : vo.getClass().getDeclaredFields()) {
                field.setAccessible(true);
                try {
                    Field declaredField = numberVO.getClass().getDeclaredField(field.getName());
                    declaredField.setAccessible(true);
                    field.set(vo, "");
                    if (ObjectUtils.isEmpty(declaredField)){
                        continue;
                    }
                    if (declaredField.getType().equals(String.class)){
                        field.set(vo, declaredField.get(numberVO));
                    }else if (declaredField.getType().equals(BigDecimal.class)){
                        field.set(vo, formatAmountWithCommas(declaredField.get(numberVO)));
                    }else if (declaredField.getType().equals(Integer.class)){
                        field.set(vo, formatAmountWithCommas(declaredField.get(numberVO)));
                    }
                }catch ( Exception e){
                    throw new RuntimeException(e);
                }
            }
        }
        return voList;
    }


    public String formatAmountWithCommas(Object amount) {
        BigDecimal decimalAmount;
        // 根据传入参数类型进行转换
        if (amount instanceof BigDecimal) {
            decimalAmount = (BigDecimal) amount;
        } else if (amount instanceof Integer) {
            decimalAmount = new BigDecimal((Integer) amount);
        } else {
            return "";
        }
        return formatWithCommas(decimalAmount);
    }


    public String formatAmount(Object amount) {
        BigDecimal decimalAmount;

        // 根据传入参数类型进行转换
        if (amount instanceof BigDecimal) {
            decimalAmount = (BigDecimal) amount;
        } else if (amount instanceof Integer) {
            decimalAmount = new BigDecimal((Integer) amount);
        } else {
            return "";
        }

        // 按照不同金额范围进行单位转换和格式化
        if (decimalAmount.compareTo(new BigDecimal(100000000)) >= 0) {
        // 金额大于等于100,000,000元，单位展示亿元
            BigDecimal billionAmount = decimalAmount.divide(new BigDecimal(100000000), 2, BigDecimal.ROUND_HALF_UP);
            return formatWithCommas(billionAmount) + "亿元";
        } else if (decimalAmount.compareTo(new BigDecimal(10000)) >= 0) {
        // 金额大于等于10,000元，小于100,000,000元，单位展示万元
            BigDecimal millionAmount = decimalAmount.divide(new BigDecimal(10000), 2, BigDecimal.ROUND_HALF_UP);
            return formatWithCommas(millionAmount) + "万元";
        } else {
        // 金额小于10,000元，单位展示元
            return formatWithCommas(decimalAmount) + "元";
        }
    }

    // 辅助方法，用于按千分位格式化数字
    private static String formatWithCommas(BigDecimal number) {
        DecimalFormat formatter = new DecimalFormat("#,###.##");
        return formatter.format(number);
    }

    private List<SupervisorPerformanceDetailNumberVO> getSupervisorPerformanceVOList(SupervisorPerformanceRequest request, String appFlag) {
        List<String> instCodeList = request.getInstCodeList();
        String teamCode = request.getTeamCode();
        String accType = request.getAccType();
        String groupType = request.getGroupType();
        LocalDate startDate = request.getStartDate();
        LocalDate endDate = request.getEndDate();
        if (ObjectUtils.isEmpty(request.getStartDate()) || StringUtils.isEmpty(accType) || StringUtils.isEmpty(groupType)){
            return new ArrayList<>();
        }
        if (ObjectUtils.isEmpty(request.getEndDate())){
            endDate = LocalDate.now();
        }
        Map map = new HashMap();
        if (!CollectionUtils.isEmpty(instCodeList)){
            map.put("performanceGroupList",instCodeList);
        }
        if (!StringUtils.isEmpty(teamCode)) {
            map.put("teamCode",teamCode);
        }
        if (!StringUtils.isEmpty(accType)){
            map.put("accType",accType);
        }
        if (!StringUtils.isEmpty(groupType)){
            map.put("groupType",groupType);
        }
        if ("time".equals(request.getType())){
            map.put("type","time");
        }
        map.put("startDate",startDate);
        map.put("endDate",endDate);
        if (StringUtils.isEmpty(appFlag)){
            List<SupervisorPerformanceDetailNumberVO> list = daDetailService.selectPerformanceList(map);
            if (CollectionUtils.isEmpty(list)){
                return new ArrayList<>();
            }
            return list;
        }else{
            List<SupervisorPerformanceDetailNumberVO> appNumList = daDetailService.selectAppNumList(map);
            if (CollectionUtils.isEmpty(appNumList) || (appNumList.size() ==1 && appNumList.get(0).getFycAppntNoNum()==0 && appNumList.get(0).getPeriodAppntNoNum()==0)){
                return new ArrayList<>();
            }
            return appNumList;
        }
    }

    @Override
    public SupervisorCheckInfoVO getCheckInoList(SupervisorPerformanceRequest request) {
        log.info("supervisorPerformance getCheckInoList request:{}",request);
        //校验请求参数
        if(!CollectionUtils.isEmpty(request.getInstCodeList())){
            if(request.getInstCodeList().size() > 1){
                if(!StrUtil.isEmpty(request.getTeamCode())){
                    throw new RuntimeException("团队应该为空");
                }
            }
        }else {
            throw new RuntimeException("机构不能为空");
        }
        if(!StrUtil.isEmpty(request.getTeamCode())){
            Map<String, Tbsaleteam> allTbsaleteamsMap = cacheService.getAllTbsaleteamsMap();
            if (allTbsaleteamsMap.containsKey(request.getTeamCode())) {
                log.info("supervisorPerformance getCheckInoList saleTeamInCode:{}",allTbsaleteamsMap.get(request.getTeamCode()).getSaleteamincode());
                request.setTeamCode(allTbsaleteamsMap.get(request.getTeamCode()).getSaleteamincode());
            }else {
                throw new RuntimeException("团队获取失败");
            }
        }
        SupervisorCheckInfoVO supervisorCheckInfoVO = new SupervisorCheckInfoVO();
        List<BasicLawInfoVO> basicLawInfoVOList = new ArrayList<>();

        // 遍历所有的基本法类型并生成相应的基本信息
        for (BasicLawType basicLawType : BasicLawType.values()) {
            SupervisorPerformanceBasicLawStrategy strategy = factory.getStrategy(basicLawType.name());
            if (strategy != null) {
                List<BasicLawInfoVO> basicLawInfoVOs = strategy.generateBasicLawInfo(request);
                basicLawInfoVOList.addAll(basicLawInfoVOs);
            } else {
                log.info("Unsupported BasicLawType: {}", basicLawType);
            }
        }
        supervisorCheckInfoVO.setBasicLawInfoVOList(basicLawInfoVOList);
        return supervisorCheckInfoVO;
    }

    @Override
    public SupervisorMarkVO getAllMark(SupervisorPerformanceRequest request) {
        SupervisorMarkVO markVO = new SupervisorMarkVO();
        String empCode = getCurrentLoginEmpCode();
        SupervisorEmployee supervisorEmployee = supervisorEmployeeMapper.getSupervisorEmployeeByCode(empCode);
        if (ObjectUtils.isEmpty(supervisorEmployee)){
            log.info("督导查询失败,未查到督导账号信息");
            return markVO;
        }
        List<MarkDetailVO>  selfPolicyList = getHologresSelfPolicy(supervisorEmployee,request.getInstCodeList(),request.getTeamCode());
        markVO.setSelfPolicyCount(selfPolicyList.size());
        markVO.setSelfPolicyList(selfPolicyList);

        List<MarkDetailVO> crList = getSupervisorCrList(supervisorEmployee,request.getInstCodeList(),request.getTeamCode());
        markVO.setDisCompleteCr13Count(crList.size());
        markVO.setCr13List(crList);

        List<MarkDetailVO> ctList = getSupervisorCtList(supervisorEmployee,request.getInstCodeList(),request.getTeamCode());
        markVO.setCtPolicyCount(ctList.size());
        markVO.setCtPolicyList(ctList);

        return markVO;
    }

    private List<MarkDetailVO> getSupervisorCtList(SupervisorEmployee supervisorEmployee, List<String> instCodeList, String teamCode) {
        List<MarkDetailVO> voList = new ArrayList<>();
        LocalDate now = LocalDate.now();
        LocalDate startDate = now.withDayOfMonth(1);
        if (!StringUtils.isEmpty(teamCode)){
            voList = daDetailService.getSupervisorCtList(null,teamCode,startDate,now);
        }else if (!CollectionUtils.isEmpty(instCodeList)){
            voList = daDetailService.getSupervisorCtList(instCodeList,"",startDate,now);
        }else if (SupervisorType.ZongBu.name().equals(supervisorEmployee.getRoleType()) || SupervisorType.JiGou.name().equals(supervisorEmployee.getRoleType())){
            List<TreeNodeVO> nodeVOList = getSelfSupervisorEmployeeInstList(supervisorEmployee);
            if (CollectionUtils.isEmpty(nodeVOList)){
                log.info("查询到督导机构权限为空");
                return new ArrayList<>();
            }
            List<String> codeList = nodeVOList.stream().map(o -> o.getCode()).collect(Collectors.toList());

            voList = daDetailService.getSupervisorCtList(codeList,"",startDate,now);
        }else if (SupervisorType.TuanDui.name().equals(supervisorEmployee.getRoleType())){
            teamCode = supervisorEmployee.getTeamCode();
            if (StringUtils.isEmpty(teamCode)){
                log.info("查询到督导团队权限为空");
                return new ArrayList<>();
            }
            voList = daDetailService.getSupervisorCtList(null,teamCode,startDate,now);
        }
        if (CollectionUtils.isEmpty(voList)){
            return new ArrayList<>();
        }else {
            voList.forEach(o -> {
                Date ctD = o.getCtD();
                if (ctD != null){
                    //转换ctd为LocalDate
                    o.setCtDate(ctD.toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
                }
                initBigDecimalRound(o,2);
            });
            return voList;
        }
    }

    private void initBigDecimalRound(MarkDetailVO vo, Integer i) {
        if (vo == null || i==null){
            return;
        }
        for (Field field : vo.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            try {
                if (field.getType().equals(BigDecimal.class)) {
                    Object o = field.get(vo);
                    if (o != null) {
                        field.set(vo, ((BigDecimal) o).setScale(i, BigDecimal.ROUND_HALF_UP));
                    }
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    private List<MarkDetailVO> getSupervisorCrList(SupervisorEmployee supervisorEmployee, List<String> instCodeList, String teamCode) {
        List<MarkDetailVO> voList = new ArrayList<>();
        String commissionMonth = DateUtils.format(new Date(), "yyyy-MM");
        if (!StringUtils.isEmpty(teamCode)){
            voList = performanceService.getSupervisorCrList(null,teamCode, commissionMonth);
        }else if (!CollectionUtils.isEmpty(instCodeList)){
            voList = performanceService.getSupervisorCrList(instCodeList,"",commissionMonth);
        }else if (SupervisorType.ZongBu.name().equals(supervisorEmployee.getRoleType()) || SupervisorType.JiGou.name().equals(supervisorEmployee.getRoleType())){
            List<TreeNodeVO> nodeVOList = getSelfSupervisorEmployeeInstList(supervisorEmployee);
            if (CollectionUtils.isEmpty(nodeVOList)){
                log.info("查询到督导机构权限为空");
                return new ArrayList<>();
            }
            List<String> codeList = nodeVOList.stream().map(o -> o.getCode()).collect(Collectors.toList());
            voList = performanceService.getSupervisorCrList(codeList,"", commissionMonth);
        }else if (SupervisorType.TuanDui.name().equals(supervisorEmployee.getRoleType())){
            teamCode = supervisorEmployee.getTeamCode();
            if (StringUtils.isEmpty(teamCode)){
                log.info("查询到督导团队权限为空");
                return new ArrayList<>();
            }
            voList = performanceService.getSupervisorCrList(null,teamCode, commissionMonth);
        }
        if (CollectionUtils.isEmpty(voList)){
            return new ArrayList<>();
        }else {
            voList.forEach(o->{
                if (null != o.getCr13()){
                    o.setCr13(o.getCr13().multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP));
                }
                String remark = o.getRemark();
                if (!StringUtils.isEmpty(remark)){
                    if (canJsonArray(remark)){
                        List<ContinueRateInfo> continueRateInfoList = JSONArray.parseArray(remark, ContinueRateInfo.class);
                        if (!org.springframework.util.CollectionUtils.isEmpty(continueRateInfoList)){
                            List<ContinueRateInfo> r13List = continueRateInfoList.stream().filter(c -> !org.apache.commons.lang3.StringUtils.isEmpty(c.CONTINUE_RATE_CODE) && "R13".equals(c.CONTINUE_RATE_CODE)).collect(Collectors.toList());
                            if (!org.springframework.util.CollectionUtils.isEmpty(r13List)){
                                ContinueRateInfo continueRateInfo = r13List.get(0);
                                if (!org.springframework.util.StringUtils.isEmpty(continueRateInfo.NUMERATOR)){
                                    o.setCr13ActualPremium(new BigDecimal(continueRateInfo.NUMERATOR).setScale(2, BigDecimal.ROUND_HALF_UP));
                                }
                                if (!org.springframework.util.StringUtils.isEmpty(continueRateInfo.DENOMINATOR)){
                                    o.setCr13Premium(new BigDecimal(continueRateInfo.DENOMINATOR).setScale(2, BigDecimal.ROUND_HALF_UP));
                                }
                            }
                        }
                    }
                }
                o.setRemark(null);
            });
            voList = voList.stream().filter(o->!ObjectUtils.isEmpty(o.getCr13Premium()) && o.getCr13Premium().compareTo(BigDecimal.ZERO)!=0).collect(Collectors.toList());
            voList.forEach(o->{
                if (ObjectUtils.isEmpty(o.getCr13ActualPremium()) && ObjectUtils.isEmpty(o.getCr13Premium())){
                    o.setCr13(BigDecimal.ZERO);
                }
            });
            voList.sort(Comparator.comparing(
                    MarkDetailVO::getCr13,
                    Comparator.nullsLast(Comparator.reverseOrder())
            ));
            return voList;
        }
    }

    private boolean canJsonArray(String remark) {
        try {
            JSONArray.parseArray(remark);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    private List<MarkDetailVO> getHologresSelfPolicy(SupervisorEmployee supervisorEmployee, List<String> instCodeList, String teamCode) {
        List<MarkDetailVO> voList = new ArrayList<>();
        LocalDate now = LocalDate.now();
        LocalDate startDate = now.withDayOfMonth(1);
        if (!StringUtils.isEmpty(teamCode)){
            voList = daDetailService.getHologresSelfPolicyList(null,teamCode,startDate,now);
        }else if (!CollectionUtils.isEmpty(instCodeList) ){
            voList = daDetailService.getHologresSelfPolicyList(instCodeList,"",startDate,now);
        }else if (SupervisorType.ZongBu.name().equals(supervisorEmployee.getRoleType()) || SupervisorType.JiGou.name().equals(supervisorEmployee.getRoleType())){
            List<TreeNodeVO> nodeVOList = getSelfSupervisorEmployeeInstList(supervisorEmployee);
            if (CollectionUtils.isEmpty(nodeVOList)){
                log.info("查询到督导机构权限为空");
                return new ArrayList<>();
            }
            List<String> codeList = nodeVOList.stream().map(o -> o.getCode()).collect(Collectors.toList());

            voList = daDetailService.getHologresSelfPolicyList(codeList,"",startDate,now);
        }else if (SupervisorType.TuanDui.name().equals(supervisorEmployee.getRoleType())){
            teamCode = supervisorEmployee.getTeamCode();
            if (StringUtils.isEmpty(teamCode)){
                log.info("查询到督导团队权限为空");
                return new ArrayList<>();
            }
            voList = daDetailService.getHologresSelfPolicyList(null,teamCode,startDate,now);
        }
        if (CollectionUtils.isEmpty(voList)){
            return new ArrayList<>();
        }else {
            voList = voList.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MarkDetailVO::getPolicyNo))),
                            ArrayList::new
                    ));
            voList.forEach(o->{
                //脱敏规则：投被保人两个字时：保留第一个字，第二个字*。名字大于2个字时，保留第一个和最后一个字，中间*
                if (!StringUtils.isEmpty(o.getAppntName()) && !o.getAppntName().contains("*")){
                    o.setAppntName(desensitizeName(o.getAppntName()));
                }
                if (!StringUtils.isEmpty(o.getInsuredName()) && !o.getInsuredName().contains("*")){
                    o.setInsuredName(desensitizeName(o.getInsuredName()));
                }
            });
            return voList;
        }
    }

    // 脱敏工具方法
    private static String desensitizeName(String name) {
        if (name == null || name.isEmpty()) {
            return name;
        }
        int length = name.length();
        if (length == 1) {
            return name; // 单字不做处理
        }
        if (length == 2) {
            return name.charAt(0) + "*";
        }
        // 长度>2时保留首尾，中间用*代替
        StringBuilder sb = new StringBuilder();
        sb.append(name.charAt(0));
        for (int i = 0; i < length - 2; i++) {
            sb.append('*');
        }
        if (length > 2) sb.append(name.charAt(length - 1));
        return sb.toString();
    }


    private String getCurrentLoginEmpCode() {
        //获取登录人code
//        return null;
//        return "S000000094";
        return RequestContextHolder.getEmployeeCode();
    }

    private void accData(SupervisorPerformanceDetailNumberVO accVo, SupervisorPerformanceDetailNumberVO vo) {
        if (vo == null || accVo == null){
            return;
        }
        for (Field field : accVo.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            try {
                if (field.getType().equals(BigDecimal.class)) {
                    Object o = field.get(accVo);
                    if (o == null) {
                        field.set(accVo, ((BigDecimal) field.get(vo)).setScale(2, BigDecimal.ROUND_HALF_UP));
                    }else{
                        field.set(accVo, ((BigDecimal) o).add((BigDecimal) field.get(vo)).setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                }else if (field.getType().equals(Integer.class)){
                    Object o = field.get(accVo);
                    if (o == null) {
                        field.set(accVo, (Integer) field.get(vo));
                    }else{
                        field.set(accVo, ((Integer) o) + (Integer) field.get(vo));
                    }
                }
            }catch (Exception e){
                throw new RuntimeException(e);
            }
        }
    }

    private void zeroIfNull(Object vo) {
        if (vo == null){
            return;
        }
        for (Field field : vo.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            try {
                if (field.getType().equals(BigDecimal.class)) {
                    Object o = field.get(vo);
                    if (o == null) {
                        field.set(vo, BigDecimal.ZERO);
                    }else{
                        field.set(vo, ((BigDecimal) o).setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                }else if (field.getType().equals(Integer.class)){
                    Object o = field.get(vo);
                    if (o == null) {
                        field.set(vo, 0);
                    }
                }else if (field.getType().equals(String.class)){
                    Object o = field.get(vo);
                    if (o == null) {
                        field.set(vo, "");
                    }
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    private BigDecimal getValue() {
        return BigDecimal.valueOf(Math.random()).multiply(new BigDecimal(getRandomZero())).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    private String getRandomZero(){
        int exponent = ThreadLocalRandom.current().nextInt(0, 8);
        return "1" + String.join("", Collections.nCopies(exponent, "0"));
    }

    private List<SimpleNodeVO> getInstList() {
        List<SimpleNodeVO> list = new ArrayList<>();
        for (int i = 1; i < 4; i++) {
            SimpleNodeVO simpleNodeVO = new SimpleNodeVO();
            simpleNodeVO.setCode("P0000"+i);
            simpleNodeVO.setName("机构"+i);
            list.add(simpleNodeVO);
        }
        return list;
    }

    private List<TreeNodeVO> getTeamList(String partnerInstCode) {
        if (!StringUtils.isEmpty(partnerInstCode)){
            List<TreeNodeVO> list = new ArrayList<>();
            for (int i = 1; i < 2; i++) {
                TreeNodeVO simpleNodeVO = new TreeNodeVO();
                simpleNodeVO.setCode("T0000"+i);
                simpleNodeVO.setName("团队"+i);
                list.add(simpleNodeVO);
            }
            return list;
        }
        List<TreeNodeVO> list = new ArrayList<>();
        for (int i = 1; i < 4; i++) {
            TreeNodeVO simpleNodeVO = new TreeNodeVO();
            simpleNodeVO.setCode("T0000"+i);
            simpleNodeVO.setName("团队"+i);
            list.add(simpleNodeVO);
        }
        return list;
    }

    /**
     * 查询考核对应的基本法
     * @param request   请求参数实体
     * @return          基本发集合
     */
    private List<BasicLawInfoVO> queryBasicLawList(SupervisorPerformanceRequest request) {
        // 获取当前日期
        LocalDate now = LocalDate.now();
        // 定义格式化器，格式为 yyyy-MM
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        // 格式化日期为 yyyy-MM
        String yearMonth = now.format(formatter);
        //根据获取到的日期进行当前机构正在使用的基本法
        List<BasicLawInfoVO> basicLawInfoVOS = assessmentDao.queryBasicLawList(request.getInstCodeList(), request.getTeamCode(), yearMonth);
        if(CollUtil.isNotEmpty(basicLawInfoVOS)){
            return basicLawInfoVOS;
        }else {
            return new ArrayList<>();
        }
    }

}

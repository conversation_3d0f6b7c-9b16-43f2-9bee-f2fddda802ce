package com.hqins.agent.org.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.hqins.agent.org.cache.CacheService;
import com.hqins.agent.org.constants.AppConsts;
import com.hqins.agent.org.dao.converter.PartnerConverter;
import com.hqins.agent.org.dao.converter.TbempEmployeeConvert;
import com.hqins.agent.org.dao.entity.exms.Tbemp;
import com.hqins.agent.org.dao.entity.iips.Tbepartner;
import com.hqins.agent.org.dao.entity.org.MqMonitorEmployee;
import com.hqins.agent.org.dao.entity.org.SupervisorEmployee;
import com.hqins.agent.org.dao.mapper.exms.TbempMapper;
import com.hqins.agent.org.dao.mapper.iips.BaseInstMapper;
import com.hqins.agent.org.dao.mapper.org.MqMonitorEmployeeMapper;
import com.hqins.agent.org.dao.mapper.org.SupervisorEmployeeMapper;
import com.hqins.agent.org.excel.CellWriteWidthConfig;
import com.hqins.agent.org.excel.PartnerEmployeeExcel;
import com.hqins.agent.org.excel.PartnerEmployeeExcelAssembler;
import com.hqins.agent.org.model.enums.TeamLevel;
import com.hqins.agent.org.model.request.EmployeeQueryRequest;
import com.hqins.agent.org.model.request.PartnerEmployeeRequest;
import com.hqins.agent.org.model.vo.*;
import com.hqins.agent.org.rpc.client.UmClient;
import com.hqins.agent.org.rpc.client.entity.PartnerEmployeeUpdate;
import com.hqins.agent.org.service.*;
import com.hqins.common.base.enums.AgentOrgType;
import com.hqins.common.base.errors.ApiException;
import com.hqins.common.base.errors.ErrorCode;
import com.hqins.common.base.page.PageInfo;
import com.hqins.common.base.utils.AssertUtil;
import com.hqins.common.helper.BeanCopier;
import com.hqins.common.utils.JsonUtil;
import com.hqins.common.utils.PageUtil;
import com.hqins.common.utils.StringUtil;
import com.hqins.file.service.api.FileApi;
import com.hqins.file.service.model.request.FileBase64Request;
import com.hqins.file.service.model.vo.FileGetUrlsVO;
import com.hqins.um.model.dto.AccountInfoDTO;
import lombok.extern.slf4j.Slf4j;
import one.util.streamex.StreamEx;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Luo
 * @date 2021/5/7
 * @Description
 */
@Service
@Slf4j
public class PartnerEmployeeServiceImpl implements PartnerEmployeeService {

    public static final String PARTNER_EMPLOYEE_LIST_EXCEL_TAG = "partner_employee_list_excel_tag_";

    private final TbempMapper tbempMapper;
    private final DataAccessService dataAccessService;
    private final CacheService cacheService;
    private final PartnerTeamService partnerTeamService;
    private final UmClient umClient;
    private final MqMonitorEmployeeMapper mqMonitorEmployeeMapper;
    private final PartnerEmployeeExcelAssembler partnerEmployeeExcelAssembler;
    private final EmployeeExternalService employeeExternalService;
    private final TbempEmployeeConvert tbempEmployeeConvert;
    private final SupervisorEmployeeMapper supervisorEmployeeMapper;
    private final SupervisorEmployeeService supervisorEmployeeService;
    private final FileApi fileApi;
    private final BaseInstMapper baseInstMapper;

    @Lazy
    @Autowired
    private EmployeeService employeeService;

    public PartnerEmployeeServiceImpl(TbempMapper tbempMapper, DataAccessService dataAccessService, CacheService cacheService, PartnerTeamService partnerTeamService, UmClient umClient, MqMonitorEmployeeMapper mqMonitorEmployeeMapper, PartnerEmployeeExcelAssembler partnerEmployeeExcelAssembler, EmployeeExternalService employeeExternalService, TbempEmployeeConvert tbempEmployeeConvert, SupervisorEmployeeMapper supervisorEmployeeMapper, SupervisorEmployeeService supervisorEmployeeService, FileApi fileApi, BaseInstMapper baseInstMapper) {
        this.tbempMapper = tbempMapper;
        this.dataAccessService = dataAccessService;
        this.cacheService = cacheService;
        this.partnerTeamService = partnerTeamService;
        this.umClient = umClient;
        this.mqMonitorEmployeeMapper = mqMonitorEmployeeMapper;
        this.partnerEmployeeExcelAssembler = partnerEmployeeExcelAssembler;
        this.employeeExternalService = employeeExternalService;
        this.tbempEmployeeConvert = tbempEmployeeConvert;
        this.supervisorEmployeeMapper = supervisorEmployeeMapper;
        this.supervisorEmployeeService = supervisorEmployeeService;
        this.fileApi = fileApi;
        this.baseInstMapper = baseInstMapper;
    }

    @Override
    public PageInfo<PartnerEmployeeVO> listMy(PartnerEmployeeRequest queryRequest) {
        log.info("PartnerEmployeeServiceImpl_listMy,queryRequest:{}", JsonUtil.toJSON(queryRequest));
        //转换数据结构
        return PageUtil.convert(listPage(queryRequest), PartnerConverter::mapToPartnerEmployeeVO);
    }

    @Override
    public PageInfo<AccountVO> listMyAccountVO(PartnerEmployeeRequest queryRequest) {

        //转换数据结构
        return PageUtil.convert(listPage(queryRequest), PartnerConverter::mapToAccountVO);
    }

    private Page<Map<String, Object>> listPage(PartnerEmployeeRequest queryRequest) {
        //获取数据权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        List<String> orgCodes = handleOrgCodes(queryRequest, myDataAccess);
        //List<String> orgCodes = null;
        log.debug("获取到orgCodes:{}", JsonUtil.toJSON(orgCodes));

        //按名称查出团队，以及其下面的子团队编码
        Set<String> saleTeamIncodes = handleTeamCodes(queryRequest, myDataAccess);
        //Set<String> saleTeamIncodes = null;
        log.debug("获取到orgCodes:{}", JsonUtil.toJSON(orgCodes));

        //获取经过筛选后的合伙人编码
        Set<String> topCodes = handleTopCodes(queryRequest);
//        Set<String> topCodes = null;
        log.debug("获取到orgCodes:{}", JsonUtil.toJSON(orgCodes));

        //计算起始数据
        queryRequest.setStart((queryRequest.getCurrent() - 1) * queryRequest.getSize());
        return tbempMapper.listPage(queryRequest, topCodes, saleTeamIncodes, orgCodes, new Page<>(queryRequest.getCurrent(), queryRequest.getSize()));
    }

    public Set<String> handleTopCodes(PartnerEmployeeRequest queryRequest) {
        Set<String> topCodes = new HashSet<>();
        if (!StringUtil.isBlank(queryRequest.getPartnerName())) {
            //合伙人名称不为空则去数据库里查出交集
            topCodes = StreamEx.of(cacheService.getAllTbepartners()).filter(t -> AppConsts.CPTYPE_PARTNER.equals(t.getCptype())
                    && t.getComanystatus() == 1
                    && t.getCompanyname().contains(queryRequest.getPartnerName())
            ).map(Tbepartner::getCompanycode).toSet();
        }
        return topCodes;
    }

    private List<String> handleOrgCodes(PartnerEmployeeRequest queryRequest, MyDataAccessVO myDataAccess) {
        List<String> orgCodes = new ArrayList<>();
        if (StringUtil.isNotEmpty(queryRequest.getOrgName()) || StringUtil.isNotEmpty(queryRequest.getOrgCode())) {
            //按编码和名称查出机构，以及其下面的子机构
            orgCodes = cacheService.selectAllChildCodes(queryRequest.getOrgCode(), queryRequest.getOrgName(), myDataAccess.getContainsSuperAdmin(), myDataAccess.getPartnerOrgCodes());
        }
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            if (CollectionUtils.isEmpty(orgCodes)) {
                orgCodes = new ArrayList(myDataAccess.getPartnerOrgCodes());
            }
        }
        return orgCodes;
    }

    public Set<String> handleTeamCodes(PartnerEmployeeRequest queryRequest, MyDataAccessVO myDataAccess) {
        Set<String> saleTeamIncodes = new HashSet<>();
        if (!StringUtil.isBlank(queryRequest.getTeamName())) {
            saleTeamIncodes = partnerTeamService.selectAllChildCodes(null, queryRequest.getTeamName(), myDataAccess);
        }
        return saleTeamIncodes;
    }


    @Override
    public List<SimpleEmployeeVO> listEmployeeSimple(EmployeeQueryRequest queryRequest) {
        //获取数据权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();
        List<String> orgCodes = null;
        if (!StringUtil.isBlank(queryRequest.getOrgCode())) {
            //按编码和名称查出机构，以及其下面的子机构
            orgCodes = cacheService.selectAllChildCodes(queryRequest.getOrgCode(), null, myDataAccess.getContainsSuperAdmin(), myDataAccess.getPartnerOrgCodes());
            if (orgCodes.isEmpty()) {
                //如果给了条件但是搜不到子机构，直接返回空即可
                return new ArrayList<>();
            }
        }
        if (Boolean.FALSE.equals(myDataAccess.getContainsSuperAdmin())) {
            if (orgCodes == null) {
                orgCodes = new ArrayList(myDataAccess.getPartnerOrgCodes());
            }
            if (orgCodes.isEmpty()) {
                //不是超级管理员,必须有有权限的机构
                return new ArrayList<>();
            }
        }
        //按名称查出团队，以及其下面的子团队编码
        Set<String> saleTeamIncodes = null;
        if (!StringUtil.isBlank(queryRequest.getTeamCode())) {
            saleTeamIncodes = partnerTeamService.selectAllChildCodes(queryRequest.getTeamCode(), null, myDataAccess);
            if (saleTeamIncodes.isEmpty()) {
                //如果给了条件但是搜不到子机构，直接返回空即可
                return new ArrayList<>();
            }
        }

        //获取经过筛选后的合伙人编码
        Set<String> topCodes = new HashSet<>();
        if (!StringUtil.isBlank(queryRequest.getTopCode())) {
            topCodes.add(queryRequest.getTopCode());
        }
        //根据经过筛选后的合伙人编码，团队内码,获取有权限的团队
        //计算起始数据
        queryRequest.setStart((queryRequest.getCurrent() - 1) * queryRequest.getSize());
        List<Map<String, Object>> maps = tbempMapper.listSimple(queryRequest, topCodes, saleTeamIncodes, orgCodes);
        return BeanCopier.copyList(maps, PartnerConverter::mapToSimpleEmployeeVO);
    }

    @Override
    public EmployeeVO getEmployeeVO(String employeeCode) {
        List<Map<String, Object>> lst = tbempMapper.list(employeeCode);
        if (lst.isEmpty()){
            EmployeeVO employeeVO = supervisorEmployeeService.getSupervisorEmployee(employeeCode);
            AssertUtil.isTrue(employeeVO != null, new ApiException(ErrorCode.BAD_REQUEST, "销售员不存在"));
            return employeeVO;
        }
        Map<String,Object> expMap = tbempMapper.getEmployeeExp(employeeCode);
        EmployeeVO employeeVO = PartnerConverter.mapToEmployeeVO(lst.get(0));
        employeeVO.setOrgType(AgentOrgType.PARTNER);
        if (expMap != null){
            employeeVO.setRank((String)expMap.get("rankName"));
            String teamLevelDb = (String)expMap.get("teamLevel");
            if ("03".equals(teamLevelDb)) {
                employeeVO.setTeamLevel(TeamLevel.AREA.name());
            } else if ("02".equals(teamLevelDb)) {
                employeeVO.setTeamLevel(TeamLevel.DEPT.name());
            } else {
                employeeVO.setTeamLevel(TeamLevel.TEAM.name());
            }
            String contractCode = (String)expMap.get("contractCode");
            employeeVO.setContractCode(contractCode);
        }
        //根据编码查出合伙人名称
        Tbepartner tb = cacheService.getAllTbepartnersMap().get(employeeVO.getTopCode());
        AssertUtil.isTrue(tb != null && tb.getComanystatus() == 1, new ApiException(ErrorCode.BAD_REQUEST, "合伙人不存在"));
        assert tb != null;
        employeeVO.setTopName(tb.getCompanyname());
        if(!StringUtil.isBlank(employeeVO.getOrgCode())){
            //获取合伙人三级管理机构
            Map<String,String> mangerMap = baseInstMapper.queryThreeMangerCodeByOrgCode(employeeVO.getOrgCode());
            if (mangerMap != null && !mangerMap.isEmpty()){
                employeeVO.setThreeMangerCode(mangerMap.get("threeMangerCode"));
                employeeVO.setThreeMangerName(mangerMap.get("threeMangerName"));
            }
        }
        return employeeVO;
    }

    @Override
    public EmployeeVO getEmployeeVOByMobile(String mobile){
        List<Map<String, Object>> lst = tbempMapper.queryByMobile(mobile);
        if (lst.isEmpty()){
            return supervisorEmployeeService.getSupervisorEmployeeByMobile(mobile);
        }
        AssertUtil.isTrue(!lst.isEmpty(), new ApiException(ErrorCode.BAD_REQUEST, "销售员不存在"));
        EmployeeVO employeeVO = PartnerConverter.mapToEmployeeVO(lst.get(0));
        employeeVO.setOrgType(AgentOrgType.PARTNER);
        //根据编码查出合伙人名称
        Tbepartner tb = cacheService.getAllTbepartnersMap().get(employeeVO.getTopCode());
        AssertUtil.isTrue(tb != null && tb.getComanystatus() == 1, new ApiException(ErrorCode.BAD_REQUEST, "合伙人不存在"));
        assert tb != null;
        employeeVO.setTopName(tb.getCompanyname());

        return employeeVO;
    }

    @Override
    public MyEmployeeVO getMyEmployeeVO(String employeeCode) {
        List<Map<String, Object>> lst = tbempMapper.list(employeeCode);
        SupervisorEmployee supervisorEmployee = null;
        if (lst.isEmpty()){
            supervisorEmployee = supervisorEmployeeMapper.selectOne(Wrappers.lambdaQuery(SupervisorEmployee.class)
                    .eq(SupervisorEmployee::getEmployeeCode, employeeCode));
            AssertUtil.isTrue(null != supervisorEmployee, new ApiException("销售员不存在"));
            MyEmployeeVO vo = BeanCopier.copyObject(supervisorEmployee, MyEmployeeVO.class);
            vo.setCode(supervisorEmployee.getEmployeeCode());
            return vo;
        }
        AssertUtil.isTrue(!lst.isEmpty() , new ApiException("销售员不存在"));
        return PartnerConverter.mapToMyEmployeeVO(lst.get(0));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String changeMobile(String message) {
        String synResult = "";

        //1.新销管系统调用合伙人下代理人手机变更
        PartnerEmployeeUpdate partnerEmployeeUpdate = JSONObject.parseObject(message, PartnerEmployeeUpdate.class);
        log.info("解析出的合伙人下代理人信息 partnerEmployeeUpdate:{}", JSONUtil.toJsonStr(partnerEmployeeUpdate));

        if (null == partnerEmployeeUpdate || StringUtil.isBlank(partnerEmployeeUpdate.getEmployCode())) {
            return synResult;
        }

        //2.调用UM系统,修改手机号
        List<AccountInfoDTO> agentUserInfos = umClient.getAgentsBatch(partnerEmployeeUpdate.getEmployCode());
        log.info("根据传入代理人code查询agentUserInfo信息:{}", JSONUtil.toJsonStr(agentUserInfos));
        if (CollectionUtils.isEmpty(agentUserInfos)) {
            return synResult;
        }

        try {
            umClient.agentUserPhoneUpdate(agentUserInfos.get(0).getUserId(), partnerEmployeeUpdate.getBeforePhone(), partnerEmployeeUpdate.getAfterPhone());
        } catch (ApiException exception) {
            synResult = exception.getMessage();
        }

        //3.更新mq_monitor_employee表中代理人手机号信息
        MqMonitorEmployee mqMonitorEmployee = mqMonitorEmployeeMapper.selectByCode(partnerEmployeeUpdate.getEmployCode());
        if (null == mqMonitorEmployee) {
            return synResult;
        }
        mqMonitorEmployeeMapper.updateMobileByCode(partnerEmployeeUpdate.getEmployCode(), partnerEmployeeUpdate.getAfterPhone());

        return synResult;

    }

    @Override
    public String exportData(PartnerEmployeeRequest queryRequest) {
        //获取数据权限
        MyDataAccessVO myDataAccess = dataAccessService.getMyDataAccess();

        List<String> orgCodes = handleOrgCodes(queryRequest, myDataAccess);

        Set<String> teamCodes = handleTeamCodes(queryRequest, myDataAccess);

        Set<String> topCodes = handleTopCodes(queryRequest);

        List<EmployeeVO> partnerEmployeeVOList = BeanCopier.copyList(tbempMapper.listNoPage(queryRequest, topCodes, teamCodes, orgCodes), PartnerConverter::mapToEmployeeVO);

        List<PartnerEmployeeExcel> resultList = new ArrayList<>();
        partnerEmployeeVOList.forEach(channelEmployee -> resultList.add(partnerEmployeeExcelAssembler.assembler(channelEmployee)));

        byte[] bytes = null;
        //查询数据 生成报表
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            EasyExcel.write(bos, PartnerEmployeeExcel.class)
                    .registerWriteHandler(new CellWriteWidthConfig())
                    .sheet("合伙人下代理人员清单")
                    .doWrite(resultList);
            bytes = bos.toByteArray();
        } catch (IOException e) {
            log.error("export.error:{}", e.getMessage(), e);
        }
        // 上传阿里云
        FileGetUrlsVO fileGetUrlsVO = fileApi.upload4Base64(FileBase64Request
                .builder()
                .fileName("合伙人下代理人员清单"+ LocalDateTime.now()+ ".xls")
                .source("agent")
                .fileBase64String(Base64.encode(bytes))
                .build());
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(fileGetUrlsVO.getFileUrlList())) {
            return fileGetUrlsVO.getFileUrlList().get(0).getOuterUrl();
        }
        return "";
    }

    @Override
    public EmployeeVO getEmployeeVO(String employeeCode, String status) {
        List<Map<String, Object>> list = tbempMapper.selectByEmployeeCode(employeeCode, status);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        EmployeeVO employeeVO = PartnerConverter.mapToEmployeeVO(list.get(0));
        employeeVO.setOrgType(AgentOrgType.PARTNER);
        //根据编码查出合伙人名称
        Tbepartner tb = cacheService.getAllTbepartnersMap().get(employeeVO.getTopCode());
        employeeVO.setTopName(null != tb ? tb.getCompanyname() : null);
        return employeeVO;
    }

    @Override
    public List<EmployeeVO> getByEmployeeCodeList(List<String> employeeCodeList) {
        List<Tbemp> tbempList = tbempMapper.getByEmployeeCodeList(employeeCodeList);
        if (CollectionUtils.isEmpty(tbempList)) {
            return Lists.newArrayList();
        }
        return tbempEmployeeConvert.convert(tbempList);
    }

    @Override
    public List<SaleTeamVO> listSaleTeamByOrgCode(String orgCode,Boolean ifpFlag){
        try{
            if (StringUtil.isEmpty(orgCode)){
                return new ArrayList<>(0);
            }
            List<SaleTeamVO> teamList = partnerTeamService.listTopSaleTeam(orgCode);
            //由于listTopSaleTeam不止一处调用，故将后续新增的ifp的过滤放在此处
            if (null != ifpFlag){
                if (ifpFlag){
                    teamList = teamList.stream().filter(item -> AppConsts.STR_IFP.equals(item.getTeamType())).collect(Collectors.toList());
                } else {
                    teamList = teamList.stream().filter(item -> !AppConsts.STR_IFP.equals(item.getTeamType())).collect(Collectors.toList());
                }
            }
            return teamList;
        }catch (Exception e){
            log.error("listSaleTeamByEmployeeCode_error",e);
        }
        return new ArrayList<>(0);
    }

    @Override
    public EmployeeVO getEmpAreaCode(String empCode) {
        return tbempMapper.queryEmpAreaCode(empCode);
    }

    @Override
    public EmployeeVO isTeamLeader(EmployeeVO employeeVO) {
        String supervisorRoleType = employeeVO.getSupervisorRoleType();

        Boolean leaderFlag = Boolean.FALSE;
        String employeeCode = employeeVO.getCode();

        if (StringUtils.isNotEmpty(supervisorRoleType)) {
            //督导账号
            leaderFlag = "PuTong".equals(supervisorRoleType) ? Boolean.FALSE : Boolean.TRUE;
        } else {
            //查询ORG数据获取代理人数据
            EmployeeOrgVO employeeOrgVO = employeeService.queryEmployeeOrgInfo(employeeCode);
            if (employeeCode.equals(employeeOrgVO.getBusinessAreaLeaderCode()) ||
                    employeeCode.equals(employeeOrgVO.getBusinessDeptLeaderCode()) ||
                    employeeCode.equals(employeeOrgVO.getBusinessTeamLeaderCode())
            ) {
                leaderFlag = Boolean.TRUE;
            }
        }
        employeeVO.setIsTeamLeader(leaderFlag);
        return employeeVO;
    }

    @Override
    public List<PartnerEmployeeVO> listMyApi(PartnerEmployeeRequest queryRequest) {
        PageInfo<PartnerEmployeeVO> partnerEmployeeVOPageInfo = listMy(queryRequest);
        if (CollectionUtils.isNotEmpty(partnerEmployeeVOPageInfo.getRecords())){
            return  partnerEmployeeVOPageInfo.getRecords();
        }
        return Collections.emptyList();
    }
}

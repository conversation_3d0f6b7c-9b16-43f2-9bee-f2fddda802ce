package com.hqins.agent.org.model.request;

import com.hqins.common.base.page.PageQueryRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@ApiModel("渠道商机构网点分页查询请求")
@Getter
@SuperBuilder
public class ChannelInstOutletsQueryRequest extends PageQueryRequest {

    @ApiModelProperty("机构网点编码")
    private String channelInstCode;

    @ApiModelProperty("机构网点名称")
    private String channelInstName;
}

package com.hqins.agent.org.model.api;

import com.hqins.agent.org.model.vo.PartnerEmployeeVO;
import com.hqins.common.base.annotations.CollectionElement;
import com.hqins.common.base.constants.Strings;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10 15:55
 */
@FeignClient(name = "hqins-agent-org")
public interface PartnerEmployeeApi {
    @ApiOperation("分页查询合伙人销售员")
    @GetMapping("/agent-org/partner/employees/listMyApi")
    @CollectionElement(targetClass = PartnerEmployeeVO.class)
    List<PartnerEmployeeVO> listMyApi(
            @ApiParam("归属销售机构代码") @RequestParam(value = "orgCode", required = false) String orgCode,
            @ApiParam("代理人状态 SERVING-有效 INVALID-无效 null查全部") @RequestParam(value = "status", required = false) String status,
            @ApiParam(Strings.CURRENT_PAGE_MSG) @RequestParam("current") long current,
            @ApiParam(Strings.SIZE_PAGE_MSG) @RequestParam("size") long size);
}

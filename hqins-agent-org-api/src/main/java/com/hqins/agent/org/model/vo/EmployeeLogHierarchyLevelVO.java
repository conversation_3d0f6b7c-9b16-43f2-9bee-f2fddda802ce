package com.hqins.agent.org.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 人员工作日志查询结果层级列表VO类.
 *
 * <AUTHOR> MXH
 * @create 2025/6/3 15:42
 */
@Data
@ApiModel("人员工作日志查询结果层级列表VO类")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EmployeeLogHierarchyLevelVO implements Serializable {

    @ApiModelProperty("层级名称")
    private String name;

    @ApiModelProperty("层级代码")
    private String code;

    @ApiModelProperty("层级级别（例如：机构、团队）")
    private String level;

    @ApiModelProperty("员工列表")
    private List<EmployeeVO> employeeList;

    @ApiModelProperty("下一级层级列表")
    private List<EmployeeLogHierarchyLevelVO> subLevels;
}

package com.hqins.agent.org.model.enums;

import com.hqins.common.utils.StringUtil;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> MXH
 * @create 2025/6/30 11:29
 */
public enum ChannelInstStatusEnum {

    NOT_IN_EFFECT("0", "未生效"),
    UNDER_REVIEW("3", "审核中"),
    EFFECTIVE("1", "已生效"),
    OVERDUE("", "过期"),
    RENEWAL("", "续签中"),
    RETURNING("", "退回中"),
    ;

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }

    private final String code;

    private final String label;

    ChannelInstStatusEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public static String getLabelByCode(String code) {
        if (StringUtil.isEmpty(code)) {
            return "";
        }
        for (ChannelInstStatusEnum feeItem : ChannelInstStatusEnum.values()) {
            if (feeItem.getCode().equals(code)) {
                return feeItem.getLabel();
            }
        }
        return "";
    }
}

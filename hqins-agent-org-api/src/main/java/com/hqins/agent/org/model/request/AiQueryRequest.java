package com.hqins.agent.org.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@ApiModel("AI查询请求服务")
@Data
@SuperBuilder
public class AiQueryRequest {


    @ApiModelProperty("API请求KEY")
    private String apiKey;

    @ApiModelProperty("查询SQL")
    private String sql;



}

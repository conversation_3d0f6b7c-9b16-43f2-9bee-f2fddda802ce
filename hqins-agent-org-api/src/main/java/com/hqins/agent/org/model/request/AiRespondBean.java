/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.hqins.agent.org.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@ApiModel(description = "响应对象")
public class AiRespondBean {
    @ApiModelProperty(value = "服务执行状态(true:服务执行成功,false:服务执行识别)",required=true,example="true")
    public Boolean FLAG=false;
    
    @ApiModelProperty(value = "服务执行结果信息",required=true,example="服务执行完成")
    public String MESSAGE="";
    
    @ApiModelProperty(value = "服务执行异常信息")    
    public String EXCEPTION;

    @ApiModelProperty(value = "保单变更核心返回信息")
    public List  list=new ArrayList();
    
   
    
   public static AiRespondBean build()
    {
        return new AiRespondBean();
    }
}

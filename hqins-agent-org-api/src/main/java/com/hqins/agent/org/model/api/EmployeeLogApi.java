package com.hqins.agent.org.model.api;

import com.hqins.agent.org.model.vo.EmployeeLogResultVO;
import com.hqins.common.base.ApiResult;
import com.hqins.common.base.annotations.CollectionElement;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> MXH
 * @create 2025/6/9 15:10
 */
@FeignClient(name = "hqins-agent-org")
public interface EmployeeLogApi {
    @ApiOperation("查阅范围")
    @GetMapping("/agent-org/employeeLog/query")
    @CollectionElement(targetClass = EmployeeLogResultVO.class)
    EmployeeLogResultVO employeeLogQuery(@ApiParam("代理人工号") @RequestParam(value = "employeeCode", required = true) String employeeCode);

    @ApiOperation("批阅范围")
    @GetMapping("/agent-org/employeeLog/audit")
    @CollectionElement(targetClass = EmployeeLogResultVO.class)
    EmployeeLogResultVO employeeLogAudit(@ApiParam("代理人工号") @RequestParam(value = "employeeCode", required = true) String employeeCode);
}
